// app/services/businessConsoleService.ts

import dayjs from "dayjs";
import { API_BASE_URL, apiRequest } from "@utils/api";
import {
  BuyerSummary,
  OrderItem,
  SellerConsoleDataResponse,
} from "~/types/api/businessConsoleService/SellerConsoleDataResponse";
import dotenv from "dotenv";
import { DashboardGroupBy } from "~/types/home";
import { BuyerSummaryDetailsResponseItem } from "~/types/api/businessConsoleService/BuyerSummaryDetailsResponseItem";
import { BuyerDetailsResponse } from "~/types/api/businessConsoleService/BuyerDetailsResponse";
import {
  ContractPrice,
  OrderSummary,
  SelectedItemSummary,
} from "~/types/api/businessConsoleService/BuyerOrdersResponse";
import {
  CreateAreaRequest,
  CreateAreaResponse,
  SellerArea,
  UpdateAreaRequest,
} from "~/types/api/businessConsoleService/Areas";
import { ApiResponse } from "~/types/api/Api";
import { SaveWABTokenRequest } from "~/types/api/businessConsoleService/token";
import {
  NetworkAreas,
  NetworkBanner,
  NetworkBuyer,
  NetworkConfig,
  Networks,
} from "~/types/api/businessConsoleService/Network";
import {
  MasterLocalities,
  MyAddonData,
  MyAddOnGroupAddOn,
  MyAddonGroupData,
  MyVariationData,
  networkAgents,
  Seller,
  SellerConfig,
  smSellerArea,
  StateAndDistricts,
  Suppliers,
} from "~/types/api/businessConsoleService/SellerManagement";
import { request } from "http";
import { AddOnGroup, SellerItem } from "~/types/api/businessConsoleService/MyItemList";
import { MasterItemCategories } from "~/types/api/businessConsoleService/MasterItemCategory";
import { NetWorkDetails } from "~/types/api/businessConsoleService/netWorkinfo";

dotenv.config();

const useMock = process.env.USE_MOCK === "true";

export async function getDashboardDataFor(
  userId: number,
  date: Date,
  groupBy: DashboardGroupBy,
  request?: Request
): Promise<ApiResponse<SellerConsoleDataResponse[]>> {
  if (useMock) {
    return { data: [], statusCode: 200 };
    /*const {
            mockDashboardDataDaily,
            mockDashboardDataWeekly,
            mockDashboardDataMonthly
        } = await import("./__mocks__/businessConsole.mockData");

        switch (groupBy) {
            case DashboardGroupBy.Daily:
                return mockDashboardDataDaily;
            case DashboardGroupBy.Weekly:
                return mockDashboardDataWeekly;
            case DashboardGroupBy.Monthly:
                return mockDashboardDataMonthly;
            default:
                throw new Error('Invalid group by');
        }*/
  } else {
    const queryDate = dayjs(date).format("YYYY-MM-DD");
    const summaryType =
      {
        [DashboardGroupBy.Daily]: "daily",
        [DashboardGroupBy.Weekly]: "weekly",
        [DashboardGroupBy.Monthly]: "monthly",
      }[groupBy] || "daily";

    const response = await apiRequest<SellerConsoleDataResponse[]>(
      `${API_BASE_URL}/bc/seller/console/summary?date=${queryDate}&period=${summaryType}`,
      "GET",
      undefined,
      {},
      true,
      request
    );

    if (response) {
      return response;
    } else {
      throw new Error("Failed to fetch dashboard data");
    }
  }
}

export async function getAddonsGroupsMap(
  sellerId: number,
  addonId?: number,
  pageNo?: number,
  size?: number,
  matchBy?: string | undefined,
  request?: Request
): Promise<ApiResponse<MyAddOnGroupAddOn[]>> {
  if (useMock) {
    return { data: [], statusCode: 200 };
  }

  // Basic validation
  if (!sellerId || typeof sellerId !== "number" || sellerId <= 0) {
    throw new Error("Invalid sellerId provided");
  }

  if (pageNo !== undefined && (typeof pageNo !== "number" || pageNo < 0)) {
    throw new Error("Invalid pageNo provided");
  }

  if (size !== undefined && (typeof size !== "number" || size <= 0)) {
    throw new Error("Invalid size provided");
  }

  if (matchBy !== undefined && typeof matchBy !== "string" && matchBy === "") {
    throw new Error("matchBy must be a string if provided");
  }

  try {
    const queryParams = new URLSearchParams();
    if (pageNo !== undefined) queryParams.append("pageNo", pageNo.toString());
    if (size !== undefined) queryParams.append("size", size.toString());
    if (matchBy && matchBy.trim().length > 0) {
      queryParams.append("matchBy", matchBy.trim());
    }
    const url = `${API_BASE_URL}/bc/catalog/seller/${sellerId}/myaddongroups/${addonId}/addonmaps?${queryParams.toString()}`;
    const response = await apiRequest<MyAddOnGroupAddOn[]>(
      url,
      "GET",
      undefined,
      {},
      true,
      request
    );

    if (response) {
      return response;
    } else {
      throw new Error("Failed to fetch addons groups");
    }
  } catch (error: any) {
    // Log error or handle it accordingly
    throw new Error(`Error in myaddongroupmap: ${error.message}`);
  }
}


export async function getSelectedVarAddonGroup(
  sellerId: number,
  varitionId?: number,
  pageNo?: number,
  size?: number,
  matchBy?: string | undefined,
  request?: Request
): Promise<ApiResponse<AddOnGroup[]>> {
  if (useMock) {
    return { data: [], statusCode: 200 };
  }

  // Basic validation
  if (!sellerId || typeof sellerId !== "number" || sellerId <= 0) {
    throw new Error("Invalid sellerId provided");
  }

  if (pageNo !== undefined && (typeof pageNo !== "number" || pageNo < 0)) {
    throw new Error("Invalid pageNo provided");
  }

  if (size !== undefined && (typeof size !== "number" || size <= 0)) {
    throw new Error("Invalid size provided");
  }

  if (matchBy !== undefined && typeof matchBy !== "string" && matchBy === "") {
    throw new Error("matchBy must be a string if provided");
  }

  try {
    const queryParams = new URLSearchParams();
    if (pageNo !== undefined) queryParams.append("pageNo", pageNo.toString());
    if (size !== undefined) queryParams.append("size", size.toString());
    if (matchBy && matchBy.trim().length > 0) {
      queryParams.append("matchBy", matchBy.trim());
    }
    const url = `${API_BASE_URL}/bc/catalog/seller/${sellerId}/variation/${varitionId}/addongroups?${queryParams.toString()}`;
    const response = await apiRequest<AddOnGroup[]>(
      url,
      "GET",
      undefined,
      {},
      true,
      request
    );

    if (response) {
      return response;
    } else {
      throw new Error("Failed to fetch getSelectedVarAddonGroup");
    }
  } catch (error: any) {
    // Log error or handle it accordingly
    throw new Error(`Error in getSelectedVarAddonGroup: ${error.message}`);
  }
}


export async function getAddonsGroups(
  sellerId: number,
  pageNo?: number,
  size?: number,
  matchBy?: string | undefined,
  request?: Request
): Promise<ApiResponse<MyAddonGroupData[]>> {
  if (useMock) {
    return { data: [], statusCode: 200 };
  }

  // Basic validation
  if (!sellerId || typeof sellerId !== "number" || sellerId <= 0) {
    throw new Error("Invalid sellerId provided");
  }

  if (pageNo !== undefined && (typeof pageNo !== "number" || pageNo < 0)) {
    throw new Error("Invalid pageNo provided");
  }

  if (size !== undefined && (typeof size !== "number" || size <= 0)) {
    throw new Error("Invalid size provided");
  }

  if (matchBy !== undefined && typeof matchBy !== "string" && matchBy === "") {
    throw new Error("matchBy must be a string if provided");
  }

  try {
    const queryParams = new URLSearchParams();
    if (pageNo !== undefined) queryParams.append("pageNo", pageNo.toString());
    if (size !== undefined) queryParams.append("size", size.toString());
    if (matchBy && matchBy.trim().length > 0) {
      queryParams.append("matchBy", matchBy.trim());
    }
    const url = `${API_BASE_URL}/bc/catalog/seller/${sellerId}/myaddongroups?${queryParams.toString()}`;
    const response = await apiRequest<MyAddonGroupData[]>(
      url,
      "GET",
      undefined,
      {},
      true,
      request
    );

    if (response) {
      return response;
    } else {
      throw new Error("Failed to fetch addons groups");
    }
  } catch (error: any) {
    // Log error or handle it accordingly
    throw new Error(`Error in getAddonsGroups: ${error.message}`);
  }
}


export async function createAddonGroup(
  sellerId: number,
  updateData: MyAddonGroupData,
  request?: Request
): Promise<ApiResponse<MyAddonGroupData>> {

  const response = await apiRequest<MyAddonGroupData>(

    `${API_BASE_URL}/bc/catalog/seller/${sellerId}/myaddongroup`,
    "POST",
    updateData,
    {},
    true,
    request
  );

  if (response) {
    return response;
  } else {
    throw new Response("Failed to update addons group");
  }
}


export async function getAddons(
  sellerId: number,
  pageNo?: number,
  size?: number,
  matchBy?: string,
  request?: Request
): Promise<ApiResponse<any>> {
  if (useMock) {
    return { data: [], statusCode: 200 };
  }

  // Basic validation
  if (!sellerId || typeof sellerId !== "number" || sellerId <= 0) {
    throw new Error("Invalid sellerId provided");
  }

  if (pageNo !== undefined && (typeof pageNo !== "number" || pageNo < 0)) {
    throw new Error("Invalid pageNo provided");
  }

  if (size !== undefined && (typeof size !== "number" || size <= 0)) {
    throw new Error("Invalid size provided");
  }

  if (matchBy !== undefined && typeof matchBy !== "string") {
    throw new Error("matchBy must be a string if provided");
  }

  try {
    const queryParams = new URLSearchParams();
    if (pageNo !== undefined) queryParams.append("pageNo", pageNo.toString());
    if (size !== undefined) queryParams.append("size", size.toString());
    if (matchBy && matchBy.trim().length > 0) {
      queryParams.append("matchBy", matchBy.trim());
    }
    const url = `${API_BASE_URL}/bc/catalog/seller/${sellerId}/myaddons?${queryParams.toString()}`;

    const response = await apiRequest<any>(
      url,
      "GET",
      undefined,
      {},
      true,
      request
    );

    if (response) {
      return response;
    } else {
      throw new Error("Failed to fetch addons groups");
    }
  } catch (error: any) {
    // Log error or handle it accordingly
    throw new Error(`Error in getAddonsGroups: ${error.message}`);
  }
}

export async function createVariationAddonsGroup(
  sellerId: number,
  variationId: number,
  updateData: AddOnGroup,
  request?: Request
): Promise<ApiResponse<AddOnGroup>> {
  if (useMock) {
    return { data: null, statusCode: 200 };
  } else {
    const response = await apiRequest<AddOnGroup>(

      `${API_BASE_URL}/bc/catalog/seller/${sellerId}/variation/${variationId}/addongroup`,
      "POST",
      updateData,
      {},
      true,
      request
    );

    if (response) {
      return response;
    } else {
      throw new Response("Failed to update AddonMap group");
    }
  }
}
export async function createAddonMap(
  sellerId: number,
  updateData: MyAddOnGroupAddOn,
  request?: Request
): Promise<ApiResponse<MyAddOnGroupAddOn>> {
  if (useMock) {
    return { data: null, statusCode: 200 };
  } else {
    const response = await apiRequest<MyAddOnGroupAddOn>(

      `${API_BASE_URL}/bc/catalog/seller/${sellerId}/myaddonmap`,
      "POST",
      updateData,
      {},
      true,
      request
    );

    if (response) {
      return response;
    } else {
      throw new Response("Failed to update AddonMap group");
    }
  }
}

export async function createAddon(
  sellerId: number,
  updateData: MyAddonData,
  request?: Request
): Promise<ApiResponse<MyAddonData>> {
  if (useMock) {
    return { data: null, statusCode: 200 };
  } else {
    const response = await apiRequest<MyAddonData>(

      `${API_BASE_URL}/bc/catalog/seller/${sellerId}/myaddon`,
      "POST",
      updateData,
      {},
      true,
      request
    );

    if (response) {
      return response;
    } else {
      throw new Response("Failed to update addons group");
    }
  }
}
export async function deleteAddonGroup(
  sellerId: number,
  addonId: number,
  request?: Request
): Promise<ApiResponse<MyAddonGroupData>> {
  if (useMock) {
    return { data: null, statusCode: 200 };
  } else {
    const response = await apiRequest<MyAddonGroupData>(
      `${API_BASE_URL}/bc/catalog/seller/${sellerId}/myaddongroup/${addonId}`,
      "DELETE",
      undefined,
      {},
      true,
      request
    );
    if (response) {
      if (response.statusCode === 200) {
        return response;
      } else if (response.statusCode === 400) {
        throw new Response("Bad request: Invalid addon GID or seller ID");
      } else {
        throw new Response(`Unexpected response status: ${response.statusCode}`);
      }
    } else {
      throw new Response("Failed to delete AddonGroup: No response received");
    }
  }
}
export async function deleteAddon(
  sellerId: number,
  addonId: number,
  request?: Request
): Promise<ApiResponse<MyAddonData>> {
  if (useMock) {
    return { data: null, statusCode: 200 };
  } else {
    const response = await apiRequest<MyAddonData>(
      `${API_BASE_URL}/bc/catalog/seller/${sellerId}/myaddon/${addonId}`,
      "DELETE",
      undefined,
      {},
      true,
      request
    );

    if (response) {
      if (response.statusCode === 200) {
        return response;
      } else if (response.statusCode === 400) {
        throw new Response("Bad request: Invalid addon ID or seller ID");
      } else {
        throw new Response(`Unexpected response status: ${response.statusCode}`);
      }
    } else {
      throw new Response("Failed to delete addon: No response received");
    }
  }
}
export async function deleteAddonMap(
  sellerId: number,
  addonmapId: number,
  request?: Request
): Promise<ApiResponse<MyAddOnGroupAddOn>> {
  if (useMock) {
    return { data: null, statusCode: 200 };
  } else {
    const response = await apiRequest<MyAddOnGroupAddOn>(
      `${API_BASE_URL}/bc/catalog/seller/${sellerId}/myaddonmap/${addonmapId}`,
      "DELETE",
      undefined,
      {},
      true,
      request
    );

    if (response) {
      if (response.statusCode === 200) {
        return response;
      } else if (response.statusCode === 400) {
        throw new Response("Bad request: Invalid addon ID or seller ID");
      } else {
        throw new Response(`Unexpected response status: ${response.statusCode}`);
      }
    } else {
      throw new Response("Failed to delete AddonMap: No response received");
    }
  }
}
export async function deleteAddonVariation(
  sellerId: number,
  variationId: number,
  addongId: number,
  request?: Request
): Promise<ApiResponse<AddOnGroup>> {
  if (useMock) {
    return { data: null, statusCode: 200 };
  } else {
    const response = await apiRequest<AddOnGroup>(
      `${API_BASE_URL}/bc/catalog/seller/${sellerId}/variation/${variationId}/addongroup/${addongId}`,
      "DELETE",
      undefined,
      {},
      true,
      request
    );

    if (response) {
      if (response.statusCode === 200) {
        return response;
      } else if (response.statusCode === 400) {
        throw new Response("Bad request: Invalid addon ID or seller ID");
      } else {
        throw new Response(`Unexpected response status: ${response.statusCode}`);
      }
    } else {
      throw new Response("Failed to delete AddonMap: No response received");
    }
  }
}
export async function deleteVariation(
  sellerId: number,
  variationId: number,
  request?: Request
): Promise<ApiResponse<MyVariationData>> {
  if (useMock) {
    return { data: null, statusCode: 200 };
  } else {
    const response = await apiRequest<MyVariationData>(
      `${API_BASE_URL}/bc/catalog/seller/${sellerId}/myvariation/${variationId}`,
      "DELETE",
      undefined,
      {},
      true,
      request
    );

    if (response) {
      if (response.statusCode === 200) {
        return response;
      } else if (response.statusCode === 400) {
        throw new Response("Bad request: Invalid Variation ID or seller ID");
      } else {
        throw new Response(`Unexpected response status: ${response.statusCode}`);
      }
    } else {
      throw new Response("Failed to delete Variation: No response received");
    }
  }
}
export async function getVariation(
  sellerId: number,
  pageNo?: number,
  size?: number,
  matchBy?: string,
  request?: Request
): Promise<ApiResponse<any>> {
  if (useMock) {
    return { data: [], statusCode: 200 };
  }

  // Basic validation
  if (!sellerId || typeof sellerId !== "number" || sellerId <= 0) {
    throw new Response("Invalid sellerId provided");
  }

  if (pageNo !== undefined && (typeof pageNo !== "number" || pageNo < 0)) {
    throw new Response("Invalid pageNo provided");
  }

  if (size !== undefined && (typeof size !== "number" || size <= 0)) {
    throw new Response("Invalid size provided");
  }

  if (matchBy !== undefined && typeof matchBy !== "string") {
    throw new Response("matchBy must be a string if provided");
  }

  try {
    const queryParams = new URLSearchParams();
    if (pageNo !== undefined) queryParams.append("pageNo", pageNo.toString());
    if (size !== undefined) queryParams.append("size", size.toString());
    if (matchBy && matchBy.trim().length > 0) {
      queryParams.append("matchBy", matchBy.trim());
    }
    const url = `${API_BASE_URL}/bc/catalog/seller/${sellerId}/myvariations?${queryParams.toString()}`;

    const response = await apiRequest<any>(
      url,
      "GET",
      undefined,
      {},
      true,
      request
    );

    if (response) {
      return response;
    } else {
      throw new Response("Failed to fetch variation groups");
    }
  } catch (error: any) {
    // Log error or handle it accordingly
    throw new Error(`Error in Variations: ${error.message}`);
  }
}
export async function createVariation(
  sellerId: number,
  updateData: MyVariationData,
  request?: Request
): Promise<ApiResponse<MyVariationData>> {
  if (useMock) {
    return { data: null, statusCode: 200 };
  } else {
    const response = await apiRequest<MyVariationData>(

      `${API_BASE_URL}/bc/catalog/seller/${sellerId}/myvariation`,
      "POST",
      updateData,
      {},
      true,
      request
    );
    if (response) {
      return response;
    } else {
      throw new Response("Failed to create Variation");
    }
  }
}
export async function getAllDashboardDataFor(
  userId: number,
  date: Date,
  groupBy: DashboardGroupBy,
  request?: Request
): Promise<ApiResponse<SellerConsoleDataResponse[]>> {
  return getDashboardDataFor(userId, date, groupBy, request);
}

export async function getOrderItemSummary(
  startDate: Date,
  endDate: Date,
  request?: Request
): Promise<ApiResponse<OrderItem[]>> {
  const queryDate1 = dayjs(startDate).format("YYYY-MM-DD");

  const queryDate2 = dayjs(endDate).format("YYYY-MM-DD");
  const response = await apiRequest<OrderItem[]>(
    `${API_BASE_URL}/bc/seller/console/itemSummary?startDate=${queryDate1}&endDate=${queryDate2}`,
    "GET",
    undefined,
    {},
    true,
    request
  );

  if (response) {
    return response;
  } else {
    throw new Error("Failed to fetch buyer details");
  }
}
export async function getDashBoardBuyerSummary(
  startDate: Date,
  endDate: Date,
  request?: Request
): Promise<ApiResponse<BuyerSummary[]>> {
  const queryDate1 = dayjs(startDate).format("YYYY-MM-DD");

  const queryDate2 = dayjs(endDate).format("YYYY-MM-DD");

  const response = await apiRequest<BuyerSummary[]>(
    `${API_BASE_URL}/bc/seller/console/orders?startDate=${queryDate1}&endDate=${queryDate2}`,
    "GET",
    undefined,
    {},
    true,
    request
  );

  if (response) {
    return response;
  } else {
    throw new Error("Failed to fetch buyer details");
  }
}

export async function getBuyerSummary(
  userId: number,
  page: number,
  pageSize: number,
  tabValue: string,
  sortBy: string,
  search: string,
  sortByOrder: string,
  sellerId?: number,
  request?: Request
): Promise<ApiResponse<BuyerSummaryDetailsResponseItem[]>> {
  const url = new URL(
    `${API_BASE_URL}/bc/${userId}/buyersummarydetails/pagesize/${pageSize}/page/${page}`
  );

  const params = new URLSearchParams();
  params.append("tabValue", tabValue);
  params.append("sortBy", sortBy);
  params.append("sortByOrder", sortByOrder);
  if (sellerId) {
    params.append("sellerId", sellerId.toString());
  }

  if (search?.length >= 3 && search !== "") {
    params.append("searchBy", encodeURIComponent(search.trim()));
  }

  url.search = params.toString();

  const response = await apiRequest<BuyerSummaryDetailsResponseItem[]>(
    url.toString(),
    "GET",
    undefined,
    {},
    true,
    request
  );

  if (response) {
    return response;
  } else {
    throw new Error("Failed to fetch buyer summary data");
  }
}
export async function updateFbDiscountPrice(
  nBuyerId: number,
  FbDiscount: number,
  request?: Request
): Promise<ApiResponse<ContractPrice>> {
  const response = await apiRequest<ContractPrice>(
    `${API_BASE_URL}/bc/seller/discount`,
    "PUT",
    {
      nBuyerId: nBuyerId,
      discountPercentage: FbDiscount,
    },
    {},
    true,
    request
  );

  if (response !== undefined) {
    return response;
  } else {
    throw new Error("Failed to create ContractPrice");
  }
}

export async function getBuyerOrders(
  userId: number,
  buyerId: number,
  page: number,
  pageSize: number,
  request?: Request
): Promise<ApiResponse<OrderSummary[]>> {
  if (useMock) {
    return { data: [] };
  } else {
    const response = await apiRequest<OrderSummary[]>(
      `${API_BASE_URL}/bc/${userId}/buyer/${buyerId}/orderdetails/pagesize/${pageSize}/page/${page}`,
      "GET",
      undefined,
      {},
      true,
      request
    );

    if (response) {
      return response;
    } else {
      throw new Error("Failed to fetch buyer orders");
    }
  }
}

export async function getBuyerDetails(
  userId: number,
  buyerId: number,
  request?: Request
): Promise<ApiResponse<BuyerDetailsResponse>> {
  if (useMock) {
    const { buyerDetailsMockData } = await import(
      "./__mocks__/buyerDetails.mockData"
    );
    return { data: buyerDetailsMockData };
  } else {
    const response = await apiRequest<BuyerDetailsResponse>(
      `${API_BASE_URL}/bc/${userId}/buyer/${buyerId}`,
      "GET",
      undefined,
      {},
      true,
      request
    );

    if (response) {
      return response;
    } else {
      throw new Error("Failed to fetch buyer details");
    }
  }
}

export async function getContractPricesForBuyer(
  buyerId: number,
  page: number,
  pageSize: number,
  request?: Request
): Promise<ApiResponse<ContractPrice[]>> {
  const response = await apiRequest<ContractPrice[]>(
    `${API_BASE_URL}/bc/contractprice/buyer/${buyerId}/items?pageNo=${page}&size=${pageSize}`,
    "GET",
    undefined,
    {},
    true,
    request
  );

  if (response) {
    return response;
  } else {
    throw new Error("Failed to fetch buyer details");
  }
}

export async function getSelectedItemSummary(
  sellerItemId: number,
  request?: Request
): Promise<ApiResponse<SelectedItemSummary>> {
  const response = await apiRequest<SelectedItemSummary>(
    `${API_BASE_URL}/bc/seller/item/${sellerItemId}/summary`,
    "GET",
    undefined,
    {},
    true,
    request
  );

  if (response) {
    return response;
  } else {
    throw new Error("Failed to fetch buyer details");
  }
}

export async function getContractPricesForSellerItem(
  sellerItemId: number,
  request?: Request
): Promise<ApiResponse<ContractPrice>> {
  const response = await apiRequest<ContractPrice>(
    `${API_BASE_URL}/bc/contractprice/selleritem/${sellerItemId}/buyers`,
    "GET",
    undefined,
    {},
    true,
    request
  );

  if (response) {
    return response;
  } else {
    throw new Error("Failed to fetch buyer details");
  }
}

export async function addContractPrice(
  sellerItemId: number,
  buyerId: number,
  request?: Request
): Promise<ApiResponse<ContractPrice>> {
  const response = await apiRequest<ContractPrice>(
    `${API_BASE_URL}/bc/contractprice/selleritem/${sellerItemId}/buyer/${buyerId}`,
    "POST",
    undefined,
    {},
    true,
    request
  );

  if (response !== undefined) {
    return response;
  } else {
    throw new Error("Failed to create ContractPrice");
  }
}
export async function updateContractStatus(
  contractPriceEnabled: boolean,
  contractPriceId: number,
  request?: Request
): Promise<ApiResponse<ContractPrice>> {
  const response = await apiRequest<ContractPrice>(
    `${API_BASE_URL}/bc/contractprice/${contractPriceId}/enabled/${contractPriceEnabled}`,
    "PUT",
    undefined,
    {},
    true,
    request
  );

  if (response !== undefined) {
    return response;
  } else {
    throw new Error("Failed to create ContractPrice");
  }
}

export async function updateContractPrice(
  contractPriceId: number,
  contractPrice: number,
  request?: Request
): Promise<ApiResponse<ContractPrice>> {
  const response = await apiRequest<ContractPrice>(
    `${API_BASE_URL}/bc/contractprice/${contractPriceId}/price/${contractPrice}`,
    "PUT",
    undefined,
    {},
    true,
    request
  );

  if (response !== undefined) {
    return response;
  } else {
    throw new Error("Failed to create ContractPrice");
  }
}
export async function updateContractValidity(
  buyerId: number,
  validityDate: string,
  request?: Request
): Promise<ApiResponse<ContractPrice>> {
  const response = await apiRequest<ContractPrice>(
    `${API_BASE_URL}/bc/contractBuyer/${buyerId}/validity/${validityDate}`,
    "PUT",
    undefined,
    {},
    true,
    request
  );

  if (response !== undefined) {
    return response;
  } else {
    throw new Error("Failed to create ContractPrice");
  }
}

export async function getSellerAreas(
  userId: number,
  request?: Request
): Promise<ApiResponse<SellerArea[]>> {
  if (useMock) {
    return { data: [] };
  } else {
    const response = await apiRequest<SellerArea[]>(
      `${API_BASE_URL}/bc/${userId}/sellerareas`,
      "GET",
      undefined,
      {},
      true,
      request
    );

    if (response.data !== null) {
      return response;
    } else {
      throw new Error("Failed to fetch seller areas");
    }
  }
}

export async function getNetworks(
  request?: Request
): Promise<ApiResponse<Networks[]>> {
  if (useMock) {
    return { data: [] };
  } else {
    const response = await apiRequest<Networks[]>(
      `${API_BASE_URL}/bc/mnetadmin/networks`,
      "GET",
      undefined,
      {},
      true,
      request
    );
    if (response) {
      return response;
    } else {
      throw new Error("Failed to fetch districts and states");
    }
  }
}

export async function getSellers(
  request?: Request
): Promise<ApiResponse<Seller[]>> {
  if (useMock) {
    return { data: [] };
  } else {
    const response = await apiRequest<Seller[]>(
      `${API_BASE_URL}/bc/mnetadmin/sellers`,
      "GET",
      undefined,
      {},
      true,
      request
    );
    if (response) {
      return response;
    } else {
      throw new Error("Failed to fetch districts and states");
    }
  }
}

export async function getNetworkAreas(
  networkId: number,
  request?: Request
): Promise<ApiResponse<NetworkAreas[]>> {
  if (useMock) {
    return { data: [] };
  } else {
    const response = await apiRequest<NetworkAreas[]>(
      `${API_BASE_URL}/bc/mnetmanager/network/${networkId}/areas`,
      "GET",
      undefined,
      {},
      true,
      request
    );
    if (response) {
      return response;
    } else {
      throw new Error("Failed to fetch districts and states");
    }
  }
}

export async function getsmSellerArea(
  sellerId: number,
  request?: Request
): Promise<ApiResponse<smSellerArea[]>> {
  if (useMock) {
    return { data: [] };
  } else {
    const response = await apiRequest<smSellerArea[]>(
      `${API_BASE_URL}/bc/seller/${sellerId}/sareas`,
      "GET",
      undefined,
      {},
      true,
      request
    );
    if (response) {
      return response;
    } else {
      throw new Error("Failed to fetch districts and states");
    }
  }
}

export async function getNetworkConfig(
  type: string,
  networkId: number,
  request?: Request
): Promise<ApiResponse<NetworkConfig[]>> {
  if (useMock) {
    return { data: [] };
  } else {
    const response = await apiRequest<NetworkConfig[]>(
      `${API_BASE_URL}/bc/mnetadmin/config/${type}/${networkId}/details`,
      "GET",
      undefined,
      {},
      true,
      request
    );
    if (response) {
      console.log(response, "77777777777");

      return response;
    } else {
      throw new Error("Failed to fetch districts and states");
    }
  }
}
export async function getNetworkBanners(
  networkId: number,
  request?: Request
): Promise<ApiResponse<NetworkBanner[]>> {
  if (useMock) {
    return { data: [] };
  } else {
    const response = await apiRequest<NetworkBanner[]>(
      `${API_BASE_URL}/bc/mnetadmin/${networkId}/networkbanners`,
      "GET",
      undefined,
      {},
      true,
      request
    );
    if (response) {
      console.log(response, "77777777777");

      return response;
    } else {
      throw new Error("Failed to fetch districts and states");
    }
  }
}

export async function getNetworkBuyers(
  networkId: number,
  pageSize: number,
  currentPage: number,
  matchBy?: string,
  request?: Request
): Promise<ApiResponse<NetworkBuyer[]>> {
  const response = await apiRequest<NetworkBuyer[]>(
    `${API_BASE_URL}/bc/mnetadmin/network/${networkId}/buyers` + `?pageSize=${pageSize}&pageNo=${currentPage}` + (matchBy ? `&matchBy=${matchBy}` : ""),
    "GET",
    undefined,
    {},
    true,
    request
  );
  if (response) {
    return response;
  } else {
    throw new Error("Failed to fetch buyers");
  }
}

export async function addNetworkLocalities(
  networkId: number,
  agentUserId: number,
  area: number[],
  request?: Request
) {
  console.log("add Network area called for ", JSON.stringify(area));
  const body = JSON.stringify({
    networkId,
    agentUserId,
    areas: area, // expects an array of area IDs
  });
  try {
    const response = await apiRequest<void>(
      `${API_BASE_URL}/bc/mnetadmin/network/area`,
      "POST",
      JSON.parse(body),
      {},
      true,
      request
    );
    // const response = await fetch(`${API_BASE_URL}/network/area`, {
    //   method: "POST",
    //   headers: {
    //     Accept: "application/json",
    //     "Content-Type": "application/json",
    //   },
    //   body: JSON.stringify(localities),
    // });

    // 🔥 FIX: Check if response is empty before parsing JSON
    const text = await response.headers;
    if (!text) {
      return { message: "No response body" }; // Return a default object instead of calling .json()
    }

    return text;
  } catch (error) {
    console.error("Error submitting localities:", error);
    throw error;
  }
}

export async function getSellerBusinessConfig(
  type: string,
  sellerId: number,
  request?: Request
): Promise<ApiResponse<SellerConfig[]>> {
  if (useMock) {
    return { data: [] };
  } else {
    const response = await apiRequest<SellerConfig[]>(
      `${API_BASE_URL}/bc/mnetadmin/seller/config/${type}/${sellerId}/details`,
      "GET",
      undefined,
      {},
      true,
      request
    );
    if (response) {
      return response;
    } else {
      throw new Error("Failed to fetch SellerConfig");
    }
  }
}

export async function getSeItems(
  sellerId: number,
  page?: number,
  pageSize?: number,
  search?: string | undefined,
  request?: Request
): Promise<ApiResponse<SellerItem[]>> {
  const url = new URL(`${API_BASE_URL}/bc/mnetadmin/seller/${sellerId}/items`);
  if (page !== undefined) url.searchParams.append("pageNo", page.toString());
  if (pageSize !== undefined)
    url.searchParams.append("size", pageSize.toString());
  if (search) url.searchParams.append("matchBy", search);

  const response = await apiRequest<SellerItem[]>(
    url.toString(),
    "GET",
    undefined,
    {},
    true,
    request
  );

  if (response) {
    return response;
  } else {
    throw new Error("Failed to fetch seller items");
  }
}

export async function getSeCategories(
  sellerId: number,
  page?: number,
  pageSize?: number,
  search?: string | undefined,
  request?: Request
): Promise<ApiResponse<MasterItemCategories[]>> {
  const url = new URL(
    `${API_BASE_URL}/bc/mnetadmin/categories/seller/${sellerId}`
  );
  if (page !== undefined) url.searchParams.append("pageNo", page.toString());
  if (pageSize !== undefined)
    url.searchParams.append("size", pageSize.toString());
  if (search) url.searchParams.append("matchBy", search);

  const response = await apiRequest<MasterItemCategories[]>(
    url.toString(),
    "GET",
    undefined,
    {},
    true,
    request
  );

  if (response) {
    return response;
  } else {
    throw new Error("Failed to fetch seller categories");
  }
}

export async function updateSellerCategory(
  sellerId: number,
  categoryId: number,
  updatedData: MasterItemCategories,
  request?: Request
): Promise<ApiResponse<MasterItemCategories>> {
  const response = await apiRequest<MasterItemCategories>(
    `${API_BASE_URL}/bc/mnetadmin/categories/seller/${sellerId}/${categoryId}?${updatedData.sequence !== undefined ? `key=sequence&value=${updatedData.sequence}` : ""}`,
    "PUT",
    undefined,
    {},
    true,
    request
  );

  if (response) {
    return response;
  } else {
    throw new Error("Failed to update seller category");
  }
}

export async function getsmSellerConfig(
  sellerId: number,
  request?: Request
): Promise<ApiResponse<SellerConfig[]>> {
  if (useMock) {
    return { data: [] };
  } else {
    const response = await apiRequest<SellerConfig[]>(
      `${API_BASE_URL}/bc/mnetadmin/seller/${sellerId}/details`,
      "GET",
      undefined,
      {},
      true,
      request
    );
    if (response) {
      return response;
    } else {
      throw new Error("Failed to fetch districts and states");
    }
  }
}
export async function getSuppliers(
  sellerItemId: number,
  request?: Request
): Promise<ApiResponse<Suppliers[]>> {
  try {
    const response = await apiRequest<Suppliers[]>(
      `${API_BASE_URL}/bc/mnetadmin/item/${sellerItemId}/suppliers`,
      "GET",
      undefined,
      {},
      true,
      request
    );
    if (response) {
      return response;
    } else {
      throw new Error("Failed to fetch suppliers");
    }
  }
  catch (err) {
    throw new Error("Failed to fetch suppliers");
  }
}

export async function getsmDistrictsAndStates(
  userId: number,
  request?: Request
): Promise<ApiResponse<StateAndDistricts[]>> {
  if (useMock) {
    return { data: [] };
  } else {
    const response = await apiRequest<StateAndDistricts[]>(
      `${API_BASE_URL}/bc/${userId}/areas/districts`,
      "GET",
      undefined,
      {},
      true,
      request
    );

    if (response) {
      return response;
    } else {
      throw new Error("Failed to fetch districts and states");
    }
  }
}

export async function getMasterLocalities(
  userId: number,
  state: string,
  district: string,
  request?: Request
): Promise<ApiResponse<MasterLocalities[]>> {
  const response = await apiRequest<MasterLocalities[]>(
    `${API_BASE_URL}/bc/${userId}/areas/state/${state}/${district}`,
    "GET",
    undefined,
    {},
    true,
    request
  );

  if (response !== undefined) {
    return response;
  } else {
    throw new Error("Failed to fetch global areas");
  }
}

export async function getNeworkAgents(
  networkId: number,
  request?: Request
): Promise<ApiResponse<networkAgents[]>> {
  const response = await apiRequest<networkAgents[]>(
    `${API_BASE_URL}/bc/mnetadmin/network/${networkId}/agents`,
    "GET",
    undefined,
    {},
    true,
    request
  );

  if (response !== undefined) {
    return response;
  } else {
    throw new Error("Failed to fetch global areas");
  }
}

export async function getDistrictsAndStates(
  userId: number,
  request: Request
): Promise<ApiResponse<SellerArea[]>> {
  if (useMock) {
    return { data: [] };
  } else {
    const response = await apiRequest<SellerArea[]>(
      `${API_BASE_URL}/bc/${userId}/areas/districts`,
      "GET",
      undefined,
      {},
      true,
      request
    );

    if (response) {
      return response;
    } else {
      throw new Error("Failed to fetch districts and states");
    }
  }
}

export async function getGlobalAreas(
  userId: number,
  state: string,
  district: string,
  request?: Request
): Promise<ApiResponse<SellerArea[]>> {
  const response = await apiRequest<SellerArea[]>(
    `${API_BASE_URL}/bc/${userId}/areas/state/${state}/${district}`,
    "GET",
    undefined,
    {},
    true,
    request
  );

  if (response !== undefined) {
    return response;
  } else {
    throw new Error("Failed to fetch global areas");
  }
}

export async function getAreaById(
  areaId: number,
  request?: Request
): Promise<ApiResponse<SellerArea>> {
  const response = await apiRequest<SellerArea>(
    `${API_BASE_URL}/bc/area/${areaId}`,
    "GET",
    undefined,
    {},
    true,
    request
  );

  if (response !== undefined) {
    return response;
  } else {
    throw new Error("Failed to fetch area");
  }
}

export async function createArea(
  userId: number,
  data: any,
  request?: Request
): Promise<ApiResponse<CreateAreaResponse>> {
  const response = await apiRequest<CreateAreaResponse>(
    `${API_BASE_URL}/bc/${userId}/areas`,
    "POST",
    data,
    {},
    true,
    request
  );

  if (response !== undefined) {
    return response;
  } else {
    throw new Error("Failed to create area");
  }
}

export async function saveWABToken(
  data: SaveWABTokenRequest,
  request?: Request
): Promise<ApiResponse<void>> {
  const response = await apiRequest<void>(
    `${API_BASE_URL}/bc/seller/wab_token`,
    "POST",
    data,
    {},
    true,
    request
  );

  if (response !== undefined) {
    return response;
  } else {
    throw new Error("Failed to save wab token");
  }
}
export async function updateEditItem(
  sellerId: number,
  itemId: number,
  data: SellerItem,
  request?: Request
): Promise<ApiResponse<SellerItem>> {

  console.log(data, "updateData.........")

  const response = await apiRequest<SellerItem>(
    `${API_BASE_URL}/bc/mnetadmin/seller/${sellerId}/item/${itemId}`,
    "PUT",
    data,
    {},
    true,
    request
  );

  if (response !== undefined) {
    return response;
  } else {
    throw new Error("Failed to create area");
  }
}

export async function updateArea(
  userId: number,
  data: UpdateAreaRequest,
  request?: Request
): Promise<ApiResponse<CreateAreaResponse>> {
  const response = await apiRequest<CreateAreaResponse>(
    `${API_BASE_URL}/bc/${userId}/areas`,
    "PUT",
    data,
    {},
    true,
    request
  );

  if (response !== undefined) {
    return response;
  } else {
    throw new Error("Failed to create area");
  }
}

export async function disableMasterLocality(
  areaId: number,
  request?: Request
): Promise<ApiResponse<void>> {
  const response = await apiRequest<void>(
    `${API_BASE_URL}/bc/mnetadmin/area/${areaId}/toggle-status`,
    "PUT",
    undefined,
    {},
    true,
    request
  );

  if (response !== undefined) {
    return response;
  } else {
    throw new Error("Failed to activate seller area");
  }
}

export async function activateSellerArea(
  userId: number,
  areaId: number,
  request?: Request
): Promise<ApiResponse<void>> {
  if (useMock) {
    return { data: undefined };
  } else {
    const response = await apiRequest<void>(
      `${API_BASE_URL}/bc/${userId}/sellerarea/${areaId}`,
      "POST",
      undefined,
      {},
      true,
      request
    );

    if (response !== undefined) {
      return response;
    } else {
      throw new Error("Failed to activate seller area");
    }
  }
}
export async function getDashboardDataForBuyer(
  bueryId: number,
  date: Date,
  groupBy: DashboardGroupBy,
  request?: Request
): Promise<ApiResponse<SellerConsoleDataResponse[]>> {
  const queryDate = dayjs(date).format("YYYY-MM-DD");
  const summaryType =
    {
      [DashboardGroupBy.Daily]: "daily",
      [DashboardGroupBy.Weekly]: "weekly",
      [DashboardGroupBy.Monthly]: "monthly",
    }[groupBy] || "daily";

  const response = await apiRequest<SellerConsoleDataResponse[]>(
    `${API_BASE_URL}/bc/seller/console/summary?date=${queryDate}&period=${summaryType}&buyerId=${bueryId}`,
    "GET",
    undefined,
    {},
    true,
    request
  );

  if (response) {
    return response;
  } else {
    throw new Error("Failed to fetch dashboard data");
  }
}
export async function getDashboardDataForItem(
  ItemId: number,
  date: Date,
  groupBy: DashboardGroupBy,
  request?: Request
): Promise<ApiResponse<SellerConsoleDataResponse[]>> {
  const queryDate = dayjs(date).format("YYYY-MM-DD");
  const summaryType =
    {
      [DashboardGroupBy.Daily]: "daily",
      [DashboardGroupBy.Weekly]: "weekly",
      [DashboardGroupBy.Monthly]: "monthly",
    }[groupBy] || "daily";

  const response = await apiRequest<SellerConsoleDataResponse[]>(
    `${API_BASE_URL}/bc/seller/console/summary?date=${queryDate}&period=${summaryType}&sellerItemId=${ItemId}`,
    "GET",
    undefined,
    {},
    true,
    request
  );

  if (response) {
    return response;
  } else {
    throw new Error("Failed to fetch dashboard data");
  }
}

export async function getNetWorkSeller(
  netWorkId: number,
  request?: Request
): Promise<ApiResponse<NetWorkDetails[]>> {
  return apiRequest<NetWorkDetails[]>(
    `${API_BASE_URL}/bc/network/${netWorkId}/seller`,
    "GET",
    undefined,
    {},
    true,
    request
  );
}
