{"version": 3, "sources": ["../../string_decoder/lib/string_decoder.js"], "sourcesContent": ["// Copyright Joyent, Inc. and other Node contributors.\n//\n// Permission is hereby granted, free of charge, to any person obtaining a\n// copy of this software and associated documentation files (the\n// \"Software\"), to deal in the Software without restriction, including\n// without limitation the rights to use, copy, modify, merge, publish,\n// distribute, sublicense, and/or sell copies of the Software, and to permit\n// persons to whom the Software is furnished to do so, subject to the\n// following conditions:\n//\n// The above copyright notice and this permission notice shall be included\n// in all copies or substantial portions of the Software.\n//\n// THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS\n// OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\n// MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN\n// NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM,\n// DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR\n// OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE\n// USE OR OTHER DEALINGS IN THE SOFTWARE.\n\n'use strict';\n\n/*<replacement>*/\n\nvar Buffer = require('safe-buffer').Buffer;\n/*</replacement>*/\n\nvar isEncoding = Buffer.isEncoding || function (encoding) {\n  encoding = '' + encoding;\n  switch (encoding && encoding.toLowerCase()) {\n    case 'hex':case 'utf8':case 'utf-8':case 'ascii':case 'binary':case 'base64':case 'ucs2':case 'ucs-2':case 'utf16le':case 'utf-16le':case 'raw':\n      return true;\n    default:\n      return false;\n  }\n};\n\nfunction _normalizeEncoding(enc) {\n  if (!enc) return 'utf8';\n  var retried;\n  while (true) {\n    switch (enc) {\n      case 'utf8':\n      case 'utf-8':\n        return 'utf8';\n      case 'ucs2':\n      case 'ucs-2':\n      case 'utf16le':\n      case 'utf-16le':\n        return 'utf16le';\n      case 'latin1':\n      case 'binary':\n        return 'latin1';\n      case 'base64':\n      case 'ascii':\n      case 'hex':\n        return enc;\n      default:\n        if (retried) return; // undefined\n        enc = ('' + enc).toLowerCase();\n        retried = true;\n    }\n  }\n};\n\n// Do not cache `Buffer.isEncoding` when checking encoding names as some\n// modules monkey-patch it to support additional encodings\nfunction normalizeEncoding(enc) {\n  var nenc = _normalizeEncoding(enc);\n  if (typeof nenc !== 'string' && (Buffer.isEncoding === isEncoding || !isEncoding(enc))) throw new Error('Unknown encoding: ' + enc);\n  return nenc || enc;\n}\n\n// StringDecoder provides an interface for efficiently splitting a series of\n// buffers into a series of JS strings without breaking apart multi-byte\n// characters.\nexports.StringDecoder = StringDecoder;\nfunction StringDecoder(encoding) {\n  this.encoding = normalizeEncoding(encoding);\n  var nb;\n  switch (this.encoding) {\n    case 'utf16le':\n      this.text = utf16Text;\n      this.end = utf16End;\n      nb = 4;\n      break;\n    case 'utf8':\n      this.fillLast = utf8FillLast;\n      nb = 4;\n      break;\n    case 'base64':\n      this.text = base64Text;\n      this.end = base64End;\n      nb = 3;\n      break;\n    default:\n      this.write = simpleWrite;\n      this.end = simpleEnd;\n      return;\n  }\n  this.lastNeed = 0;\n  this.lastTotal = 0;\n  this.lastChar = Buffer.allocUnsafe(nb);\n}\n\nStringDecoder.prototype.write = function (buf) {\n  if (buf.length === 0) return '';\n  var r;\n  var i;\n  if (this.lastNeed) {\n    r = this.fillLast(buf);\n    if (r === undefined) return '';\n    i = this.lastNeed;\n    this.lastNeed = 0;\n  } else {\n    i = 0;\n  }\n  if (i < buf.length) return r ? r + this.text(buf, i) : this.text(buf, i);\n  return r || '';\n};\n\nStringDecoder.prototype.end = utf8End;\n\n// Returns only complete characters in a Buffer\nStringDecoder.prototype.text = utf8Text;\n\n// Attempts to complete a partial non-UTF-8 character using bytes from a Buffer\nStringDecoder.prototype.fillLast = function (buf) {\n  if (this.lastNeed <= buf.length) {\n    buf.copy(this.lastChar, this.lastTotal - this.lastNeed, 0, this.lastNeed);\n    return this.lastChar.toString(this.encoding, 0, this.lastTotal);\n  }\n  buf.copy(this.lastChar, this.lastTotal - this.lastNeed, 0, buf.length);\n  this.lastNeed -= buf.length;\n};\n\n// Checks the type of a UTF-8 byte, whether it's ASCII, a leading byte, or a\n// continuation byte. If an invalid byte is detected, -2 is returned.\nfunction utf8CheckByte(byte) {\n  if (byte <= 0x7F) return 0;else if (byte >> 5 === 0x06) return 2;else if (byte >> 4 === 0x0E) return 3;else if (byte >> 3 === 0x1E) return 4;\n  return byte >> 6 === 0x02 ? -1 : -2;\n}\n\n// Checks at most 3 bytes at the end of a Buffer in order to detect an\n// incomplete multi-byte UTF-8 character. The total number of bytes (2, 3, or 4)\n// needed to complete the UTF-8 character (if applicable) are returned.\nfunction utf8CheckIncomplete(self, buf, i) {\n  var j = buf.length - 1;\n  if (j < i) return 0;\n  var nb = utf8CheckByte(buf[j]);\n  if (nb >= 0) {\n    if (nb > 0) self.lastNeed = nb - 1;\n    return nb;\n  }\n  if (--j < i || nb === -2) return 0;\n  nb = utf8CheckByte(buf[j]);\n  if (nb >= 0) {\n    if (nb > 0) self.lastNeed = nb - 2;\n    return nb;\n  }\n  if (--j < i || nb === -2) return 0;\n  nb = utf8CheckByte(buf[j]);\n  if (nb >= 0) {\n    if (nb > 0) {\n      if (nb === 2) nb = 0;else self.lastNeed = nb - 3;\n    }\n    return nb;\n  }\n  return 0;\n}\n\n// Validates as many continuation bytes for a multi-byte UTF-8 character as\n// needed or are available. If we see a non-continuation byte where we expect\n// one, we \"replace\" the validated continuation bytes we've seen so far with\n// a single UTF-8 replacement character ('\\ufffd'), to match v8's UTF-8 decoding\n// behavior. The continuation byte check is included three times in the case\n// where all of the continuation bytes for a character exist in the same buffer.\n// It is also done this way as a slight performance increase instead of using a\n// loop.\nfunction utf8CheckExtraBytes(self, buf, p) {\n  if ((buf[0] & 0xC0) !== 0x80) {\n    self.lastNeed = 0;\n    return '\\ufffd';\n  }\n  if (self.lastNeed > 1 && buf.length > 1) {\n    if ((buf[1] & 0xC0) !== 0x80) {\n      self.lastNeed = 1;\n      return '\\ufffd';\n    }\n    if (self.lastNeed > 2 && buf.length > 2) {\n      if ((buf[2] & 0xC0) !== 0x80) {\n        self.lastNeed = 2;\n        return '\\ufffd';\n      }\n    }\n  }\n}\n\n// Attempts to complete a multi-byte UTF-8 character using bytes from a Buffer.\nfunction utf8FillLast(buf) {\n  var p = this.lastTotal - this.lastNeed;\n  var r = utf8CheckExtraBytes(this, buf, p);\n  if (r !== undefined) return r;\n  if (this.lastNeed <= buf.length) {\n    buf.copy(this.lastChar, p, 0, this.lastNeed);\n    return this.lastChar.toString(this.encoding, 0, this.lastTotal);\n  }\n  buf.copy(this.lastChar, p, 0, buf.length);\n  this.lastNeed -= buf.length;\n}\n\n// Returns all complete UTF-8 characters in a Buffer. If the Buffer ended on a\n// partial character, the character's bytes are buffered until the required\n// number of bytes are available.\nfunction utf8Text(buf, i) {\n  var total = utf8CheckIncomplete(this, buf, i);\n  if (!this.lastNeed) return buf.toString('utf8', i);\n  this.lastTotal = total;\n  var end = buf.length - (total - this.lastNeed);\n  buf.copy(this.lastChar, 0, end);\n  return buf.toString('utf8', i, end);\n}\n\n// For UTF-8, a replacement character is added when ending on a partial\n// character.\nfunction utf8End(buf) {\n  var r = buf && buf.length ? this.write(buf) : '';\n  if (this.lastNeed) return r + '\\ufffd';\n  return r;\n}\n\n// UTF-16LE typically needs two bytes per character, but even if we have an even\n// number of bytes available, we need to check if we end on a leading/high\n// surrogate. In that case, we need to wait for the next two bytes in order to\n// decode the last character properly.\nfunction utf16Text(buf, i) {\n  if ((buf.length - i) % 2 === 0) {\n    var r = buf.toString('utf16le', i);\n    if (r) {\n      var c = r.charCodeAt(r.length - 1);\n      if (c >= 0xD800 && c <= 0xDBFF) {\n        this.lastNeed = 2;\n        this.lastTotal = 4;\n        this.lastChar[0] = buf[buf.length - 2];\n        this.lastChar[1] = buf[buf.length - 1];\n        return r.slice(0, -1);\n      }\n    }\n    return r;\n  }\n  this.lastNeed = 1;\n  this.lastTotal = 2;\n  this.lastChar[0] = buf[buf.length - 1];\n  return buf.toString('utf16le', i, buf.length - 1);\n}\n\n// For UTF-16LE we do not explicitly append special replacement characters if we\n// end on a partial character, we simply let v8 handle that.\nfunction utf16End(buf) {\n  var r = buf && buf.length ? this.write(buf) : '';\n  if (this.lastNeed) {\n    var end = this.lastTotal - this.lastNeed;\n    return r + this.lastChar.toString('utf16le', 0, end);\n  }\n  return r;\n}\n\nfunction base64Text(buf, i) {\n  var n = (buf.length - i) % 3;\n  if (n === 0) return buf.toString('base64', i);\n  this.lastNeed = 3 - n;\n  this.lastTotal = 3;\n  if (n === 1) {\n    this.lastChar[0] = buf[buf.length - 1];\n  } else {\n    this.lastChar[0] = buf[buf.length - 2];\n    this.lastChar[1] = buf[buf.length - 1];\n  }\n  return buf.toString('base64', i, buf.length - n);\n}\n\nfunction base64End(buf) {\n  var r = buf && buf.length ? this.write(buf) : '';\n  if (this.lastNeed) return r + this.lastChar.toString('base64', 0, 3 - this.lastNeed);\n  return r;\n}\n\n// Pass bytes on through for single-byte encodings (e.g. ascii, latin1, hex)\nfunction simpleWrite(buf) {\n  return buf.toString(this.encoding);\n}\n\nfunction simpleEnd(buf) {\n  return buf && buf.length ? this.write(buf) : '';\n}"], "mappings": ";;;;;;;;AAAA;AAAA;AAAA;AAyBA,QAAI,SAAS,sBAAuB;AAGpC,QAAI,aAAa,OAAO,cAAc,SAAU,UAAU;AACxD,iBAAW,KAAK;AAChB,cAAQ,YAAY,SAAS,YAAY,GAAG;AAAA,QAC1C,KAAK;AAAA,QAAM,KAAK;AAAA,QAAO,KAAK;AAAA,QAAQ,KAAK;AAAA,QAAQ,KAAK;AAAA,QAAS,KAAK;AAAA,QAAS,KAAK;AAAA,QAAO,KAAK;AAAA,QAAQ,KAAK;AAAA,QAAU,KAAK;AAAA,QAAW,KAAK;AACxI,iBAAO;AAAA,QACT;AACE,iBAAO;AAAA,MACX;AAAA,IACF;AAEA,aAAS,mBAAmB,KAAK;AAC/B,UAAI,CAAC,IAAK,QAAO;AACjB,UAAI;AACJ,aAAO,MAAM;AACX,gBAAQ,KAAK;AAAA,UACX,KAAK;AAAA,UACL,KAAK;AACH,mBAAO;AAAA,UACT,KAAK;AAAA,UACL,KAAK;AAAA,UACL,KAAK;AAAA,UACL,KAAK;AACH,mBAAO;AAAA,UACT,KAAK;AAAA,UACL,KAAK;AACH,mBAAO;AAAA,UACT,KAAK;AAAA,UACL,KAAK;AAAA,UACL,KAAK;AACH,mBAAO;AAAA,UACT;AACE,gBAAI,QAAS;AACb,mBAAO,KAAK,KAAK,YAAY;AAC7B,sBAAU;AAAA,QACd;AAAA,MACF;AAAA,IACF;AAIA,aAAS,kBAAkB,KAAK;AAC9B,UAAI,OAAO,mBAAmB,GAAG;AACjC,UAAI,OAAO,SAAS,aAAa,OAAO,eAAe,cAAc,CAAC,WAAW,GAAG,GAAI,OAAM,IAAI,MAAM,uBAAuB,GAAG;AAClI,aAAO,QAAQ;AAAA,IACjB;AAKA,YAAQ,gBAAgB;AACxB,aAAS,cAAc,UAAU;AAC/B,WAAK,WAAW,kBAAkB,QAAQ;AAC1C,UAAI;AACJ,cAAQ,KAAK,UAAU;AAAA,QACrB,KAAK;AACH,eAAK,OAAO;AACZ,eAAK,MAAM;AACX,eAAK;AACL;AAAA,QACF,KAAK;AACH,eAAK,WAAW;AAChB,eAAK;AACL;AAAA,QACF,KAAK;AACH,eAAK,OAAO;AACZ,eAAK,MAAM;AACX,eAAK;AACL;AAAA,QACF;AACE,eAAK,QAAQ;AACb,eAAK,MAAM;AACX;AAAA,MACJ;AACA,WAAK,WAAW;AAChB,WAAK,YAAY;AACjB,WAAK,WAAW,OAAO,YAAY,EAAE;AAAA,IACvC;AAEA,kBAAc,UAAU,QAAQ,SAAU,KAAK;AAC7C,UAAI,IAAI,WAAW,EAAG,QAAO;AAC7B,UAAI;AACJ,UAAI;AACJ,UAAI,KAAK,UAAU;AACjB,YAAI,KAAK,SAAS,GAAG;AACrB,YAAI,MAAM,OAAW,QAAO;AAC5B,YAAI,KAAK;AACT,aAAK,WAAW;AAAA,MAClB,OAAO;AACL,YAAI;AAAA,MACN;AACA,UAAI,IAAI,IAAI,OAAQ,QAAO,IAAI,IAAI,KAAK,KAAK,KAAK,CAAC,IAAI,KAAK,KAAK,KAAK,CAAC;AACvE,aAAO,KAAK;AAAA,IACd;AAEA,kBAAc,UAAU,MAAM;AAG9B,kBAAc,UAAU,OAAO;AAG/B,kBAAc,UAAU,WAAW,SAAU,KAAK;AAChD,UAAI,KAAK,YAAY,IAAI,QAAQ;AAC/B,YAAI,KAAK,KAAK,UAAU,KAAK,YAAY,KAAK,UAAU,GAAG,KAAK,QAAQ;AACxE,eAAO,KAAK,SAAS,SAAS,KAAK,UAAU,GAAG,KAAK,SAAS;AAAA,MAChE;AACA,UAAI,KAAK,KAAK,UAAU,KAAK,YAAY,KAAK,UAAU,GAAG,IAAI,MAAM;AACrE,WAAK,YAAY,IAAI;AAAA,IACvB;AAIA,aAAS,cAAc,MAAM;AAC3B,UAAI,QAAQ,IAAM,QAAO;AAAA,eAAW,QAAQ,MAAM,EAAM,QAAO;AAAA,eAAW,QAAQ,MAAM,GAAM,QAAO;AAAA,eAAW,QAAQ,MAAM,GAAM,QAAO;AAC3I,aAAO,QAAQ,MAAM,IAAO,KAAK;AAAA,IACnC;AAKA,aAAS,oBAAoB,MAAM,KAAK,GAAG;AACzC,UAAI,IAAI,IAAI,SAAS;AACrB,UAAI,IAAI,EAAG,QAAO;AAClB,UAAI,KAAK,cAAc,IAAI,CAAC,CAAC;AAC7B,UAAI,MAAM,GAAG;AACX,YAAI,KAAK,EAAG,MAAK,WAAW,KAAK;AACjC,eAAO;AAAA,MACT;AACA,UAAI,EAAE,IAAI,KAAK,OAAO,GAAI,QAAO;AACjC,WAAK,cAAc,IAAI,CAAC,CAAC;AACzB,UAAI,MAAM,GAAG;AACX,YAAI,KAAK,EAAG,MAAK,WAAW,KAAK;AACjC,eAAO;AAAA,MACT;AACA,UAAI,EAAE,IAAI,KAAK,OAAO,GAAI,QAAO;AACjC,WAAK,cAAc,IAAI,CAAC,CAAC;AACzB,UAAI,MAAM,GAAG;AACX,YAAI,KAAK,GAAG;AACV,cAAI,OAAO,EAAG,MAAK;AAAA,cAAO,MAAK,WAAW,KAAK;AAAA,QACjD;AACA,eAAO;AAAA,MACT;AACA,aAAO;AAAA,IACT;AAUA,aAAS,oBAAoB,MAAM,KAAK,GAAG;AACzC,WAAK,IAAI,CAAC,IAAI,SAAU,KAAM;AAC5B,aAAK,WAAW;AAChB,eAAO;AAAA,MACT;AACA,UAAI,KAAK,WAAW,KAAK,IAAI,SAAS,GAAG;AACvC,aAAK,IAAI,CAAC,IAAI,SAAU,KAAM;AAC5B,eAAK,WAAW;AAChB,iBAAO;AAAA,QACT;AACA,YAAI,KAAK,WAAW,KAAK,IAAI,SAAS,GAAG;AACvC,eAAK,IAAI,CAAC,IAAI,SAAU,KAAM;AAC5B,iBAAK,WAAW;AAChB,mBAAO;AAAA,UACT;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAGA,aAAS,aAAa,KAAK;AACzB,UAAI,IAAI,KAAK,YAAY,KAAK;AAC9B,UAAI,IAAI,oBAAoB,MAAM,KAAK,CAAC;AACxC,UAAI,MAAM,OAAW,QAAO;AAC5B,UAAI,KAAK,YAAY,IAAI,QAAQ;AAC/B,YAAI,KAAK,KAAK,UAAU,GAAG,GAAG,KAAK,QAAQ;AAC3C,eAAO,KAAK,SAAS,SAAS,KAAK,UAAU,GAAG,KAAK,SAAS;AAAA,MAChE;AACA,UAAI,KAAK,KAAK,UAAU,GAAG,GAAG,IAAI,MAAM;AACxC,WAAK,YAAY,IAAI;AAAA,IACvB;AAKA,aAAS,SAAS,KAAK,GAAG;AACxB,UAAI,QAAQ,oBAAoB,MAAM,KAAK,CAAC;AAC5C,UAAI,CAAC,KAAK,SAAU,QAAO,IAAI,SAAS,QAAQ,CAAC;AACjD,WAAK,YAAY;AACjB,UAAI,MAAM,IAAI,UAAU,QAAQ,KAAK;AACrC,UAAI,KAAK,KAAK,UAAU,GAAG,GAAG;AAC9B,aAAO,IAAI,SAAS,QAAQ,GAAG,GAAG;AAAA,IACpC;AAIA,aAAS,QAAQ,KAAK;AACpB,UAAI,IAAI,OAAO,IAAI,SAAS,KAAK,MAAM,GAAG,IAAI;AAC9C,UAAI,KAAK,SAAU,QAAO,IAAI;AAC9B,aAAO;AAAA,IACT;AAMA,aAAS,UAAU,KAAK,GAAG;AACzB,WAAK,IAAI,SAAS,KAAK,MAAM,GAAG;AAC9B,YAAI,IAAI,IAAI,SAAS,WAAW,CAAC;AACjC,YAAI,GAAG;AACL,cAAI,IAAI,EAAE,WAAW,EAAE,SAAS,CAAC;AACjC,cAAI,KAAK,SAAU,KAAK,OAAQ;AAC9B,iBAAK,WAAW;AAChB,iBAAK,YAAY;AACjB,iBAAK,SAAS,CAAC,IAAI,IAAI,IAAI,SAAS,CAAC;AACrC,iBAAK,SAAS,CAAC,IAAI,IAAI,IAAI,SAAS,CAAC;AACrC,mBAAO,EAAE,MAAM,GAAG,EAAE;AAAA,UACtB;AAAA,QACF;AACA,eAAO;AAAA,MACT;AACA,WAAK,WAAW;AAChB,WAAK,YAAY;AACjB,WAAK,SAAS,CAAC,IAAI,IAAI,IAAI,SAAS,CAAC;AACrC,aAAO,IAAI,SAAS,WAAW,GAAG,IAAI,SAAS,CAAC;AAAA,IAClD;AAIA,aAAS,SAAS,KAAK;AACrB,UAAI,IAAI,OAAO,IAAI,SAAS,KAAK,MAAM,GAAG,IAAI;AAC9C,UAAI,KAAK,UAAU;AACjB,YAAI,MAAM,KAAK,YAAY,KAAK;AAChC,eAAO,IAAI,KAAK,SAAS,SAAS,WAAW,GAAG,GAAG;AAAA,MACrD;AACA,aAAO;AAAA,IACT;AAEA,aAAS,WAAW,KAAK,GAAG;AAC1B,UAAI,KAAK,IAAI,SAAS,KAAK;AAC3B,UAAI,MAAM,EAAG,QAAO,IAAI,SAAS,UAAU,CAAC;AAC5C,WAAK,WAAW,IAAI;AACpB,WAAK,YAAY;AACjB,UAAI,MAAM,GAAG;AACX,aAAK,SAAS,CAAC,IAAI,IAAI,IAAI,SAAS,CAAC;AAAA,MACvC,OAAO;AACL,aAAK,SAAS,CAAC,IAAI,IAAI,IAAI,SAAS,CAAC;AACrC,aAAK,SAAS,CAAC,IAAI,IAAI,IAAI,SAAS,CAAC;AAAA,MACvC;AACA,aAAO,IAAI,SAAS,UAAU,GAAG,IAAI,SAAS,CAAC;AAAA,IACjD;AAEA,aAAS,UAAU,KAAK;AACtB,UAAI,IAAI,OAAO,IAAI,SAAS,KAAK,MAAM,GAAG,IAAI;AAC9C,UAAI,KAAK,SAAU,QAAO,IAAI,KAAK,SAAS,SAAS,UAAU,GAAG,IAAI,KAAK,QAAQ;AACnF,aAAO;AAAA,IACT;AAGA,aAAS,YAAY,KAAK;AACxB,aAAO,IAAI,SAAS,KAAK,QAAQ;AAAA,IACnC;AAEA,aAAS,UAAU,KAAK;AACtB,aAAO,OAAO,IAAI,SAAS,KAAK,MAAM,GAAG,IAAI;AAAA,IAC/C;AAAA;AAAA;", "names": []}