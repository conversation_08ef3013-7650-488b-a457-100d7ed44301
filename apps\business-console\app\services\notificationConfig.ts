import { ApiResponse } from "~/types/api/Api";
import { API_BASE_URL, apiRequest } from "~/utils/api";

export interface NotificationConfig {
  id?: number;
  sellerId: number;
  type: string;
  notificationTemplateId?: number;
  waMobileNumbers: string;
  saMobileNumbers: string;
  waEnabled: boolean;
  saEnabled: boolean;
  createdAt?: string;
  updatedAt?: string;
}

export interface CreateNotificationConfigRequest {
  sellerId: number;
  type: string;
  notificationTemplateId?: number;
  waMobileNumbers: string;
  saMobileNumbers: string;
  waEnabled: boolean;
  saEnabled: boolean;
}

export interface UpdateNotificationConfigRequest extends CreateNotificationConfigRequest {
  id: number;
}

export interface NetworkNotificationConfig {
  id?: number;
  networkId: number;
  type: string;
  notificationTemplateId?: number;
  waEnabled: boolean;
  baEnabled: boolean;
  createdAt?: string;
  updatedAt?: string;
  disabled: boolean
}

export interface CreateNetworkNotificationConfigRequest {
  networkId: number;
  type: string;
  notificationTemplateId?: number;
  waEnabled: boolean;
  baEnabled: boolean;
}

export interface UpdateNetworkNotificationConfigRequest extends CreateNetworkNotificationConfigRequest {
  id: number;
}

export interface WhatsappTokenType {
  id: number;
  networkDomainId: number;
  token: string;
  expiryTime: number;
  wabMobileNumber: string;
  wabPhoneNumberId: string;
}

// Get All Seller Notification Configs
export const getSellerNotificationConfigs = async (sellerId: number, request: Request): Promise<ApiResponse<NotificationConfig[]>> => {
  const response = await apiRequest<{ message: string; success: boolean; sellerConfigs: NotificationConfig[] }>(
    `${API_BASE_URL}/api/notification-config/seller/${sellerId}`,
    "GET",
    undefined,
    {},
    true,
    request
  );

  // Extract the sellerConfigs from the response
  const notificationConfigs = response?.data?.sellerConfigs || [];

  return {
    data: notificationConfigs,
    success: response?.data?.success || false,
    status: response?.status || 200,
    headers: response?.headers || new Headers()
  };
};

// Get Seller Notification Config by Type
export const getSellerNotificationConfigByType = async (sellerId: number, type: string, request: Request): Promise<ApiResponse<NotificationConfig>> => {
  const response = await apiRequest<{ message: string; success: boolean; sellerConfig?: NotificationConfig }>(
    `${API_BASE_URL}/api/notification-config/seller/${sellerId}/type/${type}`,
    "GET",
    undefined,
    {},
    true,
    request
  );

  // Extract the sellerConfig from the response
  const notificationConfig = response?.data?.sellerConfig;

  return {
    data: notificationConfig,
    success: response?.data?.success || false,
    status: response?.status || 200,
    headers: response?.headers || new Headers()
  };
};

// Create Seller Notification Config
export const createSellerNotificationConfig = async (data: CreateNotificationConfigRequest, request: Request): Promise<ApiResponse<NotificationConfig>> => {
  const response = await apiRequest<{ message: string; success: boolean; sellerConfig?: NotificationConfig }>(
    `${API_BASE_URL}/api/notification-config/seller`,
    "POST",
    data,
    {},
    true,
    request
  );

  // Extract the sellerConfig from the response
  const notificationConfig = response?.data?.sellerConfig;

  return {
    data: notificationConfig,
    success: response?.data?.success || false,
    status: response?.status || 200,
    headers: response?.headers || new Headers()
  };
};

// Update Seller Notification Config
export const updateSellerNotificationConfig = async (data: UpdateNotificationConfigRequest, request: Request): Promise<ApiResponse<NotificationConfig>> => {
  const response = await apiRequest<{ message: string; success: boolean; sellerConfig?: NotificationConfig }>(
    `${API_BASE_URL}/api/notification-config/seller`,
    "PUT",
    data,
    {},
    true,
    request
  );

  // Extract the sellerConfig from the response
  const notificationConfig = response?.data?.sellerConfig;

  return {
    data: notificationConfig,
    success: response?.data?.success || false,
    status: response?.status || 200,
    headers: response?.headers || new Headers()
  };
};

// Delete Seller Notification Config
export const deleteSellerNotificationConfig = async (sellerId: number, type: string, request: Request): Promise<ApiResponse<void>> => {
  const response = await apiRequest<{ message: string; success: boolean }>(
    `${API_BASE_URL}/api/notification-config/seller/${sellerId}/type/${type}`,
    "DELETE",
    undefined,
    {},
    true,
    request
  );

  return {
    data: undefined,
    success: response?.data?.success || false,
    status: response?.status || 200,
    headers: response?.headers || new Headers()
  };
};


// Get All Network Notification Configs
export const getNetworkNotificationConfigs = async (networkId: number, request: Request): Promise<ApiResponse<NetworkNotificationConfig[]>> => {
  const response = await apiRequest<{ message: string; success: boolean; networkConfigs: NetworkNotificationConfig[] }>(
    `${API_BASE_URL}/api/notification-config/network/${networkId}`,
    "GET",
    undefined,
    {},
    true,
    request
  );

  // Extract the networkConfigs from the response
  const notificationConfigs = response?.data?.networkConfigs || [];

  return {
    data: notificationConfigs,
    success: response?.data?.success || false,
    status: response?.status || 200,
    headers: response?.headers || new Headers()
  };
};

// Create Network Notification Config
export const createNetworkNotificationConfig = async (data: CreateNetworkNotificationConfigRequest, request: Request): Promise<ApiResponse<NetworkNotificationConfig>> => {
  const response = await apiRequest<{ message: string; success: boolean; networkConfig?: NetworkNotificationConfig }>(
    `${API_BASE_URL}/api/notification-config/network`,
    "POST",
    data,
    {},
    true,
    request
  );

  // Extract the networkConfig from the response
  const notificationConfig = response?.data?.networkConfig;

  return {
    data: notificationConfig,
    success: response?.data?.success || false,
    status: response?.status || 200,
    headers: response?.headers || new Headers()
  };
};

// Update Network Notification Config
export const updateNetworkNotificationConfig = async (data: UpdateNetworkNotificationConfigRequest, request: Request): Promise<ApiResponse<NetworkNotificationConfig>> => {
  const response = await apiRequest<{ message: string; success: boolean; networkConfig?: NetworkNotificationConfig }>(
    `${API_BASE_URL}/api/notification-config/network`,
    "PUT",
    data,
    {},
    true,
    request
  );

  // Extract the networkConfig from the response
  const notificationConfig = response?.data?.networkConfig;

  return {
    data: notificationConfig,
    success: response?.data?.success || false,
    status: response?.status || 200,
    headers: response?.headers || new Headers()
  };
};

// Delete Network Notification Config
export const deleteNetworkNotificationConfig = async (networkId: number, type: string, request: Request): Promise<ApiResponse<void>> => {
  const response = await apiRequest<{ message: string; success: boolean }>(
    `${API_BASE_URL}/api/notification-config/network/${networkId}/type/${type}`,
    "DELETE",
    undefined,
    {},
    true,
    request
  );
  return {
    data: undefined,
    success: response?.data?.success || false,
    status: response?.status || 200,
    headers: response?.headers || new Headers()
  };
};

export const getWabToken = async (networkDomainId: number, request: Request): Promise<ApiResponse<WhatsappTokenType>> => {
  try {
    const response = await apiRequest<{ message: string; success: boolean; wabToken?: WhatsappTokenType }>(
      `${API_BASE_URL}/api/notification-config/wab-token/${networkDomainId}`,
      "GET",
      undefined,
      {},
      true,
      request
    );
    return {
      data: response?.data?.wabToken,
      success: response?.data?.success || false,
      status: response?.status || 200,
      headers: response?.headers || new Headers()
    };
  } catch (error) {
    return {
      data: undefined,
      success: false,
      status: 200,
      headers: new Headers()
    };
  }
};

export const updateWabToken = async (networkDomainId: number, data: WhatsappTokenType, request: Request): Promise<ApiResponse<WhatsappTokenType>> => {
  const response = await apiRequest<{ message: string; success: boolean; wabToken?: WhatsappTokenType }>(
    `${API_BASE_URL}/api/notification-config/wab-token/${networkDomainId}`,
    "PUT",
    data,
    {},
    true,
    request
  );
  return {
    data: response?.data?.wabToken,
    success: response?.data?.success || false,
    status: response?.status || 200,
    headers: response?.headers || new Headers()
  };
};