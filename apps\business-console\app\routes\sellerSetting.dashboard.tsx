import { useLoaderData } from "@remix-run/react";
import { metabaseService } from "../utils/metabase";
import { withAuth, withResponse } from "../utils/auth-utils";
import { AreaChart, Area, PieChart, Pie, Cell, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer } from 'recharts';
import { AlertCircle, DollarSign, ShoppingCart, Clock, Package, TrendingUp, TrendingDown } from "lucide-react";
import { Badge } from "~/components/ui/badge";
import { Card, CardContent, CardHeader, CardTitle } from "~/components/ui/card";
import { Button } from "~/components/ui/button";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "~/components/ui/tabs";
import { useState } from "react";
import CustomerHeatMap from "~/components/ui/CustomerHeatMap";
import { ComingSoonOverlay } from "~/components/modals/ComingSoonOverlay";
import { getServerCookie } from "~/utils/cookie";

export const loader = withAuth(async ({ user, request }) => {
  let sellerId = user.sellerId;
  const selectedSeller = getServerCookie(request, "selectedSeller");
  if (selectedSeller) {
    sellerId = parseInt(selectedSeller);
  }
  const embedUrl = metabaseService.generateDashboardUrl(10, {
    id: sellerId,
  });

  return withResponse({ embedUrl });
});

const mockDashboardData = {
  kpis: {
    grossSales: { value: 45680, change: 12.5, period: "today" },
    orders: { value: 127, change: 8.3, period: "today" },
    aov: { value: 359, change: -2.1, period: "today" },
    netMargin: { value: 23.5, change: 1.8, period: "today" },
    refunds: { value: 1280, change: -5.2, period: "today" },
    fulfilmentSLA: { value: 18, change: -12.3, period: "avg min" }
  },
  channelMix: [
    { name: "Website", value: 45, orders: 57 },
    { name: "WhatsApp", value: 35, orders: 44 },
    { name: "Instagram", value: 12, orders: 15 },
    { name: "Paid Ads", value: 8, orders: 11 }
  ],
  topSellers: [
    { name: "Margherita Pizza", units: 23, sales: 1840, outlet: "Downtown" },
    { name: "Chicken Biryani", units: 18, sales: 2160, outlet: "Mall Road" },
    { name: "Veg Burger", units: 16, sales: 1280, outlet: "Downtown" },
    { name: "Pasta Alfredo", units: 14, sales: 1540, outlet: "Mall Road" },
    { name: "Caesar Salad", units: 12, sales: 960, outlet: "Downtown" }
  ],
  lowStockAlerts: [
    { item: "Mozzarella Cheese", remaining: 2, critical: "Downtown, Mall Road" },
    { item: "Chicken Breast", remaining: 5, critical: "Mall Road" },
    { item: "Basil Leaves", remaining: 1, critical: "Downtown" }
  ],
  liveOrders: {
    unaccepted: 3,
    preparing: 8,
    ready: 5
  },
  salesTrend: [
    { time: "6 AM", sales: 1200 },
    { time: "9 AM", sales: 3400 },
    { time: "12 PM", sales: 8900 },
    { time: "3 PM", sales: 6700 },
    { time: "6 PM", sales: 12400 },
    { time: "9 PM", sales: 15600 },
    { time: "12 AM", sales: 8900 }
  ]
};

const COLORS = ['#00A390', '#DB3532', '#28a745', '#ffc107'];

// Add radius filter parameter to API call: /api/analytics/heatmap?radiusKm=5
const mockHeatmapData = {
  shop: {
    id: "shop-1",
    name: "Downtown Restaurant",
    lat: 12.9716,
    lng: 77.5946
  },
  radius: 5,
  customers: [
    { id: "c1", name: "Rajesh Kumar", lat: 12.9750, lng: 77.5980, orderCount: 15, lastOrderDate: "2024-01-20" },
    { id: "c2", name: "Priya Sharma", lat: 12.9680, lng: 77.5920, orderCount: 8, lastOrderDate: "2024-01-19" },
    { id: "c3", name: "Amit Singh", lat: 12.9800, lng: 77.6000, orderCount: 3, lastOrderDate: "2024-01-18" },
    { id: "c4", name: "Sneha Patel", lat: 12.9650, lng: 77.5880, orderCount: 12, lastOrderDate: "2024-01-21" },
    { id: "c5", name: "Vikram Reddy", lat: 12.9780, lng: 77.5850, orderCount: 6, lastOrderDate: "2024-01-17" },
    { id: "c6", name: "Anita Gupta", lat: 12.9720, lng: 77.6020, orderCount: 2, lastOrderDate: "2024-01-16" },
    { id: "c7", name: "Rohit Jain", lat: 12.9690, lng: 77.5960, orderCount: 9, lastOrderDate: "2024-01-22" },
    { id: "c8", name: "Kavya Nair", lat: 12.9760, lng: 77.5900, orderCount: 14, lastOrderDate: "2024-01-23" },
    { id: "c9", name: "Suresh Yadav", lat: 12.9640, lng: 77.5940, orderCount: 4, lastOrderDate: "2024-01-15" },
    { id: "c10", name: "Meera Iyer", lat: 12.9810, lng: 77.5970, orderCount: 7, lastOrderDate: "2024-01-14" },
    { id: "c11", name: "Arjun Mehta", lat: 12.9700, lng: 77.5820, orderCount: 11, lastOrderDate: "2024-01-24" },
    { id: "c12", name: "Deepika Rao", lat: 12.9730, lng: 77.6040, orderCount: 5, lastOrderDate: "2024-01-13" },
    { id: "c13", name: "Kiran Kumar", lat: 12.9820, lng: 77.5890, orderCount: 1, lastOrderDate: "2024-01-12" },
    { id: "c14", name: "Pooja Agarwal", lat: 12.9660, lng: 77.5860, orderCount: 13, lastOrderDate: "2024-01-25" },
    { id: "c15", name: "Manoj Tiwari", lat: 12.9790, lng: 77.6010, orderCount: 8, lastOrderDate: "2024-01-11" }
  ]
};

export default function Dashboard() {
  const { embedUrl } = useLoaderData<typeof loader>();
  const [selectedTab, setSelectedTab] = useState("dashboard");

  return (
    <div className="p-6">
      <div className="mb-6">
        <h1 className="text-3xl font-bold text-gray-900">Dashboard</h1>
        <p className="text-gray-600 mt-2">Overview of your restaurant performance</p>
      </div>

      <Tabs value={selectedTab} onValueChange={setSelectedTab}>
        <TabsList className="grid grid-cols-2 w-full">
          <TabsTrigger value="dashboard" className="font-semibold">Dashboard</TabsTrigger>
          <TabsTrigger value="heatmap" className="font-semibold">Customer HeatMap</TabsTrigger>
        </TabsList>
        <TabsContent value="dashboard">
          <div className="w-full h-screen">
            {embedUrl ? (
              <iframe
                id="metabase-iframe"
                src={embedUrl}
                title="Restaurant Dashboard"
                className="w-full h-full border-0"
              />
            ) : (
              <div className="space-y-5">
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                  <Card className="flex flex-col gap-5">
                    <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                      <CardTitle className="text-sm font-semibold">Gross Sales</CardTitle>
                      <DollarSign className="h-4 w-4 text-muted-foreground" />
                    </CardHeader>
                    <CardContent>
                      <div className="text-2xl font-bold">₹{mockDashboardData.kpis.grossSales.value.toLocaleString()}</div>
                      <div className="flex items-center text-xs text-muted-foreground">
                        {mockDashboardData.kpis.grossSales.change > 0 ? (
                          <>
                            <TrendingUp className="h-4 w-4 text-green-600 mr-1" />
                            <span className="text-green-600">+{mockDashboardData.kpis.grossSales.change}%</span>
                          </>
                        ) : (
                          <>
                            <TrendingDown className="h-4 w-4 text-red-600 mr-1" />
                            <span className="text-red-600">{mockDashboardData.kpis.grossSales.change}%</span>
                          </>
                        )}
                        <span className="ml-1">from yesterday</span>
                      </div>
                    </CardContent>
                  </Card>

                  <Card className="flex flex-col gap-5">
                    <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                      <CardTitle className="text-sm font-semibold">Orders</CardTitle>
                      <ShoppingCart className="h-4 w-4 text-muted-foreground" />
                    </CardHeader>
                    <CardContent>
                      <div className="text-2xl font-bold">{mockDashboardData.kpis.orders.value}</div>
                      <div className="flex items-center text-xs text-muted-foreground">
                        <TrendingUp className="h-4 w-4 text-green-600 mr-1" />
                        <span className="text-green-600">+{mockDashboardData.kpis.orders.change}%</span>
                        <span className="ml-1">from yesterday</span>
                      </div>
                    </CardContent>
                  </Card>

                  <Card className="flex flex-col gap-5">
                    <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                      <CardTitle className="text-sm font-semibold">Average Order Value</CardTitle>
                      <DollarSign className="h-4 w-4 text-muted-foreground" />
                    </CardHeader>
                    <CardContent>
                      <div className="text-2xl font-bold">₹{mockDashboardData.kpis.aov.value}</div>
                      <div className="flex items-center text-xs text-muted-foreground">
                        <TrendingDown className="h-4 w-4 text-red-600 mr-1" />
                        <span className="text-red-600">{mockDashboardData.kpis.aov.change}%</span>
                        <span className="ml-1">from yesterday</span>
                      </div>
                    </CardContent>
                  </Card>

                  <Card className="flex flex-col gap-5">
                    <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                      <CardTitle className="text-sm font-semibold">Net Margin</CardTitle>
                      <TrendingUp className="h-4 w-4 text-muted-foreground" />
                    </CardHeader>
                    <CardContent>
                      <div className="text-2xl font-bold">{mockDashboardData.kpis.netMargin.value}%</div>
                      <div className="flex items-center text-xs text-muted-foreground">
                        <TrendingUp className="h-4 w-4 text-green-600 mr-1" />
                        <span className="text-green-600">+{mockDashboardData.kpis.netMargin.change}%</span>
                        <span className="ml-1">from yesterday</span>
                      </div>
                    </CardContent>
                  </Card>

                  <Card className="flex flex-col gap-5">
                    <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                      <CardTitle className="text-sm font-semibold">Fulfilment SLA</CardTitle>
                      <Clock className="h-4 w-4 text-muted-foreground" />
                    </CardHeader>
                    <CardContent>
                      <div className="text-2xl font-bold">{mockDashboardData.kpis.fulfilmentSLA.value} min</div>
                      <div className="flex items-center text-xs text-muted-foreground">
                        <TrendingUp className="h-4 w-4 text-green-600 mr-1" />
                        <span className="text-green-600">{mockDashboardData.kpis.fulfilmentSLA.change}%</span>
                        <span className="ml-1">improvement</span>
                      </div>
                    </CardContent>
                  </Card>

                  <Card className="flex flex-col gap-5">
                    <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                      <CardTitle className="text-sm font-semibold">Live Orders</CardTitle>
                      <Package className="h-4 w-4 text-muted-foreground" />
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-2">
                        <div className="flex justify-between text-sm">
                          <span>Unaccepted</span>
                          <Badge variant="destructive">{mockDashboardData.liveOrders.unaccepted}</Badge>
                        </div>
                        <div className="flex justify-between text-sm">
                          <span>Preparing</span>
                          <Badge variant="secondary">{mockDashboardData.liveOrders.preparing}</Badge>
                        </div>
                        <div className="flex justify-between text-sm">
                          <span>Ready</span>
                          <Badge className="bg-green-600">{mockDashboardData.liveOrders.ready}</Badge>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                </div>

                {/* Charts Row */}
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                  {/* Sales Trend */}
                  <Card>
                    <CardHeader>
                      <CardTitle className="text-sm font-normal">Today's Sales Trend</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <ResponsiveContainer width="100%" height={300}>
                        <AreaChart data={mockDashboardData.salesTrend}>
                          <CartesianGrid strokeDasharray="3 3" />
                          <XAxis dataKey="time" />
                          <YAxis />
                          <Tooltip formatter={(value) => [`₹${value}`, 'Sales']} />
                          <Area type="monotone" dataKey="sales" stroke="#00A390" fill="#00A390" fillOpacity={0.3} />
                        </AreaChart>
                      </ResponsiveContainer>
                    </CardContent>
                  </Card>

                  {/* Channel Mix */}
                  <Card>
                    <CardHeader>
                      <CardTitle className="text-sm font-normal">Channel Mix (Orders)</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <ResponsiveContainer width="100%" height={300}>
                        <PieChart>
                          <Pie
                            data={mockDashboardData.channelMix}
                            cx="50%"
                            cy="50%"
                            outerRadius={100}
                            fill="#8884d8"
                            dataKey="orders"
                            label={({ name, value }) => `${name}: ${value}`}
                          >
                            {mockDashboardData.channelMix.map((entry, index) => (
                              <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                            ))}
                          </Pie>
                          <Tooltip />
                        </PieChart>
                      </ResponsiveContainer>
                    </CardContent>
                  </Card>
                </div>

                {/* Bottom Row */}
                <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
                  {/* Top Sellers */}
                  <Card className="lg:col-span-2">
                    <CardHeader>
                      <CardTitle className="text-sm font-normal">Top Sellers Today</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-4">
                        {mockDashboardData.topSellers.map((item, index) => (
                          <div key={index} className="flex items-center justify-between">
                            <div className="flex items-center gap-3">
                              <div className="w-8 h-8 bg-primary/10 rounded-full flex items-center justify-center">
                                <span className="text-primary font-medium">{index + 1}</span>
                              </div>
                              <div>
                                <p className="font-medium">{item.name}</p>
                                <p className="text-sm text-muted-foreground">{item.outlet}</p>
                              </div>
                            </div>
                            <div className="text-right">
                              <p className="font-medium">₹{item.sales.toLocaleString()}</p>
                              <p className="text-sm text-muted-foreground">{item.units} units</p>
                            </div>
                          </div>
                        ))}
                      </div>
                    </CardContent>
                  </Card>

                  {/* Low Stock Alerts */}
                  <Card>
                    <CardHeader>
                      <CardTitle className="flex items-center gap-2 text-sm font-normal">
                        <AlertCircle className="h-4 w-4 text-orange-500" />
                        Low Stock Alerts
                      </CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-3">
                        {mockDashboardData.lowStockAlerts.map((alert, index) => (
                          <div key={index} className="p-3 border rounded-lg bg-orange-50 border-orange-200">
                            <div className="flex items-center justify-between mb-2">
                              <p className="font-medium text-sm">{alert.item}</p>
                              <Badge variant="outline" className="text-orange-600 border-orange-300">
                                {alert.remaining} left
                              </Badge>
                            </div>
                            <p className="text-xs text-muted-foreground">{alert.critical}</p>
                            <Button size="sm" variant="outline" className="w-full mt-2">
                              Restock
                            </Button>
                          </div>
                        ))}
                      </div>
                    </CardContent>
                  </Card>
                </div>
              </div>
            )}
          </div>
        </TabsContent>
        <TabsContent value="heatmap">
          <div>
            {/* Heatmap */}
            <Card>
              <CardHeader>
                <CardTitle className="text-sm font-normal">Customer Heatmap</CardTitle>
                <p className="text-sm text-muted-foreground">
                  Visualize customer locations around your restaurant with radius filtering
                </p>
              </CardHeader>
              <CardContent>
                <CustomerHeatMap
                  shop={mockHeatmapData.shop}
                  customers={mockHeatmapData.customers}
                  radiusKm={mockHeatmapData.radius}
                />
              </CardContent>
            </Card>
          </div>
        </TabsContent>
      </Tabs>
      {(selectedTab === "heatmap") && <ComingSoonOverlay />}
    </div>
  );
} 