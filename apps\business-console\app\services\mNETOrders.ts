import { ApiResponse } from "~/types/api/Api";
import { mNETAgent, mNETDriver, mNETOrder } from "~/types/api/businessConsoleService/mNETOrder";
import { API_BASE_URL, apiRequest } from "~/utils/api";

export async function getmNETOrders(
  request?: Request,
  queryParams?: string
): Promise<ApiResponse<{ orders: mNETOrder[], totalElements: number, totalPages: number, pageSize: number, currentPage: number }>> {
  const response = await apiRequest<{ orders: mNETOrder[], totalElements: number, totalPages: number, pageSize: number, currentPage: number }>(
    `${API_BASE_URL}/bc/seller/fmorders${queryParams ? '' + queryParams.toString() : ''}`,
    "GET",
    undefined,
    {},
    true,
    request
  );

  if (response) {
    return response;
  } else {
    throw new Response("Failed to fetch orders.");
  }
}

export async function getmNETAgents(
  request?: Request
): Promise<ApiResponse<mNETAgent[]>> {
  const response = await apiRequest<mNETAgent[]>(
    `${API_BASE_URL}/bc/seller/network/agents`,
    "GET",
    undefined,
    {},
    true,
    request
  );

  if (response) {
    return response;
  } else {
    throw new Response("Failed to fetch agents.");
  }
}

export async function getmNETDrivers(
  request?: Request
): Promise<ApiResponse<mNETDriver[]>> {
  const response = await apiRequest<mNETDriver[]>(
    `${API_BASE_URL}/bc/seller/drivers`,
    "GET",
    undefined,
    {},
    true,
    request
  );

  if (response) {
    return response;
  } else {
    throw new Response("Failed to fetch drivers.");
  }
}
