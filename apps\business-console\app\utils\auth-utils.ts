import { json, redirect } from "@remix-run/node";
import type { LoaderFunctionArgs, ActionFunctionArgs } from "@remix-run/node";
import { getSession } from "~/utils/session.server";
import { parseJWT } from "~/utils/token-utils";
import type { User } from "~/types";
import { Params } from "@remix-run/react";

export interface AuthenticatedRequest {
  user: User;
  request: Request;
  params?: Params<string>;
}

export const withAuth = <T>(
  handler: (args: AuthenticatedRequest) => Promise<T>
) => {
  return async (
    args: LoaderFunctionArgs | ActionFunctionArgs | Request
  ): Promise<T> => {
    const request = args instanceof Request ? args : args.request;
    const params = args instanceof Request ? undefined : args.params;

    const session = await getSession(request.headers.get("Cookie"));
    const token = session.get("access_token");

    if (!token) {
      const url = new URL(request.url);
      throw redirect(
        `/login?returnTo=${encodeURIComponent(url.pathname + url.search)}`
      );
    }

    const tokenData = parseJWT(token);
    const user: User = {
      userId: tokenData.userDetails.userId,
      userName: tokenData.userDetails.userName,
      businessName: tokenData.userDetails.businessName,
      buyerId: tokenData.userDetails.buyerId,
      sellerId: tokenData.userDetails.sellerId,
      platformId: tokenData.userDetails.platformId,
      userPermissions: tokenData.roles,
      userDetails: tokenData.userDetails,
    };

    return handler({ user, request, params });
  };
};

export const withResponse = <T>(data: T, headers?: Headers) => {
  return json(
    data,
    headers?.has("Set-Cookie")
      ? { headers: { "Set-Cookie": headers.get("Set-Cookie")! } }
      : undefined
  );
};
