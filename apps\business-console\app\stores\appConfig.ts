import { create } from 'zustand';
import type { NetworkTheme } from '~/types/api/common';

interface AppConfigStore {
  networkConfig: NetworkTheme | null;
  setNetworkConfig: (networkConfig: NetworkTheme | null) => void;
}

export const useAppConfigStore = create<AppConfigStore>((set) => ({
  networkConfig: null,
  setNetworkConfig: (config) =>
    set((state) => ({
      ...state,
      networkConfig: config,
    })),
}));
