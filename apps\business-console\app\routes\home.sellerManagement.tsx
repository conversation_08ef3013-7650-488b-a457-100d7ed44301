'use client'

import { ActionFunction, json } from "@remix-run/node";
import { use<PERSON><PERSON><PERSON>, useLoaderData, useNavigate, Link } from "@remix-run/react";
import { getSellers } from "~/services/businessConsoleService";
import { Seller } from "~/types/api/businessConsoleService/SellerManagement";
import { withAuth, withResponse } from "~/utils/auth-utils";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "~/components/ui/table";
import { Button } from "~/components/ui/button";
import { useEffect, useState } from "react";
import { createSeller, createSellerPushMenu, updateSellerStatus } from "~/services/masterItemCategories";
import CreateSeller from "~/components/ui/createSeller";
import { useToast } from "~/components/ui/ToastProvider";
import { Switch } from "~/components/ui/switch";
import { Input } from "~/components/ui/input";
import { useDebounce } from "~/hooks/useDebounce";
import { Search } from "lucide-react";
import SpinnerLoader from "~/components/loader/SpinnerLoader";
interface LoaderData {
  sellerData: Seller[],
}
export interface ActionData {
  success?: boolean,
  error?: string
}
export const loader = withAuth(async ({ request }) => {
  try {
    const response = await getSellers(request);
    // Transform roleData into an array of `{ value, label }` objects
    return withResponse({
      sellerData: response.data,
    }, response.headers);
  } catch (error) {
    console.error("Error in loader:", error);
    // Return a JSON-based error shape
    throw new Response("failed to get sellers", { status: 500 })
  }
});
export const action: ActionFunction = async ({ request }) => {
  const formData = await request.formData();
  const actionType = formData.get("actionType");
  const sellerId = Number(formData.get("sellerId"));

  if (actionType === "updateSellerStatus") {
    try {
      const response = await updateSellerStatus(sellerId, request);
      return withResponse({ data: response.data }, response.headers);
    } catch (error) {
      if (error instanceof Response && error.status === 404) {
        throw json({ error: "update Seller Status page Not found" }, { status: 404 });
      }
      throw new Response("Failed to update Seller Status", { status: 500 });
    }
  } else if (actionType === "createNewSeller") {
    const seller = {
      name: formData.get("name"),
      address: formData.get("address"),
      email: formData.get("email"),
      customerSupportNumber: formData.get("customerSupportNumber"),
      owner: {
        firstName: formData.get("owner.firstName"),
        lastName: formData.get("owner.lastName"),
        email: formData.get("owner.email"),
        mobileNumber: formData.get("owner.mobileNumber"),
        address: formData.get("owner.address"),
        roles: formData.getAll("roles"),
      },
      latitude: formData.get("latitude"),
      longitude: formData.get("longitude"),
      ondcDomain: formData.get("ondcDomain") as "RET10" | "RET11",
      pincode: formData.get("ondcDomain") === "RET11" ? formData.get("pincode") : "",
      miSource: formData.get("ondcDomain") === "RET11" ? "rnet" : "mnet",
    };
    try {
      const response = await createSeller(seller, request);
      return withResponse({ success: response.statusCode === 200 }, response.headers);
    } catch (error) {
      if (error instanceof Response && error.status === 404) {
        throw json({ error: "create Seller page Not found", success: false }, { status: 404 });
      }
      return json({ success: false, error: "Failed to create Seller" }, { status: 500 });
    }
  }
  else if (actionType === "pushMenu") {
    console.log("ooooooooooooooo")

    try {
      const response = await createSellerPushMenu(sellerId, request);
      return withResponse({ success: response.statusCode === 200 }, response.headers);
    } catch (error) {
      if (error instanceof Response && error.status === 404) {
        throw json({ error: "create Seller page Not found", success: false }, { status: 404 });
      }
      return json({ success: false, error: "Failed to create Seller" }, { status: 500 });
    }
  }
  return json({ success: false, error: "Invalid action type" });
};
export default function SellerManagementPage() {
  const { showToast } = useToast()
  const { sellerData } = useLoaderData<LoaderData>();
  const dataleng = sellerData.length;
  const [isModalOpen, setIsModalOpen] = useState(false);
  const goTo = useNavigate();
  const [isSuccess, setIsSuccess] = useState(false);

  const [sellerStates, setSellerStates] = useState<Record<number, boolean>>(() => {
    return Array.isArray(sellerData)
      ? sellerData.reduce((acc, seller) => {
        acc[seller.id] = seller.enabled;
        return acc;
      }, {} as Record<number, boolean>)
      : {}; // Return an empty object if sellerData is not an array
  });

  const fetcher = useFetcher<{ sellerData: Seller[] }>();

  const handleSwitch = async (sellerId: number) => {
    // Optimistically toggle the switch
    setSellerStates((prev) => ({
      ...prev,
      [sellerId]: !prev[sellerId], // Toggle status before request completes
    }));

    const formData = new FormData();
    formData.append("sellerId", sellerId.toString());
    formData.append("actionType", "updateSellerStatus");

    try {
      await fetcher.submit(formData, { method: "put" });
      if (fetcher.state === "idle") {
        showToast("sellerStatus Updated SuccessFully", "success")
      }
    } catch (error) {
      console.error("Failed to update status:", error);

      // ❌ Revert the toggle if the update fails
      setSellerStates((prev) => ({
        ...prev,
        [sellerId]: !prev[sellerId],
      }));
    }
  };


  const handleSubmitPushMenu = (row: number) => {
    console.log(",,,,,,,,,,,,,,,,,,")
    const formData = new FormData();
    setUniQId(row)
    formData.append("actionType", "pushMenu");
    formData.append("sellerId", row.toString());
    fetcher.submit(formData, { method: 'POST' })
  }
  useEffect(() => {
    if (fetcher.state === "idle" && fetcher.data && "sellerData" in fetcher.data) {
      const data = fetcher.data as { sellerData: Seller[] };
      setSellerStates((prev) =>
        (data.sellerData || []).reduce((acc, seller) => {
          return acc;
        }, {} as Record<number, boolean>)
      );
    }
  }, [fetcher.state, fetcher.data]);
  const [searchTerm, setSearchTerm] = useState('');
  const [sellerFilter, setSellerFilter] = useState<Seller[]>([]);
  const handleSearch = (val: string) => {
    setSearchTerm(val)
  }

  const [uniQId, setUniQId] = useState(0);

  useEffect(() => {
    if (sellerData) {
      setSellerFilter(sellerData);
    }
  }, [sellerData]);

  useEffect(() => {
    if (typeof searchTerm === "string" && searchTerm.length >= 3) {
      const filteredSellers = sellerData?.filter(
        (item) =>
          item?.name &&
          typeof item.name === "string" &&
          item.name.toLowerCase().includes(searchTerm.toLowerCase())
      );
      setSellerFilter(filteredSellers || []);
      console.log(filteredSellers, "Filtered Sellers");
    } else {
      setSellerFilter(sellerData || []);
      console.log(sellerData, "All Sellers");
    }
  }, [searchTerm, sellerData]);


  useEffect(() => {

    if (fetcher?.data?.success) {

      setIsSuccess(true)

      console.log(fetcher.data, "fetcherData.......")
      showToast("sucessFully Pushed Menu", "success")
    }
    else if (fetcher.data?.success === false) {
      setIsSuccess(false)
      showToast("Failed to Push Menu", "error")
    }

  }, [fetcher.data])


  const loading = fetcher.state !== "idle"



  return (
    <div className="container mx-auto p-6">
      {loading && <SpinnerLoader loading={loading} />}


      <h1 className="text-2xl font-bold mb-4">Seller Management</h1>
      <div className="flex flex-col my-3 relative ">
        <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
        <Input
          type='search'
          placeholder="Search by Seller Name"
          value={searchTerm}
          onChange={(e: { target: { value: string; }; }) => handleSearch(e.target.value)}
          className="max-w-sm rounded-full pl-10  "
          autoFocus
        />
      </div>
      <Table>
        <TableHeader className="bg-gray-100">
          <TableRow>
            <TableHead>ID</TableHead>
            <TableHead>Name</TableHead>
            <TableHead>Wallet Balance</TableHead>
            <TableHead>Stock Management</TableHead>
            <TableHead>Status</TableHead>
            <TableHead></TableHead>

          </TableRow>
        </TableHeader>

        <TableBody>

          {sellerFilter.length > 0 ? (
            sellerFilter.map((seller) => (
              <TableRow key={seller.id}>
                <TableCell>{seller.id}</TableCell>
                <TableCell className={`cursor-pointer text-blue-500 font-bold  items-center`}
                  onClick={() =>
                    goTo(`/home/<USER>
                  }  >{seller.name}</TableCell>

                <TableCell>{(seller.walletBalance !== null && seller.walletBalance !== undefined) ? seller.walletBalance : "-"}</TableCell>

                <TableCell>
                  <div className="flex gap-2">
                    <Link to={`/home/<USER>
                      <Button variant="outline" size="sm" className="hover:bg-primary hover:text-white transition-colors">
                        MyStock
                      </Button>
                    </Link>
                    <Link to={`/home/<USER>
                      <Button variant="outline" size="sm" className="hover:bg-primary hover:text-white transition-colors">
                        StockWithMe
                      </Button>
                    </Link>
                  </div>
                </TableCell>
                <TableCell>
                  <Switch
                    checked={sellerStates[seller.id]}  // Use sellerStates to track toggle status
                    onCheckedChange={() => handleSwitch(seller.id)}
                  />
                </TableCell>


                <TableCell>
                  <button
                    className={`
          px-6 py-2 rounded-full font-bold text-white
          ${(isSuccess && seller.id === uniQId) ? 'bg-green-600' : 'bg-blue-600'}
          hover:bg-blue-700
          disabled:bg-gray-400 disabled:cursor-not-allowed
        `}
                    onClick={() => handleSubmitPushMenu(seller?.id)}
                    disabled={loading}
                  >
                    <span className="flex items-center justify-center">
                      {(loading && seller.id === uniQId) ? (
                        <svg
                          className="h-5 w-5 mr-2 text-white"
                          xmlns="http://www.w3.org/2000/svg"
                          fill="none"
                          viewBox="0 0 24 24"
                        >
                          <circle
                            className="opacity-25"
                            cx="12"
                            cy="12"
                            r="10"
                            stroke="currentColor"
                            strokeWidth="4"
                          />
                          <path
                            className="opacity-75"
                            fill="currentColor"
                            d="M4 12a8 8 0 018-8v8h8a8 8 0 01-16 0z"
                          />
                        </svg>
                      ) : (isSuccess && seller.id === uniQId) ? (
                        <svg
                          className="h-5 w-5 mr-2"
                          xmlns="http://www.w3.org/2000/svg"
                          fill="none"
                          viewBox="0 0 24 24"
                          stroke="currentColor"
                        >
                          <path
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            strokeWidth="2"
                            d="M5 13l4 4L19 7"
                          />
                        </svg>
                      ) : null}
                      {(loading && seller.id === uniQId) ? 'Pushing...' : (isSuccess && seller.id === uniQId) ? 'Pushed!' : 'Push Menu'}
                    </span>
                  </button>
                </TableCell>
              </TableRow>
            ))
          ) : (
            <TableRow>
              <TableCell colSpan={6} className="h-24 text-center">
                No results.
              </TableCell>
            </TableRow>
          )}
        </TableBody>
      </Table>
      <Button className="fixed bottom-5 right-5 rounded-full cursor-pointer" onClick={() => setIsModalOpen(true)}>+ Add Seller</Button>

      <CreateSeller isOpen={isModalOpen} onClose={() => setIsModalOpen(false)} />
    </div>
  );
}