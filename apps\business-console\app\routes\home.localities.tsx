import { useRef, useState, useCallback, useEffect, useMemo } from "react";
import {
  useLoaderData,
  useNavigate,
  useLocation,
  useSearchParams,
  useActionData,
  Form,
  Outlet
} from "@remix-run/react";
import type { LoaderFunction, ActionFunction } from "@remix-run/node";
import { json, redirect } from "@remix-run/node";

// ----- Services (server-side safe) -----
import {
  getGlobalAreas,
  getDistrictsAndStates,
  updateArea,
  createArea,
  disableMasterLocality
} from "~/services/businessConsoleService";

// ----- Utils (server-side safe) -----
import { withAuth, withResponse } from "@utils/auth-utils";
// Use polylineCodec for encoding coordinates
import * as polylineCodec from "@googlemaps/polyline-codec";
// We'll continue using your decodePolygon function for decoding
import { decodePolygon } from "@utils/polyline-utils";

// ----- Types -----
import type { Seller<PERSON><PERSON> } from "~/types/api/businessConsoleService/Areas";



import {
  CreateAreaRequest,
  CreateAreaResponse,
  UpdateAreaRequest
} from "~/types/api/businessConsoleService/Areas";

// ----- UI & Components -----
import {
  GoogleMap,
  LoadScript,
  Polygon,
  InfoWindow,
  DrawingManager,
  Circle,
  Marker
} from "@react-google-maps/api";
import { Button } from "@components/ui/button";
import { Input } from "@components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from "@components/ui/select";
import { Pencil, Trash2, ArrowUpDown, Eye, EyeOff, X } from "lucide-react";

// ------------- Loader (server side) -------------
interface LoaderData {
  areas: SellerArea[];
  googleMapsApiKey: string;
  states: string[];
  districts: { [state: string]: string[] };
}

export const loader: LoaderFunction = withAuth(async ({ request, user }) => {
  const url = new URL(request.url);
  const stateParam = url.searchParams.get("state") || "Karnataka";
  const districtParam = url.searchParams.get("district") || "Bangalore";

  const [areasResponse, districtsAndStatesResponse] = await Promise.all([
    getGlobalAreas(user.userId, stateParam, districtParam, request),
    getDistrictsAndStates(user.userId, request)
  ]);

  const googleMapsApiKey = process.env.GOOGLE_MAPS_API_KEY || "";
  const areas = areasResponse.data;
  const districtsAndStates = districtsAndStatesResponse.data;

  const statesSet = new Set<string>();
  const districtsMap: { [state: string]: Set<string> } = {};

  districtsAndStates?.forEach((area: SellerArea) => {
    statesSet.add(area.state);
    if (!districtsMap[area.state]) {
      districtsMap[area.state] = new Set<string>();
    }
    districtsMap[area.state].add(area.district);
  });

  return withResponse({
    areas,
    googleMapsApiKey,
    states: Array.from(statesSet).sort(),
    districts: Object.fromEntries(
      Object.entries(districtsMap).map(([state, districtsSet]) => [
        state,
        Array.from(districtsSet).sort()
      ])
    )
  });
});

// ------------- Action (server side) -------------
// New: if the form field "action" equals "delete", call disableMasterLocality.
export const action: ActionFunction = withAuth(async ({ request, user }) => {
  const formData = await request.formData();
  const actionType = formData.get("action")?.toString();
  const id = Number(formData.get("id"));

  const name = formData.get("name")?.toString() || "";
  const state = formData.get("state")?.toString() || "";
  const district = formData.get("district")?.toString() || "";
  const polygon = formData.get("polygon")?.toString() || "";
  const mode = formData.get("mode")
  const radius = Number(formData.get("radius"));
  const latitude = Number(formData.get("latitude"));
  const longitude = Number(formData.get("longitude"));


  if (actionType === "delete") {
    try {
      await disableMasterLocality(id, request);
      return redirect(`/home/<USER>
    } catch (error) {
      return json({ error: "Failed to delete area" }, { status: 400 });
    }
  }


  if (id === 0) {
    const createReq: CreateAreaRequest = {
      name,
      state,
      district,
      polygon: polygon || "",
    };
    const createRadiusReq: any = {
      name,
      state,
      district,
      radius: radius,
      latitude: latitude,
      longitude: longitude
    };

    const finalCreateRequest = mode === "RadiusCreate" ? createRadiusReq : createReq;
    try {
      await createArea(user.userId, finalCreateRequest, request);
      return redirect(`/home/<USER>
    } catch (error) {
      return json({ error: "Failed to create area" }, { status: 400 });
    }
  } else {
    const updateReq: UpdateAreaRequest = {
      id,
      name,
      state,
      district,
      polygon: polygon || "",
    };
    const updateRadiusReq: any = {
      id,
      name,
      state,
      district,
      radius: radius,
      latitude: latitude,
      longitude: longitude
    };
    const finalUpdateRequest = mode === "RadiusUpdate" ? updateRadiusReq : updateReq;
    try {
      await updateArea(user.userId, finalUpdateRequest, request);
      return redirect(`/home/<USER>
    } catch (error) {
      return json({ error: "Failed to update area" }, { status: 400 });
    }
  }
});

// ------------- Helper: Deduplicate Coordinates -------------
// Receives an array of [lat, lng] pairs and returns a deduplicated array.
function deduplicateCoordinates(coords: number[][]): number[][] {
  if (coords.length === 0) return coords;
  const deduped = [coords[0]];
  for (let i = 1; i < coords.length; i++) {
    const prev = deduped[deduped.length - 1];
    const curr = coords[i];
    if (prev[0] === curr[0] && prev[1] === curr[1]) continue;
    deduped.push(curr);
  }
  return deduped;
}

// ------------- Constants & Helpers -------------
const BANGALORE_CENTER = { lat: 12.9716, lng: 77.5946 };
const MAP_CONTAINER_STYLE = { width: "100%", height: "100%" };

function getPolygonColor(index: number) {
  const colors = [
    "#3b82f6",
    "#10b981",
    "#f59e0b",
    "#ef4444",
    "#8b5cf6",
    "#ec4899",
    "#06b6d4",
    "#f97316"
  ];
  return colors[index % colors.length];
}

// ------------- Main Component -------------
export default function LocalitiesSection() {
  const location = useLocation();
  const navigate = useNavigate();
  const [searchParams, setSearchParams] = useSearchParams();
  const actionData = useActionData<{ error?: string }>();

  const { areas: initialAreas, googleMapsApiKey, states, districts } =
    useLoaderData<LoaderData>();

  const [localAreas, setLocalAreas] = useState<SellerArea[]>(initialAreas);
  useEffect(() => {
    setLocalAreas(initialAreas);
  }, [initialAreas]);

  const [selectedState, setSelectedState] = useState(
    searchParams.get("state") || "Karnataka"
  );
  const [selectedDistrict, setSelectedDistrict] = useState(
    searchParams.get("district") || "Bangalore"
  );
  const [visibleAreas, setVisibleAreas] = useState<Set<number>>(new Set());
  const [map, setMap] = useState<google.maps.Map | null>(null);
  const [mapLoaded, setMapLoaded] = useState(false);
  const [searchTerm, setSearchTerm] = useState("");
  const [sortOrder, setSortOrder] = useState<"asc" | "desc">("asc");

  // The area currently being edited (if any). For new areas, id === 0.
  const [editingArea, setEditingArea] = useState<SellerArea | null>(null);
  // This state stores the updated encoded polygon string as the user draws/edits.
  const [editingPolygon, setEditingPolygon] = useState<string>("");

  // For vertex deletion: index of the selected vertex (if any)
  const [selectedVertexIndex, setSelectedVertexIndex] = useState<number | null>(null);

  // Info window state: to show area info on polygon click.
  const [infoWindow, setInfoWindow] = useState<{
    position: google.maps.LatLngLiteral;
    content: string;
  } | null>(null);

  // Flag for drawing mode (for free drawing of new polygon)
  const [drawingMode, setDrawingMode] = useState<boolean>(false);
  const [radiusMode, setRadiusMode] = useState<boolean>(false);


  // We'll store references to each polygon so we can attach listeners.
  const polygonRefs = useRef<{ [areaId: number]: google.maps.Polygon }>({});

  useEffect(() => {
    if (mapLoaded) {
      const initialVisible = new Set(
        localAreas.filter(a => a.polygon || (a.radius && a.latitude && a.longitude)).map(a => a.id)
      );
      setVisibleAreas(initialVisible);
    }
  }, [mapLoaded, localAreas]);

  const onLoadMap = useCallback((mapInstance: google.maps.Map) => {
    setMap(mapInstance);
    setMapLoaded(true);
  }, []);

  const onUnmountMap = useCallback(() => {
    setMap(null);
    setMapLoaded(false);
  }, []);

  const fitBounds = useCallback(() => {
    if (map && visibleAreas.size > 0) {
      const bounds = new google.maps.LatLngBounds();
      localAreas.forEach(area => {
        if (visibleAreas.has(area.id)) {
          if (area.polygon) {
            decodePolygon(area.polygon).forEach(coord => bounds.extend(coord));
          } else if (area.radius && area.latitude && area.longitude) {
            // For radius areas, extend bounds to include the circle
            const center = { lat: area.latitude, lng: area.longitude };
            const radiusInDegrees = area.radius / 111320; // Approximate conversion from meters to degrees
            bounds.extend({ lat: center.lat + radiusInDegrees, lng: center.lng + radiusInDegrees });
            bounds.extend({ lat: center.lat - radiusInDegrees, lng: center.lng - radiusInDegrees });
          }
        }
      });
      map.fitBounds(bounds);
    }
  }, [map, visibleAreas, localAreas]);

  useEffect(() => {
    if (mapLoaded) {
      fitBounds();
    }
  }, [fitBounds, mapLoaded]);

  const toggleAreaVisibility = useCallback((areaId: number) => {
    setVisibleAreas(prev => {
      const newSet = new Set(prev);
      if (newSet.has(areaId)) {
        newSet.delete(areaId);
      } else {
        newSet.add(areaId);
      }
      return newSet;
    });
  }, []);

  useEffect(() => {
    fitBounds();
  }, [visibleAreas, fitBounds]);

  const setAllVisible = () => {
    const allWithBoundaries = new Set(
      localAreas.filter(a => a.polygon || (a.radius && a.latitude && a.longitude)).map(a => a.id)
    );
    setVisibleAreas(allWithBoundaries);
  };
  const setNoneVisible = () => setVisibleAreas(new Set());

  const filteredAreas = useMemo(() => {
    return localAreas
      .filter(area => {
        const matchesSearch = area.name
          .toLowerCase()
          .includes(searchTerm.toLowerCase());
        const matchesState =
          selectedState === "all" || area.state === selectedState;
        const matchesDistrict =
          selectedDistrict === "all" || area.district === selectedDistrict;
        const polygonpresent = area.polygon
        return matchesSearch && matchesState && matchesDistrict;
      })
      .sort((a, b) =>
        sortOrder === "asc"
          ? a.name.localeCompare(b.name)
          : b.name.localeCompare(a.name)
      );
  }, [localAreas, searchTerm, selectedState, selectedDistrict, sortOrder]);

  const handleSort = () => {
    setSortOrder(sortOrder === "asc" ? "desc" : "asc");
  };

  if (
    location.pathname.endsWith("/new") ||
    location.pathname.endsWith("/attach") ||
    location.pathname.includes("/edit")
  ) {
    return <Outlet />;
  }

  // ------------------
  // ADD FLOW:
  // When "Add" is clicked, we set drawingMode to true so the user can freely draw.
  const handleAdd = () => {
    setDrawingMode(true);
    // Clear any previous editing state.
    setEditingArea(null);
    setEditingPolygon("");
    setRadiusMode(false)
  };
  const handleRadiusAdd = () => {
    setRadiusMode(true)
    // Clear any previous editing state.
    setDrawingMode(false);
    setEditingArea(null);
    setEditingPolygon("");
  };
  // ------------------

  // When the drawing is complete, this callback is called.
  const onPolygonComplete = useCallback(
    (poly: google.maps.Polygon) => {
      // Remove the drawing overlay.
      poly.setMap(null);
      const path = poly.getPath();
      const coords: { lat: number; lng: number }[] = [];
      for (let i = 0; i < path.getLength(); i++) {
        const latLng = path.getAt(i);
        coords.push({ lat: latLng.lat(), lng: latLng.lng() });
      }
      const coordsArray = coords.map(({ lat, lng }) => [lat, lng]);
      const deduped = deduplicateCoordinates(coordsArray);
      const encoded = polylineCodec.encode(deduped);
      // Create new area with id 0.
      const newArea: SellerArea = {
        id: 0,
        name: "",
        state: selectedState === "all" ? "" : selectedState,
        district: selectedDistrict === "all" ? "" : selectedDistrict,
        polygon: encoded
      };
      setLocalAreas(prev => [...prev, newArea]);
      setEditingArea(newArea);
      setEditingPolygon(encoded);
      setDrawingMode(false);
    },
    [selectedState, selectedDistrict]
  );

  // Start editing an area (from table "Edit" button)
  const startEditing = (area: SellerArea) => {
    setEditingArea(area);
    if (area.polygon) {
      setEditingPolygon(area.polygon);
      setRadiusMode(false)
    } else if (area.radius) {
      setRadiusMode(true)
    }
    setSelectedVertexIndex(null);
  };

  // Store polygon reference for each area.
  const handlePolygonLoad = useCallback(
    (polygon: google.maps.Polygon, areaId: number) => {
      polygonRefs.current[areaId] = polygon;
    },
    []
  );

  // Update editingPolygon state by reading the polygon's current path.
  const handlePolygonEdit = useCallback(
    (areaId: number) => {
      const poly = polygonRefs.current[areaId];
      if (!poly) return;
      const path = poly.getPath();
      const coords: { lat: number; lng: number }[] = [];
      for (let i = 0; i < path.getLength(); i++) {
        const latLng = path.getAt(i);
        coords.push({ lat: latLng.lat(), lng: latLng.lng() });
      }
      const coordsArray = coords.map(({ lat, lng }) => [lat, lng]);
      const deduped = deduplicateCoordinates(coordsArray);
      if (deduped.length < coordsArray.length) {
        const newPath = deduped.map(([lat, lng]) => ({ lat, lng }));
        poly.setPath(newPath);
      }
      const encoded = polylineCodec.encode(deduped);
      setEditingPolygon(encoded);
    },
    []
  );

  // Helper: Deduplicate overlapping coordinate pairs.
  function deduplicateCoordinates(coords: number[][]): number[][] {
    if (coords.length === 0) return coords;
    const deduped = [coords[0]];
    for (let i = 1; i < coords.length; i++) {
      const prev = deduped[deduped.length - 1];
      const curr = coords[i];
      if (prev[0] === curr[0] && prev[1] === curr[1]) continue;
      deduped.push(curr);
    }
    return deduped;
  }

  // Attach listeners to polygon's path when an area is in edit mode.
  useEffect(() => {
    if (editingArea) {
      const poly = polygonRefs.current[editingArea.id];
      if (poly) {
        const path = poly.getPath();
        const listener1 = google.maps.event.addListener(path, "set_at", () => {
          handlePolygonEdit(editingArea.id);
        });
        const listener2 = google.maps.event.addListener(path, "insert_at", () => {
          handlePolygonEdit(editingArea.id);
        });
        return () => {
          google.maps.event.removeListener(listener1);
          google.maps.event.removeListener(listener2);
        };
      }
    }
  }, [editingArea, handlePolygonEdit]);

  // Allow deletion of a selected vertex via Delete/Backspace key.
  useEffect(() => {
    function handleKeyDown(e: KeyboardEvent) {
      if (
        (e.key === "Delete" || e.key === "Backspace") &&
        editingArea !== null &&
        selectedVertexIndex !== null
      ) {
        const poly = polygonRefs.current[editingArea.id];
        if (poly) {
          const path = poly.getPath();
          if (path.getLength() > 3) {
            path.removeAt(selectedVertexIndex);
            handlePolygonEdit(editingArea.id);
            setSelectedVertexIndex(null);
          }
        }
      }
    }
    if (editingArea) {
      window.addEventListener("keydown", handleKeyDown);
    }
    return () => {
      window.removeEventListener("keydown", handleKeyDown);
    };
  }, [editingArea, selectedVertexIndex, handlePolygonEdit]);

  // When in editing mode, clicking on the polygon selects a vertex.
  const handlePolygonClick = useCallback(
    (e: google.maps.MapMouseEvent, area: SellerArea) => {
      if (editingArea && editingArea.id === area.id && e.latLng) {
        const poly = polygonRefs.current[area.id];
        if (poly) {
          const path = poly.getPath();
          let closestIndex: number | null = null;
          let minDist = Infinity;
          for (let i = 0; i < path.getLength(); i++) {
            const vertex = path.getAt(i);
            const d = Math.sqrt(
              Math.pow(vertex.lat() - e.latLng.lat(), 2) +
              Math.pow(vertex.lng() - e.latLng.lng(), 2)
            );
            if (d < minDist) {
              minDist = d;
              closestIndex = i;
            }
          }
          const threshold = 0.0001;
          if (minDist < threshold) {
            setSelectedVertexIndex(closestIndex);
          } else {
            setSelectedVertexIndex(null);
          }
        }
      } else {
        // When not in editing mode, show an info window.
        if (e.latLng && area) {
          setInfoWindow({
            position: e.latLng.toJSON(),
            content: `${area.id} - ${area.name}`
          });
        }
      }
    },
    [editingArea]
  );

  const handleCancelEdit = () => {
    setEditingArea(null);
    setEditingPolygon("");
    setSelectedVertexIndex(null);
  };




  return (
    <LoadScript
      googleMapsApiKey={googleMapsApiKey}
      libraries={["drawing"]}
    >
      <div className="flex h-[calc(100vh-64px)] relative">
        {/* LEFT PANE */}
        <div className="w-fit bg-white border-r border-gray-200 overflow-y-auto flex flex-col">
          <div className="p-4 border-b border-gray-200">
            <div className="flex justify-between items-center mb-4">
              <h2 className="text-lg font-semibold text-gray-900">
                Service Areas
              </h2>
              <div className="space-x-2 flex ">
                <Button onClick={handleAdd} size="sm">
                  Add Polygon                </Button>
                <Button onClick={handleRadiusAdd} size="sm">
                  Add Radius
                </Button>
              </div>
            </div>

            <div className="flex gap-2 mb-4">
              <Select
                value={selectedState}
                onValueChange={(value) => {
                  setSelectedState(value);
                  setSelectedDistrict("");
                  setSearchParams({ state: value, district: "" });
                }}
              >
                <SelectTrigger className="w-1/2">
                  <SelectValue placeholder="Select state" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All States</SelectItem>
                  {states.map((state) => (
                    <SelectItem key={state} value={state}>
                      {state}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              <Select
                value={selectedDistrict}
                onValueChange={(value) => {
                  setSelectedDistrict(value);
                  setSearchParams({ state: selectedState, district: value });
                }}
                disabled={selectedState === "all"}
              >
                <SelectTrigger className="w-1/2">
                  <SelectValue placeholder="Select district" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Districts</SelectItem>
                  {selectedState !== "all" &&
                    districts[selectedState]?.map((d) => (
                      <SelectItem key={d} value={d}>
                        {d}
                      </SelectItem>
                    ))}
                </SelectContent>
              </Select>
            </div>
            <div className="flex justify-between items-center mb-4">
              <Input
                placeholder="Search areas..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="flex w-1/2"
              />
              <div className="space-x-2">
                <Button
                  onClick={setNoneVisible}
                  variant="outline"
                  size="sm"
                >
                  Hide All
                </Button>
                <Button
                  onClick={setAllVisible}
                  variant="outline"
                  size="sm"
                >
                  Show All
                </Button>
              </div>
            </div>
          </div>
          <div className="flex-grow overflow-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50 sticky top-0">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    <Button
                      variant="ghost"
                      onClick={handleSort}
                      className="flex items-center"
                    >
                      Name
                      <ArrowUpDown size={14} className="ml-1" />
                    </Button>
                  </th>
                  <th className="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Actions
                  </th>

                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {filteredAreas.map((area, index) => {
                  const isEditing = editingArea?.id === area.id;
                  return (
                    <tr
                      key={area.id}
                      className={`hover:bg-gray-50 ${isEditing ? "bg-blue-50" : ""
                        }`}
                    >
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="flex items-center">
                          <div
                            className="w-3 h-3 rounded-full mr-2"
                            style={{
                              backgroundColor: getPolygonColor(index),
                              opacity: visibleAreas.has(area.id) ? 1 : 0.3
                            }}
                          />
                          <span className="text-sm text-gray-900">
                            {area.name}
                            {(!area.polygon && !area.radius) && (
                              <span className="text-xs text-gray-400 ml-2">
                                (no boundary)
                              </span>
                            )}
                          </span>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium flex gap-2">
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => toggleAreaVisibility(area.id)}
                          disabled={!area.polygon && !(area.radius && area.latitude && area.longitude)}
                        >
                          {visibleAreas.has(area.id) ? (
                            <Eye size={16} />
                          ) : (
                            <EyeOff size={16} />
                          )}
                        </Button>

                        <Button
                          variant="ghost"
                          size="sm"
                          className="text-indigo-600 hover:text-indigo-900"
                          onClick={() => startEditing(area)}
                        >
                          <Pencil size={16} />
                        </Button>
                        {/* Delete button wrapped in a Form */}
                        <Form method="post" onSubmit={(e) => {
                          if (!confirm("Are you sure you want to delete this area?")) {
                            e.preventDefault();
                          }
                        }}>
                          <input type="hidden" name="id" value={area.id} />
                          <input type="hidden" name="action" value="delete" />
                          <Button type="submit" variant="ghost" size="sm" className="text-red-600 hover:text-red-900">
                            <Trash2 size={16} />
                          </Button>
                        </Form>
                      </td>
                    </tr>
                  );
                })}
              </tbody>
            </table>
          </div>
        </div>

        {/* RIGHT PANE - MAP */}
        <div className="flex-1 bg-white relative">
          <GoogleMap
            mapContainerStyle={MAP_CONTAINER_STYLE}
            center={BANGALORE_CENTER}
            zoom={11}
            onLoad={onLoadMap}
            onUnmount={onUnmountMap}
            options={{
              styles: [
                {
                  featureType: "all",
                  elementType: "geometry.fill",
                  stylers: [{ visibility: "on" }]
                }
              ],
              mapTypeControl: false,
              streetViewControl: false,
              fullscreenControl: false
            }}
          >
            {mapLoaded &&
              localAreas.map((area, index) => {
                if (!visibleAreas.has(area.id)) return null;

                // Render polygon areas
                if (area.polygon) {
                  const isEditing = editingArea?.id === area.id;
                  const polygonPath =
                    isEditing && editingPolygon
                      ? decodePolygon(editingPolygon)
                      : decodePolygon(area.polygon);

                  const polygonOptions = isEditing
                    ? {
                      fillColor: getPolygonColor(index),
                      fillOpacity: 0.5,
                      strokeColor: "#FFD700",
                      strokeOpacity: 1,
                      strokeWeight: 4,
                      clickable: true,
                      editable: true
                    }
                    : {
                      fillColor: getPolygonColor(index),
                      fillOpacity: 0.2,
                      strokeColor: getPolygonColor(index),
                      strokeOpacity: 1,
                      strokeWeight: 2,
                      clickable: true,
                      editable: false
                    };

                  return (
                    <Polygon
                      key={`polygon-${area.id}`}
                      paths={polygonPath}
                      options={polygonOptions}
                      onLoad={(poly) => handlePolygonLoad(poly, area.id)}
                      onClick={(e) => handlePolygonClick(e, area)}
                      onMouseUp={() => handlePolygonEdit(area.id)}
                      onDragEnd={() => handlePolygonEdit(area.id)}
                    />
                  );
                }

                // Render radius areas
                if (area.radius && area.latitude && area.longitude) {
                  const center = { lat: area.latitude, lng: area.longitude };

                  return (
                    <div key={`radius-${area.id}`}>
                      <Circle
                        center={center}
                        radius={area.radius}
                        options={{
                          fillColor: getPolygonColor(index),
                          fillOpacity: 0.2,
                          strokeColor: getPolygonColor(index),
                          strokeOpacity: 1,
                          strokeWeight: 2,
                          clickable: true
                        }}
                        onClick={(e) => {
                          if (e.latLng && area) {
                            setInfoWindow({
                              position: e.latLng.toJSON(),
                              content: `${area.id} - ${area.name}`
                            });
                          }
                        }}
                      />
                      <Marker
                        position={center}
                        options={{
                          icon: {
                            path: google.maps.SymbolPath.CIRCLE,
                            scale: 8,
                            fillColor: "#4285F4",
                            fillOpacity: 1,
                            strokeColor: "#ffffff",
                            strokeWeight: 2
                          }
                        }}
                        onClick={() => {
                          setInfoWindow({
                            position: center,
                            content: `${area.id} - ${area.name}`
                          });
                        }}
                      />
                    </div>
                  );
                }

                return null;
              })}

            {/* Render DrawingManager if drawingMode is true */}
            {drawingMode && (
              <DrawingManager
                options={{
                  drawingControl: true,
                  drawingMode:
                    window.google.maps.drawing.OverlayType.POLYGON,
                  polygonOptions: {
                    fillColor: "#FF0000",
                    fillOpacity: 0.5,
                    strokeWeight: 2,
                    clickable: true,
                    editable: true,
                    zIndex: 1
                  }
                }}
                onPolygonComplete={(poly) => {
                  poly.setMap(null);
                  const path = poly.getPath();
                  const coords: { lat: number; lng: number }[] = [];
                  for (let i = 0; i < path.getLength(); i++) {
                    const latLng = path.getAt(i);
                    coords.push({ lat: latLng.lat(), lng: latLng.lng() });
                  }
                  const coordsArray = coords.map(({ lat, lng }) => [lat, lng]);
                  const deduped = deduplicateCoordinates(coordsArray);
                  const encoded = polylineCodec.encode(deduped);
                  const newArea: SellerArea = {
                    id: 0,
                    name: "",
                    state: selectedState === "all" ? "" : selectedState,
                    district:
                      selectedDistrict === "all" ? "" : selectedDistrict,
                    polygon: encoded
                  };
                  setLocalAreas((prev) => [...prev, newArea]);
                  setEditingArea(newArea);
                  setEditingPolygon(encoded);
                  setDrawingMode(false);
                }}
              />
            )}

            {/* Info Window: show area info when a polygon is clicked */}
            {infoWindow && (
              <InfoWindow
                position={infoWindow.position}
                // onCloseClick={() => setInfoWindow(null)}
                options={{
                  headerDisabled: true,
                  disableAutoPan: true,
                }}
              >
                {/* <div>{infoWindow.content}</div> */}
                <div className="flex flex-col gap-2 overflow-hidden ">
                  <div className='flex justify-between w-full align-middle items-center'>
                    <h2 className='text-md font-semibold text-typography-300'>Locality Info</h2>
                    <button className="inline-flex items-center gap-1 hover:text-blue-800"
                      onClick={() => setInfoWindow(null)}>
                      <X className="h-5 w-5" />
                    </button>
                  </div>
                  <div className="flex flex-col gap-1 text-sm font-medium text-typography-700 w-[calc(100%-1rem)]">
                    <p> {infoWindow.content}</p>
                  </div>
                </div>
              </InfoWindow>
            )}
          </GoogleMap>

          {
            radiusMode ?
              <div className="absolute top-4 right-4 w-80 bg-white p-4 shadow-lg rounded z-50">
                <Form method="post">
                  <input type="hidden" name="id" value={editingArea?.id || 0} />
                  <input type="hidden" name="mode" value={editingArea?.id ? "RadiusUpdate" : "RadiusCreate"} readOnly />
                  {actionData?.error && (
                    <p className="text-red-500 mb-2">{actionData.error}</p>
                  )}
                  <div className="mb-3">
                    <label className="block text-sm font-medium text-gray-700">
                      Name
                    </label>
                    <Input
                      className="w-full"
                      name="name"
                      defaultValue={editingArea?.name}
                    />
                  </div>
                  <div className="mb-3">
                    <label className="block text-sm font-medium text-gray-700">Radius</label>
                    <Input
                      className="w-full"
                      name="radius"
                      defaultValue={editingArea?.radius}
                    />
                  </div>
                  <div className="mb-3">
                    <label className="block text-sm font-medium text-gray-700">Latitude</label>
                    <Input
                      className="w-full"
                      name="latitude"
                      defaultValue={editingArea?.latitude}
                      required={!!editingArea?.radius} // Make required if radius is present
                    />
                  </div>
                  <div className="mb-3">
                    <label className="block text-sm font-medium text-gray-700">Longitude</label>
                    <Input
                      className="w-full"
                      name="longitude"
                      defaultValue={editingArea?.longitude}
                      required={!!editingArea?.radius} // Make required if radius is present
                    />
                  </div>
                  <div className="mb-3">
                    <label className="block text-sm font-medium text-gray-700">
                      State
                    </label>
                    <Select name="state" defaultValue={editingArea?.state}>
                      <SelectTrigger className="w-full mt-1">
                        <SelectValue placeholder="Select state" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="state">States</SelectItem>
                        {states.map((state) => (
                          <SelectItem key={state} value={state}>
                            {state}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                  <div className="mb-3">
                    <label className="block text-sm font-medium text-gray-700">
                      District
                    </label>
                    <Select
                      name="district"
                      value={selectedDistrict}
                      onValueChange={(value) => {
                        setSelectedDistrict(value);
                        setSearchParams({ state: selectedState, district: value });
                      }}
                      disabled={selectedState === "all"}
                    >
                      <SelectTrigger className="w-1/2">
                        <SelectValue placeholder="Select district" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="all">All Districts</SelectItem>
                        {selectedState !== "all" &&
                          districts[selectedState]?.map((d) => (
                            <SelectItem key={d} value={d}>
                              {d}
                            </SelectItem>
                          ))}
                      </SelectContent>
                    </Select>
                  </div>
                  <div className="flex justify-end space-x-3 mt-4">
                    <Button
                      type="button"
                      variant="outline"
                      onClick={() => {
                        setRadiusMode(false)
                        setEditingArea(null)
                      }}
                    >
                      Cancel
                    </Button>
                    <Button type="submit">
                      {editingArea?.id === 0 ? "Create" : "Save"}
                    </Button>
                  </div>
                </Form>
              </div> : editingArea ?
                <div className="absolute top-4 right-4 w-80 bg-white p-4 shadow-lg rounded z-50">
                  <h3 className="text-lg font-semibold mb-2">
                    {editingArea.id === 0 ? "Create New Area" : "Edit Area"}
                  </h3>
                  {actionData?.error && (
                    <p className="text-red-500 mb-2">{actionData.error}</p>
                  )}
                  <Form method="post">
                    <input type="hidden" name="id" value={editingArea.id} />
                    <input type="hidden" name="polygon" value={editingPolygon} />
                    <div className="mb-3">
                      <label className="block text-sm font-medium text-gray-700">
                        Name
                      </label>
                      <Input
                        className="w-full"
                        name="name"
                        defaultValue={editingArea.name}
                      />
                    </div>
                    <div className="mb-3">
                      <label className="block text-sm font-medium text-gray-700">
                        State
                      </label>
                      <Select name="state" defaultValue={editingArea.state}>
                        <SelectTrigger className="w-full mt-1">
                          <SelectValue placeholder="Select state" />
                        </SelectTrigger>
                        <SelectContent>
                          {states.map((state) => (
                            <SelectItem key={state} value={state}>
                              {state}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>
                    <div className="mb-3">
                      <label className="block text-sm font-medium text-gray-700">
                        District
                      </label>
                      <Select name="district" defaultValue={editingArea.district}>
                        <SelectTrigger className="w-full mt-1">
                          <SelectValue placeholder="Select district" />
                        </SelectTrigger>
                        <SelectContent>
                          {editingArea.state &&
                            districts[editingArea.state]?.map((d) => (
                              <SelectItem key={d} value={d}>
                                {d}
                              </SelectItem>
                            ))}
                        </SelectContent>
                      </Select>
                    </div>
                    <div className="flex justify-end space-x-3 mt-4">
                      <Button
                        type="button"
                        variant="outline"
                        onClick={handleCancelEdit}
                      >
                        Cancel
                      </Button>
                      <Button type="submit">
                        {editingArea.id === 0 ? "Create" : "Save"}
                      </Button>
                    </div>
                  </Form>
                </div> : null}
        </div>
      </div >
    </LoadScript >
  );
}

// When not in editing mode, clicking on a polygon shows an info window.
function handlePolygonClick(
  e: google.maps.MapMouseEvent,
  area: SellerArea
) {
  if (e.latLng && area) {
    const position = e.latLng.toJSON();
    (window as any).setInfoWindow &&
      (window as any).setInfoWindow({
        position,
        content: `${area.id} - ${area.name}`
      });
  }
}