{"name": "@remix-run/node", "version": "2.15.3", "description": "Node.js platform abstractions for Remix", "bugs": {"url": "https://github.com/remix-run/remix/issues"}, "repository": {"type": "git", "url": "https://github.com/remix-run/remix", "directory": "packages/remix-node"}, "license": "MIT", "main": "dist/index.js", "typings": "dist/index.d.ts", "sideEffects": ["./install.js"], "dependencies": {"@remix-run/web-fetch": "^4.4.2", "@web3-storage/multipart-parser": "^1.0.0", "cookie-signature": "^1.1.0", "source-map-support": "^0.5.21", "stream-slice": "^0.1.2", "undici": "^6.11.1", "@remix-run/server-runtime": "2.15.3"}, "devDependencies": {"@types/cookie-signature": "^1.0.3", "@types/source-map-support": "^0.5.4", "typescript": "^5.1.6"}, "peerDependencies": {"typescript": "^5.1.0"}, "peerDependenciesMeta": {"typescript": {"optional": true}}, "engines": {"node": ">=18.0.0"}, "files": ["dist/", "globals.d.ts", "install.d.ts", "install.js", "CHANGELOG.md", "LICENSE.md", "README.md"], "scripts": {"tsc": "tsc"}}