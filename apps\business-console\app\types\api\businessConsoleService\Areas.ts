import { number } from "zod";

export interface SellerArea {
    id: number;
    name: string;
    district: string;
    state: string;
    polygon?: string;
    radius?: number,
    latitude?: number,
    longitude?: number
}

export interface CreateAreaRequest {
    name: string;
    district: string;
    state: string;
    polygon?: string;
    radius?: number,
    latitude?: number,
    longitude?: number
}

export interface UpdateAreaRequest {
    id: number;
    name?: string;
    district?: string;
    state?: string;
    polygon?: string;
    radius?: number,
    latitude?: number,
    longitude?: number
}

export interface CreateAreaResponse {
    id: number;
    name: string;
    district: string;
    state: string;
    polygon?: string;
    radius?: number,
    latitude?: number,
    longitude?: number
}