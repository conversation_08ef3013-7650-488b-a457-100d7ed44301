import { LoaderFunction, ActionFunction } from "@remix-run/node";
import { useLoaderData, useActionData, Form, useNavigation, useNavigate, Link } from "@remix-run/react";
import { Payout, WalletInfo, InitiatePayment } from "~/types/api/businessConsoleService/Payouts";
import { getWalletInfo, getPayoutList, initiateWithdraw, confirmWithdraw, cancelWithdraw } from "~/services/payments";
import { withAuth, withResponse } from "~/utils/auth-utils";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "~/components/ui/card";
import { Button } from "~/components/ui/button";
import { Badge } from "~/components/ui/badge";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "~/components/ui/table";
import { Wallet, Download, Calendar, DollarSign, TrendingUp, ChevronLeft, ChevronRight, AlertCircle, CheckCircle, X } from "lucide-react";
import { Alert, AlertDescription } from "~/components/ui/alert";
import SpinnerLoader from "~/components/loader/SpinnerLoader";
import { useState, useEffect } from "react";
import { AlertDialog, AlertDialogOverlay, AlertDialogContent, AlertDialogHeader, AlertDialogTitle, AlertDialogDescription, AlertDialogFooter } from "~/components/ui/alert-dialog";
import { showDecimalAsSubscript } from "./home.myPayment.$id";

interface LoaderData {
    walletInfo: WalletInfo;
    payouts: Payout[];
    pageNo: number;
    pageSize: number;
    error?: string;
}

interface ActionData {
    success?: boolean;
    error?: string;
    message?: string;
    paymentInitiationId?: number;
    initiatePaymentData?: InitiatePayment;
}

export const loader: LoaderFunction = withAuth(async ({ request }) => {
    const url = new URL(request.url);
    const pageNo = Number(url.searchParams.get("pageNo")) || 0;
    const pageSize = Number(url.searchParams.get("pageSize")) || 10;
    try {
        const [walletResponse, payoutsResponse] = await Promise.all([
            getWalletInfo(request),
            getPayoutList(pageNo, pageSize, request)
        ]);

        return withResponse({
            walletInfo: walletResponse.data,
            payouts: payoutsResponse.data,
            pageNo,
            pageSize
        }, walletResponse.headers);
    } catch (error) {

        return withResponse({
            walletInfo: null,
            payouts: [],
            pageNo,
            pageSize,
            error: "Failed to load payments data"
        }, new Headers());
    }
});

export const action: ActionFunction = withAuth(async ({ request, user }) => {
    const formData = await request.formData();
    const actionType = formData.get("_action");

    if (actionType === "initiate_withdraw") {
        try {
            const amount = Number(formData.get("amount"));
            if (!amount || amount <= 0) {
                return { success: false, error: "Invalid withdrawal amount" };
            }

            const response = await initiateWithdraw(user.userId, amount, request);
            return {
                success: true,
                message: "Withdrawal initiated.",
                paymentInitiationId: response.data?.paymentInitiationId,
                initiatePaymentData: response.data
            };
        } catch (error) {
            return { success: false, error: "Failed to initiate withdrawal" };
        }
    }

    if (actionType === "confirm_withdraw") {
        try {
            const paymentInitiationId = Number(formData.get("paymentInitiationId"));
            if (!paymentInitiationId) {
                return { success: false, error: "Invalid payment initiation ID" };
            }

            await confirmWithdraw(user.userId, paymentInitiationId, request);
            return {
                success: true,
                message: "Withdrawal confirmed successfully. Your funds will be transferred to your bank account."
            };
        } catch (error) {
            return { success: false, error: "Failed to confirm withdrawal" };
        }
    }

    if (actionType === "cancel_withdraw") {
        try {
            const paymentInitiationId = Number(formData.get("paymentInitiationId"));
            if (!paymentInitiationId) {
                return { success: false, error: "Invalid payment initiation ID" };
            }

            await cancelWithdraw(user.userId, paymentInitiationId, request);
            return {
                success: true,
                message: "Withdrawal cancelled successfully"
            };
        } catch (error) {
            return { success: false, error: "Failed to cancel withdrawal" };
        }
    }

    return { success: false, error: "Invalid action" };
});

export default function MyPayments() {
    const { walletInfo, payouts, pageNo, pageSize, error } = useLoaderData<LoaderData>();
    const actionData = useActionData<ActionData>();
    const navigation = useNavigation();
    const isSubmitting = (navigation.state === "submitting" || navigation.state === "loading") && (navigation.formMethod === "POST" || navigation.formMethod === "PUT");
    const navigate = useNavigate();

    // Modal state management
    const [showWithdrawModal, setShowWithdrawModal] = useState(false);
    const [withdrawalData, setWithdrawalData] = useState<InitiatePayment | null>(null);
    const [modalStep, setModalStep] = useState<'confirm' | 'processing' | 'success' | 'error'>('confirm');

    // Handle action data changes
    useEffect(() => {
        if (actionData?.success && actionData?.initiatePaymentData) {
            // Withdrawal initiated successfully, show confirmation modal
            setWithdrawalData(actionData.initiatePaymentData);
            setShowWithdrawModal(true);
            setModalStep('confirm');
        } else if (actionData?.success && !actionData?.initiatePaymentData) {
            // Confirm or cancel action completed
            setModalStep('success');
            setTimeout(() => {
                setShowWithdrawModal(false);
                setWithdrawalData(null);
                setModalStep('confirm');
            }, 3000);
        } else if (actionData?.error) {
            setModalStep('error');
        }
    }, [actionData]);

    const handleWithdrawClick = () => {
        // This will trigger the form submission for initiate_withdraw
        const form = document.getElementById('withdraw-form') as HTMLFormElement;
        if (form) {
            form.requestSubmit();
        }
    };

    const handleConfirmWithdraw = () => {
        setModalStep('processing');
        const form = document.getElementById('confirm-form') as HTMLFormElement;
        if (form) {
            form.requestSubmit();
        }
    };

    const handleCancelWithdraw = () => {
        setModalStep('processing');
        const form = document.getElementById('cancel-form') as HTMLFormElement;
        if (form) {
            form.requestSubmit();
        }
    };

    const closeModal = () => {
        setShowWithdrawModal(false);
        setWithdrawalData(null);
        setModalStep('confirm');
    };

    const getStatusBadge = (status: string) => {
        const statusLower = status.toLowerCase();
        if (statusLower === "completed") {
            return <Badge variant="default" className="bg-green-100 text-green-800 text-xs sm:text-sm whitespace-nowrap">Completed</Badge>;
        } else if (statusLower === "pending") {
            return <Badge variant="secondary" className="bg-yellow-100 text-yellow-800 text-xs sm:text-sm whitespace-nowrap">Pending</Badge>;
        } else if (statusLower === "failed") {
            return <Badge variant="destructive" className="text-xs sm:text-sm whitespace-nowrap">Failed</Badge>;
        } else if (statusLower === "in review") {
            return <Badge variant="outline" className="bg-blue-100 text-blue-800 text-xs sm:text-sm whitespace-nowrap">In Review</Badge>;
        }
        return <Badge variant="outline" className="text-xs sm:text-sm whitespace-nowrap break-all">{status}</Badge>;
    };

    const formatCurrency = (amount: number) => {
        return new Intl.NumberFormat('en-IN', {
            style: 'decimal',
            minimumFractionDigits: 2,
            maximumFractionDigits: 2,
        }).format(amount);
    };

    const formatDate = (dateString: string) => {
        return new Date(dateString).toLocaleDateString('en-IN', {
            year: 'numeric',
            month: 'short',
            day: 'numeric'
        });
    };

    if (error) {
        return (
            <div className="p-6">
                <Alert className="border-red-200 bg-red-50">
                    <AlertDescription className="text-red-800">
                        {error}
                    </AlertDescription>
                </Alert>
            </div>
        );
    }

    if (!walletInfo) {
        return <SpinnerLoader loading={true} />;
    }

    return (
        <div className="container mx-auto p-4 sm:p-6 space-y-4 sm:space-y-6 max-w-7xl">
            <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-2 sm:space-y-0">
                <div>
                    <h1 className="text-2xl sm:text-3xl font-bold text-gray-900 break-words">My Payments</h1>
                    <p className="text-sm sm:text-base text-gray-600 mt-1">Manage your payments and view payout history</p>
                </div>
            </div>

            {actionData?.success && (
                actionData.message === "Withdrawal cancelled successfully" ? (
                    <Alert className="border-red-200 bg-red-50">
                        <CheckCircle className="h-4 w-4 text-red-600" />
                        <AlertDescription className="text-red-800">
                            {actionData.message}
                        </AlertDescription>
                    </Alert>
                ) : (
                    <Alert className="border-green-200 bg-green-50">
                        <CheckCircle className="h-4 w-4 text-green-600" />
                        <AlertDescription className="text-green-800">
                            {actionData.message}
                        </AlertDescription>
                    </Alert>
                )
            )}

            {actionData?.error && (
                <Alert className="border-red-200 bg-red-50">
                    <AlertDescription className="text-red-800">
                        {actionData.error}
                    </AlertDescription>
                </Alert>
            )}

            {/* Wallet Information Card */}
            <Card className="border-0 shadow-md">
                <CardHeader className="bg-gradient-to-r from-blue-50 to-indigo-50 rounded-t-lg p-2 sm:p-4">
                    <div className="flex flex-col sm:flex-row sm:items-start sm:justify-between space-y-4 sm:space-y-0">
                        <div className="flex items-center sm:items-start space-x-3">
                            <div className="p-2 bg-blue-100 rounded-lg flex-shrink-0">
                                <Wallet className="h-5 w-5 sm:h-6 sm:w-6 text-blue-600" />
                            </div>
                            <div className="min-w-0 flex-1">
                                <CardTitle className="text-lg sm:text-xl text-gray-900 break-words">Wallet Balance</CardTitle>
                                <CardDescription className="text-sm sm:text-base text-gray-600 break-words">
                                    Available funds for withdrawal
                                </CardDescription>
                            </div>
                        </div>
                        <div className="flex flex-row sm:flex-col justify-between px-1 text-left sm:text-right">
                            <div className="text-xl sm:text-3xl font-bold text-gray-900 break-all">
                                ₹{formatCurrency(walletInfo.walletBalance)}
                            </div>
                            <Link
                                to="/home/<USER>"
                                className="inline-flex justify-end text-sm sm:text-base underline px-1 py-2 font-medium rounded-lg"
                            >
                                Go to My Wallet
                            </Link>
                        </div>
                    </div>
                </CardHeader>
                <CardContent className="p-4 px-6">
                    <Button
                        onClick={handleWithdrawClick}
                        disabled={!walletInfo.withdrawEnabled || isSubmitting || walletInfo.walletBalance <= 0}
                        className="w-full sm:w-auto bg-blue-600 hover:bg-blue-700 text-white px-4 sm:px-6 py-2 rounded-lg flex items-center justify-center space-x-2 text-sm sm:text-base"
                    >
                        <Download className="h-4 w-4 flex-shrink-0" />
                        <span className="truncate">{isSubmitting ? 'Processing...' : 'Withdraw Funds'}</span>
                    </Button>

                    {/* Hidden forms for different actions */}
                    <Form method="post" id="withdraw-form" style={{ display: 'none' }}>
                        <input type="hidden" name="_action" value="initiate_withdraw" />
                        <input type="hidden" name="amount" value={walletInfo.walletBalance} />
                    </Form>

                    <Form method="post" id="confirm-form" style={{ display: 'none' }}>
                        <input type="hidden" name="_action" value="confirm_withdraw" />
                        <input type="hidden" name="paymentInitiationId" value={withdrawalData?.paymentInitiationId || ''} />
                    </Form>

                    <Form method="post" id="cancel-form" style={{ display: 'none' }}>
                        <input type="hidden" name="_action" value="cancel_withdraw" />
                        <input type="hidden" name="paymentInitiationId" value={withdrawalData?.paymentInitiationId || ''} />
                    </Form>
                </CardContent>
            </Card>

            {/* Payout History */}
            <Card className="border-0 shadow-md">
                <CardHeader className="p-4 sm:p-6 pb-2">
                    <div className="flex items-center space-x-3">
                        <div className="p-2 bg-blue-100 rounded-lg flex-shrink-0">
                            <Calendar className="h-5 w-5 sm:h-6 sm:w-6 text-blue-600" />
                        </div>
                        <div className="min-w-0 flex-1">
                            <CardTitle className="text-lg sm:text-xl text-gray-900 break-words">Payout History</CardTitle>
                            <CardDescription className="text-sm sm:text-base text-gray-600 break-words">
                                Track your payment transactions and settlements
                            </CardDescription>
                        </div>
                    </div>
                </CardHeader>
                <CardContent>
                    {payouts && payouts.length > 0 ? (
                        <div className="overflow-x-auto -mx-4 sm:mx-0">
                            <div className="min-w-full inline-block align-middle">
                                <Table className="min-w-full">
                                    <TableHeader>
                                        <TableRow className="bg-gray-50">
                                            {/* <TableHead className="font-semibold text-gray-700 text-xs sm:text-sm px-2 sm:px-4 py-2 sm:py-3 whitespace-nowrap">ID</TableHead> */}
                                            <TableHead className="font-semibold text-gray-700 text-xs sm:text-sm px-2 sm:px-4 py-2 sm:py-3 min-w-[120px] sm:min-w-[160px]">Date Range</TableHead>
                                            <TableHead className="font-semibold text-gray-700 text-xs sm:text-sm px-2 sm:px-4 py-2 sm:py-3 whitespace-nowrap">Payment Date</TableHead>
                                            <TableHead className="font-semibold text-gray-700 text-xs sm:text-sm px-2 sm:px-4 py-2 sm:py-3 text-right whitespace-nowrap">Amount (₹)</TableHead>
                                            <TableHead className="font-semibold text-gray-700 text-xs sm:text-sm px-2 sm:px-4 py-2 sm:py-3 whitespace-nowrap text-center">Status</TableHead>
                                        </TableRow>
                                    </TableHeader>
                                    <TableBody>
                                        {payouts.map((payout) => (
                                            <TableRow key={payout.payoutId} className="hover:bg-gray-50 transition-colors cursor-pointer" onClick={() => navigate(`/home/<USER>/${payout.payoutId}`)}>
                                                {/* <TableCell className="font-medium text-gray-900 text-xs sm:text-sm px-2 sm:px-4 py-2 sm:py-3 whitespace-nowrap">
                                                    {payout.payoutId}
                                                </TableCell> */}
                                                <TableCell className="text-gray-600 text-xs sm:text-sm px-2 sm:px-4 py-2 sm:py-3">
                                                    <div className="break-words max-w-[120px] sm:max-w-none text-blue-400">
                                                        {payout.dateRange}
                                                    </div>
                                                </TableCell>
                                                <TableCell className="text-gray-600 text-xs sm:text-sm px-2 sm:px-4 py-2 sm:py-3 whitespace-nowrap">
                                                    {formatDate(payout.paymentDate)}
                                                </TableCell>
                                                <TableCell className="text-right font-semibold text-gray-900 text-xs sm:text-sm px-2 sm:px-4 py-2 sm:py-3 whitespace-nowrap">
                                                    {showDecimalAsSubscript(formatCurrency(payout.amount))}
                                                </TableCell>
                                                <TableCell className="px-2 sm:px-4 py-2 sm:py-3" align="center">
                                                    {getStatusBadge(payout.status)}
                                                </TableCell>
                                            </TableRow>
                                        ))}
                                    </TableBody>
                                </Table>
                            </div>
                        </div>
                    ) : (
                        <div className="text-center py-8 sm:py-12 px-4">
                            <div className="p-3 sm:p-4 bg-gray-100 rounded-full w-12 h-12 sm:w-16 sm:h-16 mx-auto mb-4 flex items-center justify-center">
                                <DollarSign className="h-6 w-6 sm:h-8 sm:w-8 text-gray-400" />
                            </div>
                            <h3 className="text-base sm:text-lg font-medium text-gray-900 mb-2 break-words">No Payouts Yet</h3>
                            <p className="text-sm sm:text-base text-gray-600 break-words max-w-md mx-auto">
                                Your payout history will appear here once you start receiving payments.
                            </p>
                        </div>
                    )}
                    <div className="flex items-center justify-end space-x-3 mt-6">
                        <div className="p-1.5 border rounded-md text-sm cursor-pointer" onClick={() => navigate(`/home/<USER>/div>
                        <div className="text-sm text-gray-500">
                            Page {pageNo + 1}
                        </div>
                        <div className={`p-1.5 border rounded-md ${pageNo === 0 ? "text-gray-400" : "cursor-pointer"}`} onClick={() => {
                            if (pageNo > 0) {
                                navigate(`/home/<USER>
                            }
                        }}
                        >
                            <ChevronLeft className="h-5 w-5" />
                        </div>
                        <div className={`p-1.5 border rounded-md ${payouts.length < pageSize ? "text-gray-400" : "cursor-pointer"}`} onClick={() => {
                            if (payouts.length === pageSize) {
                                navigate(`/home/<USER>
                            }
                        }}
                        >
                            <ChevronRight className="h-5 w-5" />
                        </div>
                    </div>
                </CardContent>
            </Card>

            {/* Withdrawal Confirmation Modal */}
            <AlertDialog open={showWithdrawModal} onOpenChange={closeModal}>
                <AlertDialogOverlay className="bg-black/10" />
                <AlertDialogContent className="sm:max-w-[500px]">
                    <AlertDialogHeader>
                        <AlertDialogTitle className="flex items-center space-x-2">
                            {modalStep === 'confirm' && (
                                <>
                                    <AlertCircle className="h-5 w-5 text-blue-600" />
                                    <span>Confirm Withdrawal</span>
                                </>
                            )}
                            {modalStep === 'processing' && (
                                <>
                                    <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-blue-600"></div>
                                    <span>Processing...</span>
                                </>
                            )}
                            {modalStep === 'success' && (
                                <>
                                    <CheckCircle className={actionData?.message === "Withdrawal cancelled successfully" ? "h-5 w-5 text-red-600" : "h-5 w-5 text-green-600"} />
                                    <span>Success</span>
                                </>
                            )}
                            {modalStep === 'error' && (
                                <>
                                    <X className="h-5 w-5 text-red-600" />
                                    <span>Error</span>
                                </>
                            )}
                        </AlertDialogTitle>
                    </AlertDialogHeader>

                    {modalStep === 'confirm' && withdrawalData && (
                        <>
                            <AlertDialogDescription className="space-y-4">
                                <div className="bg-blue-50 p-4 rounded-lg border border-blue-200">
                                    <h4 className="font-semibold text-blue-900 mb-2">Withdrawal Details</h4>
                                    <div className="space-y-2 text-sm">
                                        <div className="flex justify-between">
                                            <span className="text-gray-600">Amount:</span>
                                            <span className="font-semibold">₹{formatCurrency(walletInfo.walletBalance)}</span>
                                        </div>
                                        <div className="flex justify-between">
                                            <span className="text-gray-600">Account Name:</span>
                                            <span className="font-semibold">{withdrawalData.accountName}</span>
                                        </div>
                                        <div className="flex justify-between">
                                            <span className="text-gray-600">Account Number:</span>
                                            <span className="font-semibold">{withdrawalData.accountNumber}</span>
                                        </div>
                                        <div className="flex justify-between">
                                            <span className="text-gray-600">IFSC Code:</span>
                                            <span className="font-semibold">{withdrawalData.ifscCode}</span>
                                        </div>
                                    </div>
                                </div>
                                <p className="text-sm text-gray-600">
                                    Please review the withdrawal details above. Once confirmed, the amount will be transferred to your registered bank account.
                                </p>
                            </AlertDialogDescription>
                            <AlertDialogFooter className="flex flex-col sm:flex-row gap-2">
                                <Button
                                    variant="outline"
                                    onClick={handleCancelWithdraw}
                                    disabled={isSubmitting}
                                    className="w-full sm:w-auto"
                                >
                                    Cancel Withdrawal
                                </Button>
                                <Button
                                    onClick={handleConfirmWithdraw}
                                    disabled={isSubmitting}
                                    className="w-full sm:w-auto bg-blue-600 hover:bg-blue-700"
                                >
                                    Confirm Withdrawal
                                </Button>
                            </AlertDialogFooter>
                        </>
                    )}

                    {modalStep === 'processing' && (
                        <AlertDialogDescription className="text-center py-8">
                            <div className="flex flex-col items-center space-y-4">
                                <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
                                <p className="text-gray-600">Processing your withdrawal request...</p>
                            </div>
                        </AlertDialogDescription>
                    )}

                    {modalStep === 'success' && (
                        <AlertDialogDescription className="text-center py-8">
                            <div className="flex flex-col items-center space-y-4">
                                <CheckCircle className={actionData?.message === "Withdrawal cancelled successfully" ? "h-12 w-12 text-red-600" : "h-12 w-12 text-green-600"} />
                                <div className="space-y-2">
                                    <p className={actionData?.message === "Withdrawal cancelled successfully" ? "font-semibold text-red-800" : "font-semibold text-green-800"}>
                                        {actionData?.message || 'Operation completed successfully!'}
                                    </p>
                                    <p className="text-sm text-gray-600">
                                        This page will refresh automatically in a moment.
                                    </p>
                                </div>
                            </div>
                        </AlertDialogDescription>
                    )}

                    {modalStep === 'error' && (
                        <>
                            <AlertDialogDescription className="text-center py-8">
                                <div className="flex flex-col items-center space-y-4">
                                    <X className="h-12 w-12 text-red-600" />
                                    <div className="space-y-2">
                                        <p className="font-semibold text-red-800">
                                            {actionData?.error || 'An error occurred'}
                                        </p>
                                        <p className="text-sm text-gray-600">
                                            Please try again or contact support if the problem persists.
                                        </p>
                                    </div>
                                </div>
                            </AlertDialogDescription>
                            <AlertDialogFooter>
                                <Button onClick={closeModal} className="w-full">
                                    Close
                                </Button>
                            </AlertDialogFooter>
                        </>
                    )}
                </AlertDialogContent>
            </AlertDialog>
        </div>
    );
}