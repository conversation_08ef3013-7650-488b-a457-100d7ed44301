export type mNETOrder = {
  id: number;

  buyerName: string;
  deliveryDate: string;
  agentName: string;
  agentUserId: number;
  deliveredByName: string;
  deliveredById: number;

  codAmount: number;
  totalCreditAmount: number;
  creditPendingAmount: number;
  creditGiven: number;

  status: string;
  paymentCompleted: boolean;
  totalOrderGroupAmount: number;
};

export type mNETDriver = {
  driverUserId: number,
  fullName: string
}

export type mNETAgent = {
  agentUserId: 0,
  fullName: string,
  businessName: string,
  status: true
}