{"name": "vite-tsconfig-paths", "version": "4.3.2", "description": "Vite resolver for TypeScript compilerOptions.paths", "main": "dist/index.js", "module": "dist/index.mjs", "exports": {"import": "./dist/index.mjs", "require": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}}, "author": "<PERSON><PERSON><PERSON><PERSON>", "repository": "aleclarson/vite-tsconfig-paths", "license": "MIT", "scripts": {"clean": "rimraf dist && git checkout HEAD dist", "dev": "yarn bundle --watch", "build": "yarn clean && yarn bundle", "bundle": "tsup-node src/index.ts --sourcemap --dts --format cjs,esm", "prepare": "yarn build", "test": "jest"}, "dependencies": {"debug": "^4.1.1", "globrex": "^0.1.2", "tsconfck": "^3.0.3"}, "devDependencies": {"@alloc/fast-rimraf": "^1.0.8", "@types/debug": "^4.1.5", "@types/globrex": "^0.1.0", "@types/jest": "^26.0.22", "@types/node": "^14.0.27", "esbuild": "^0.11.12", "esbuild-jest": "npm:@alloc/esbuild-jest@^0.3.6", "jest": "^26.6.3", "klona": "^2.0.4", "prettier": "^2.8.7", "rollup": "^2.45.2", "tsup": "^6.5.0", "typescript": "^4.9.3", "vite": "*", "vite-tsconfig-paths": "link:."}, "peerDependencies": {"vite": "*"}, "peerDependenciesMeta": {"vite": {"optional": true}}, "keywords": ["vite", "resolver", "tsconfig", "paths"], "files": ["src", "dist"]}