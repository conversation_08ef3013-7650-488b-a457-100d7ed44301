import { useState } from "react";
import { useLoaderData } from "@remix-run/react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from "../components/ui/tabs";
import { metabaseService } from "../utils/metabase";
import { withAuth, withResponse } from "../utils/auth-utils";
import { Card, CardContent, CardHeader, CardTitle } from "~/components/ui/card";
import { Badge } from "~/components/ui/badge";
import { Button } from "~/components/ui/button";
import { Input } from "~/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "~/components/ui/select";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "~/components/ui/table";
import { Calendar } from "~/components/ui/calendar";
import { Popover, PopoverContent, PopoverTrigger } from "~/components/ui/popover";
import {
  CalendarIcon,
  TrendingUp,
  TrendingDown,
  Download,
  Filter,
  BarChart3,
  <PERSON><PERSON><PERSON> as PieChartIcon,
  Activity,
  Users,
  ShoppingCart,
  DollarSign,
  Target,
  Zap
} from "lucide-react";
import {
  LineChart,
  Line,
  AreaChart,
  Area,
  BarChart,
  Bar,
  PieChart,
  Pie,
  Cell,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer,
  FunnelChart,
  Funnel,
  LabelList
} from 'recharts';
import { format } from "date-fns";
import { ComingSoonOverlay } from "~/components/modals/ComingSoonOverlay";
import CustomerHeatMap from "~/components/ui/CustomerHeatMap";
import { getServerCookie } from "~/utils/cookie";

export const loader = withAuth(async ({ user, request }) => {
  let sellerId = user.sellerId;
  const selectedSeller = getServerCookie(request, "selectedSeller");
  if (selectedSeller) {
    sellerId = parseInt(selectedSeller);
  }
  const overviewUrl = metabaseService.generateDashboardUrl(11, {
    id: sellerId,
  });
  const itemsAnalyticsUrl = metabaseService.generateDashboardUrl(12, {
    id: sellerId,
  });

  return withResponse({ overviewUrl, itemsAnalyticsUrl });
});

const mockAnalyticsData = {
  overview: {
    totalRevenue: { value: 1234567, change: 15.3 },
    totalOrders: { value: 4567, change: 8.7 },
    avgOrderValue: { value: 270, change: -2.1 },
    conversionRate: { value: 3.4, change: 0.8 },
    customerAcquisitionCost: { value: 45, change: -12.5 },
    returnOnAdSpend: { value: 4.2, change: 18.9 }
  },
  salesTrend: [
    { date: "Jan 01", revenue: 12400, orders: 45, aov: 276 },
    { date: "Jan 02", revenue: 15600, orders: 58, aov: 269 },
    { date: "Jan 03", revenue: 11800, orders: 42, aov: 281 },
    { date: "Jan 04", revenue: 18900, orders: 67, aov: 282 },
    { date: "Jan 05", revenue: 21500, orders: 78, aov: 276 },
    { date: "Jan 06", revenue: 19800, orders: 71, aov: 279 },
    { date: "Jan 07", revenue: 23400, orders: 84, aov: 279 },
    { date: "Jan 08", revenue: 20100, orders: 75, aov: 268 },
    { date: "Jan 09", revenue: 17800, orders: 64, aov: 278 },
    { date: "Jan 10", revenue: 22300, orders: 81, aov: 275 },
    { date: "Jan 11", revenue: 25600, orders: 89, aov: 288 },
    { date: "Jan 12", revenue: 24100, orders: 86, aov: 280 },
    { date: "Jan 13", revenue: 26800, orders: 94, aov: 285 },
    { date: "Jan 14", revenue: 28900, orders: 102, aov: 283 }
  ],
  channelAttribution: [
    { channel: "Website", sessions: 12450, atc: 2890, orders: 234, cvr: 8.1, cac: 35, roas: 4.8 },
    { channel: "WhatsApp", sessions: 8900, atc: 3400, orders: 289, cvr: 32.5, cac: 12, roas: 12.3 },
    { channel: "Instagram", sessions: 6700, atc: 890, orders: 67, cvr: 7.5, cac: 28, roas: 3.2 },
    { channel: "Meta Ads", sessions: 4500, atc: 670, orders: 45, cvr: 6.7, cac: 67, roas: 2.1 },
    { channel: "Google Ads", sessions: 3200, atc: 450, orders: 32, cvr: 7.1, cac: 72, roas: 1.9 }
  ],
  channelMix: [
    { name: "Website", value: 35, color: "#00A390" },
    { name: "WhatsApp", value: 42, color: "#25D366" },
    { name: "Instagram", value: 15, color: "#E4405F" },
    { name: "Meta Ads", value: 8, color: "#1877F2" }
  ],
  itemPerformance: [
    { item: "Margherita Pizza", category: "Pizza", outlet: "Downtown", unitsSold: 234, sales: 82400, grossProfit: 41200, refunds: 2.1 },
    { item: "Chicken Biryani", category: "Rice", outlet: "Mall Road", unitsSold: 189, sales: 85050, grossProfit: 46900, refunds: 1.8 },
    { item: "Veg Burger", category: "Burgers", outlet: "Downtown", unitsSold: 167, sales: 45090, grossProfit: 27800, refunds: 3.2 },
    { item: "Pasta Alfredo", category: "Pasta", outlet: "Mall Road", unitsSold: 142, sales: 56800, grossProfit: 32500, refunds: 1.5 },
    { item: "Caesar Salad", category: "Salads", outlet: "Downtown", unitsSold: 98, sales: 31360, grossProfit: 22400, refunds: 0.8 }
  ],
  funnelData: [
    { step: "Website Visits", users: 10000, percentage: 100, color: "#00A390" },
    { step: "Menu Views", users: 7500, percentage: 75, color: "#1fb8a8" },
    { step: "Add to Cart", users: 3200, percentage: 32, color: "#3ec4b8" },
    { step: "Checkout Started", users: 1800, percentage: 18, color: "#5dd0c8" },
    { step: "Payment", users: 1440, percentage: 14.4, color: "#7cdcd8" },
    { step: "Order Completed", users: 1200, percentage: 12, color: "#9be8e8" }
  ],
  cohortRetention: [
    { cohort: "Dec W1", day7: 45, day30: 22, day60: 18 },
    { cohort: "Dec W2", day7: 48, day30: 25, day60: 20 },
    { cohort: "Dec W3", day7: 42, day30: 28, day60: 22 },
    { cohort: "Dec W4", day7: 51, day30: 32, day60: 25 },
    { cohort: "Jan W1", day7: 46, day30: 29, day60: null },
    { cohort: "Jan W2", day7: 49, day30: null, day60: null }
  ]
};


export default function Analytics() {
  const { overviewUrl, itemsAnalyticsUrl } = useLoaderData<typeof loader>();
  const [selectedTab, setSelectedTab] = useState("overview");
  const [dateRange, setDateRange] = useState("7d");
  const [selectedOutlet, setSelectedOutlet] = useState("all");
  const [selectedChannel, setSelectedChannel] = useState("all");

  const renderOverview = () => (
    <div className="space-y-6">
      {/* KPI Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        <Card className="space-y-4">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-semibold">Total Revenue</CardTitle>
            <DollarSign className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">₹{mockAnalyticsData.overview.totalRevenue.value.toLocaleString()}</div>
            <div className="flex items-center text-xs text-muted-foreground">
              <TrendingUp className="h-4 w-4 text-green-600 mr-1" />
              <span className="text-green-600">+{mockAnalyticsData.overview.totalRevenue.change}%</span>
              <span className="ml-1">vs last period</span>
            </div>
          </CardContent>
        </Card>

        <Card className="space-y-4">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-semibold">Total Orders</CardTitle>
            <ShoppingCart className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{mockAnalyticsData.overview.totalOrders.value.toLocaleString()}</div>
            <div className="flex items-center text-xs text-muted-foreground">
              <TrendingUp className="h-4 w-4 text-green-600 mr-1" />
              <span className="text-green-600">+{mockAnalyticsData.overview.totalOrders.change}%</span>
              <span className="ml-1">vs last period</span>
            </div>
          </CardContent>
        </Card>

        <Card className="space-y-4">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-semibold">Average Order Value</CardTitle>
            <Target className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">₹{mockAnalyticsData.overview.avgOrderValue.value}</div>
            <div className="flex items-center text-xs text-muted-foreground">
              <TrendingDown className="h-4 w-4 text-red-600 mr-1" />
              <span className="text-red-600">{mockAnalyticsData.overview.avgOrderValue.change}%</span>
              <span className="ml-1">vs last period</span>
            </div>
          </CardContent>
        </Card>

        <Card className="space-y-4">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-semibold">Conversion Rate</CardTitle>
            <Activity className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{mockAnalyticsData.overview.conversionRate.value}%</div>
            <div className="flex items-center text-xs text-muted-foreground">
              <TrendingUp className="h-4 w-4 text-green-600 mr-1" />
              <span className="text-green-600">+{mockAnalyticsData.overview.conversionRate.change}%</span>
              <span className="ml-1">vs last period</span>
            </div>
          </CardContent>
        </Card>

        <Card className="space-y-4">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-semibold">Customer Acquisition Cost</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">₹{mockAnalyticsData.overview.customerAcquisitionCost.value}</div>
            <div className="flex items-center text-xs text-muted-foreground">
              <TrendingUp className="h-4 w-4 text-green-600 mr-1" />
              <span className="text-green-600">{mockAnalyticsData.overview.customerAcquisitionCost.change}%</span>
              <span className="ml-1">improvement</span>
            </div>
          </CardContent>
        </Card>

        <Card className="space-y-4">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-semibold">Return on Ad Spend</CardTitle>
            <Zap className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{mockAnalyticsData.overview.returnOnAdSpend.value}x</div>
            <div className="flex items-center text-xs text-muted-foreground">
              <TrendingUp className="h-4 w-4 text-green-600 mr-1" />
              <span className="text-green-600">+{mockAnalyticsData.overview.returnOnAdSpend.change}%</span>
              <span className="ml-1">vs last period</span>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Charts */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <Card>
          <CardHeader>
            <CardTitle className="text-sm font-normal">Revenue Trend</CardTitle>
          </CardHeader>
          <CardContent>
            <ResponsiveContainer width="100%" height={300}>
              <AreaChart data={mockAnalyticsData.salesTrend}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="date" />
                <YAxis />
                <Tooltip formatter={(value, name) => [name === 'revenue' ? `₹${value}` : value, name === 'revenue' ? 'Revenue' : 'Orders']} />
                <Area type="monotone" dataKey="revenue" stroke="#00A390" fill="#00A390" fillOpacity={0.3} />
              </AreaChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="text-sm font-normal">Channel Performance</CardTitle>
          </CardHeader>
          <CardContent>
            <ResponsiveContainer width="100%" height={300}>
              <PieChart>
                <Pie
                  data={mockAnalyticsData.channelMix}
                  cx="50%"
                  cy="50%"
                  outerRadius={100}
                  fill="#8884d8"
                  dataKey="value"
                  label={({ name, value }) => `${name}: ${value}%`}
                >
                  {mockAnalyticsData.channelMix.map((entry, index) => (
                    <Cell key={`cell-${index}`} fill={entry.color} />
                  ))}
                </Pie>
                <Tooltip />
              </PieChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>
      </div>
    </div>
  );

  const renderSalesTrend = () => (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="text-sm font-normal">Sales Performance Over Time</CardTitle>
        </CardHeader>
        <CardContent>
          <ResponsiveContainer width="100%" height={400}>
            <LineChart data={mockAnalyticsData.salesTrend}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="date" />
              <YAxis yAxisId="left" />
              <YAxis yAxisId="right" orientation="right" />
              <Tooltip />
              <Legend />
              <Line yAxisId="left" type="monotone" dataKey="revenue" stroke="#00A390" strokeWidth={2} name="Revenue (₹)" />
              <Line yAxisId="right" type="monotone" dataKey="orders" stroke="#DB3532" strokeWidth={2} name="Orders" />
              <Line yAxisId="left" type="monotone" dataKey="aov" stroke="#28a745" strokeWidth={2} name="AOV (₹)" />
            </LineChart>
          </ResponsiveContainer>
        </CardContent>
      </Card>
    </div>
  );

  const renderChannelAttribution = () => (
    <div className="space-y-6">
      <Card>
        <CardContent className="p-0">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead className="text-black font-semibold">Channel</TableHead>
                <TableHead className="text-black font-semibold">Sessions</TableHead>
                <TableHead className="text-black font-semibold">Add to Cart</TableHead>
                <TableHead className="text-black font-semibold">Orders</TableHead>
                <TableHead className="text-black font-semibold">CVR %</TableHead>
                <TableHead className="text-black font-semibold">CAC ₹</TableHead>
                <TableHead className="text-black font-semibold">ROAS</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {mockAnalyticsData.channelAttribution.map((channel) => (
                <TableRow key={channel.channel}>
                  <TableCell>
                    <div className="flex items-center gap-2">
                      <div className="w-3 h-3 rounded-full bg-primary" />
                      <span className="font-medium">{channel.channel}</span>
                    </div>
                  </TableCell>
                  <TableCell>{channel.sessions.toLocaleString()}</TableCell>
                  <TableCell>{channel.atc.toLocaleString()}</TableCell>
                  <TableCell>{channel.orders.toLocaleString()}</TableCell>
                  <TableCell>
                    <Badge variant={channel.cvr > 10 ? "default" : "secondary"}>
                      {channel.cvr}%
                    </Badge>
                  </TableCell>
                  <TableCell>₹{channel.cac}</TableCell>
                  <TableCell>
                    <div className="flex items-center gap-1">
                      {channel.roas > 3 ? (
                        <TrendingUp className="h-3 w-3 text-green-600" />
                      ) : (
                        <TrendingDown className="h-3 w-3 text-red-600" />
                      )}
                      <span className={channel.roas > 3 ? "text-green-600" : "text-red-600"}>
                        {channel.roas}x
                      </span>
                    </div>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle className="text-sm font-normal">Channel Conversion Rates</CardTitle>
        </CardHeader>
        <CardContent>
          <ResponsiveContainer width="100%" height={300}>
            <BarChart data={mockAnalyticsData.channelAttribution}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="channel" />
              <YAxis />
              <Tooltip formatter={(value) => [`${value}%`, 'Conversion Rate']} />
              <Bar dataKey="cvr" fill="#00A390" />
            </BarChart>
          </ResponsiveContainer>
        </CardContent>
      </Card>
    </div>
  );

  const renderItemPerformance = () => (
    <div className="space-y-6">
      <Card>
        <CardContent className="p-0">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead className="text-black font-semibold">Item</TableHead>
                <TableHead className="text-black font-semibold">Category</TableHead>
                <TableHead className="text-black font-semibold">Outlet</TableHead>
                <TableHead className="text-black font-semibold">Units Sold</TableHead>
                <TableHead className="text-black font-semibold">Sales ₹</TableHead>
                <TableHead className="text-black font-semibold">Gross Profit ₹</TableHead>
                <TableHead className="text-black font-semibold">Refunds %</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {mockAnalyticsData.itemPerformance.map((item) => (
                <TableRow key={item.item}>
                  <TableCell className="font-medium">{item.item}</TableCell>
                  <TableCell>
                    <Badge variant="outline">{item.category}</Badge>
                  </TableCell>
                  <TableCell>{item.outlet}</TableCell>
                  <TableCell>{item.unitsSold}</TableCell>
                  <TableCell>₹{item.sales.toLocaleString()}</TableCell>
                  <TableCell>
                    <div className="flex items-center gap-1">
                      <TrendingUp className="h-3 w-3 text-green-600" />
                      <span className="text-green-600">₹{item.grossProfit.toLocaleString()}</span>
                    </div>
                  </TableCell>
                  <TableCell>
                    <Badge variant={item.refunds < 2 ? "default" : item.refunds < 5 ? "secondary" : "destructive"}>
                      {item.refunds}%
                    </Badge>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle className="text-sm font-normal">Top Items by Revenue</CardTitle>
        </CardHeader>
        <CardContent>
          <ResponsiveContainer width="100%" height={300}>
            <BarChart data={mockAnalyticsData.itemPerformance}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="item" angle={-45} textAnchor="end" height={100} />
              <YAxis />
              <Tooltip formatter={(value) => [`₹${value}`, 'Sales']} />
              <Bar dataKey="sales" fill="#00A390" />
            </BarChart>
          </ResponsiveContainer>
        </CardContent>
      </Card>
    </div>
  );

  const renderFunnelAnalysis = () => (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="text-sm font-normal">Conversion Funnel</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {mockAnalyticsData.funnelData.map((step, index) => (
              <div key={step.step} className="flex items-center gap-4">
                <div className="flex-1">
                  <div className="flex items-center justify-between mb-2">
                    <span className="font-medium">{step.step}</span>
                    <div className="flex items-center gap-2">
                      <span>{step.users.toLocaleString()} users</span>
                      <Badge variant="outline">{step.percentage}%</Badge>
                    </div>
                  </div>
                  <div className="w-full bg-muted rounded-full h-4">
                    <div
                      className="h-4 rounded-full transition-all duration-500"
                      style={{
                        width: `${step.percentage}%`,
                        backgroundColor: step.color
                      }}
                    />
                  </div>
                  {index < mockAnalyticsData.funnelData.length - 1 && (
                    <div className="mt-2 text-xs text-muted-foreground">
                      Drop-off: {((mockAnalyticsData.funnelData[index].users - mockAnalyticsData.funnelData[index + 1].users) / mockAnalyticsData.funnelData[index].users * 100).toFixed(1)}%
                    </div>
                  )}
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  );

  const renderCohortRetention = () => (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="text-sm font-normal">Customer Retention Cohorts</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="overflow-x-auto">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Cohort</TableHead>
                  <TableHead>Day 7</TableHead>
                  <TableHead>Day 30</TableHead>
                  <TableHead>Day 60</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {mockAnalyticsData.cohortRetention.map((cohort) => (
                  <TableRow key={cohort.cohort}>
                    <TableCell className="font-medium">{cohort.cohort}</TableCell>
                    <TableCell>
                      <div className="flex items-center gap-2">
                        <div
                          className="w-12 h-6 rounded flex items-center justify-center text-xs text-white"
                          style={{ backgroundColor: `hsl(${cohort.day7 * 2.4}, 70%, 50%)` }}
                        >
                          {cohort.day7}%
                        </div>
                      </div>
                    </TableCell>
                    <TableCell>
                      {cohort.day30 ? (
                        <div className="flex items-center gap-2">
                          <div
                            className="w-12 h-6 rounded flex items-center justify-center text-xs text-white"
                            style={{ backgroundColor: `hsl(${cohort.day30 * 2.4}, 70%, 50%)` }}
                          >
                            {cohort.day30}%
                          </div>
                        </div>
                      ) : (
                        <span className="text-muted-foreground">-</span>
                      )}
                    </TableCell>
                    <TableCell>
                      {cohort.day60 ? (
                        <div className="flex items-center gap-2">
                          <div
                            className="w-12 h-6 rounded flex items-center justify-center text-xs text-white"
                            style={{ backgroundColor: `hsl(${cohort.day60 * 2.4}, 70%, 50%)` }}
                          >
                            {cohort.day60}%
                          </div>
                        </div>
                      ) : (
                        <span className="text-muted-foreground">-</span>
                      )}
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </div>
        </CardContent>
      </Card>
    </div>
  );

  return (
    <div className="p-6">
      <div className="mb-6">
        <h1 className="text-3xl font-bold text-gray-900">Analytics</h1>
        <p className="text-gray-600 mt-2">Detailed performance insights</p>
      </div>

      <div className="space-y-5">
        {/* Filters */}
        <Card>
          <CardContent className="pt-6">
            <div className="flex flex-col sm:flex-row gap-4">
              <Select value={dateRange} onValueChange={setDateRange}>
                <SelectTrigger className="w-full sm:w-[180px]">
                  <CalendarIcon className="h-4 w-4 mr-2" />
                  <SelectValue placeholder="Date Range" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="7d">Last 7 Days</SelectItem>
                  <SelectItem value="30d">Last 30 Days</SelectItem>
                  <SelectItem value="90d">Last 90 Days</SelectItem>
                  <SelectItem value="custom">Custom Range</SelectItem>
                </SelectContent>
              </Select>
              <Select value={selectedOutlet} onValueChange={setSelectedOutlet}>
                <SelectTrigger className="w-full sm:w-[180px]">
                  <SelectValue placeholder="Outlet" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Outlets</SelectItem>
                  <SelectItem value="downtown">Downtown</SelectItem>
                  <SelectItem value="mall-road">Mall Road</SelectItem>
                </SelectContent>
              </Select>
              <Select value={selectedChannel} onValueChange={setSelectedChannel}>
                <SelectTrigger className="w-full sm:w-[180px]">
                  <SelectValue placeholder="Channel" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Channels</SelectItem>
                  <SelectItem value="website">Website</SelectItem>
                  <SelectItem value="whatsapp">WhatsApp</SelectItem>
                  <SelectItem value="instagram">Instagram</SelectItem>
                  <SelectItem value="meta-ads">Meta Ads</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </CardContent>
        </Card>

        {/* Report Tabs */}
        <Tabs value={selectedTab} onValueChange={setSelectedTab}>
          <TabsList className="grid grid-cols-2 lg:grid-cols-6 w-full h-30 lg:h-10">
            <TabsTrigger value="overview" className="font-semibold">Overview</TabsTrigger>
            <TabsTrigger value="items" className="font-semibold">Items</TabsTrigger>
            <TabsTrigger value="sales-trend" className="font-semibold">Sales Trend</TabsTrigger>
            <TabsTrigger value="channels" className="font-semibold">Channels</TabsTrigger>
            <TabsTrigger value="funnel" className="font-semibold">Funnel</TabsTrigger>
            <TabsTrigger value="retention" className="font-semibold">Retention</TabsTrigger>
          </TabsList>

          <TabsContent value="overview">
            {overviewUrl ? (
              <iframe
                id="metabase-analytics-iframe"
                src={overviewUrl}
                title="Analytics Dashboard"
                className="w-full h-screen border-0"
              />
            ) : renderOverview()}
          </TabsContent>

          <TabsContent value="items">
            {itemsAnalyticsUrl ? (
              <iframe
                id="metabase-analytics-iframe"
                src={itemsAnalyticsUrl}
                title="Analytics Dashboard"
                className="w-full h-screen border-0"
              />
            ) : renderItemPerformance()}
          </TabsContent>

          <TabsContent value="sales-trend">
            {renderSalesTrend()}
          </TabsContent>

          <TabsContent value="channels">
            {renderChannelAttribution()}
          </TabsContent>

          <TabsContent value="funnel">
            {renderFunnelAnalysis()}
          </TabsContent>

          <TabsContent value="retention">
            {renderCohortRetention()}
          </TabsContent>
        </Tabs>
        {(selectedTab === "sales-trend" || selectedTab === "channels" || selectedTab === "funnel" || selectedTab === "retention") && <ComingSoonOverlay />}
      </div>
    </div>
  );
} 