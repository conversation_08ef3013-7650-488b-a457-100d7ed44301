import { NetworkNotificationConfig, WhatsappTokenType } from "~/services/notificationConfig";
import SpinnerLoader from "../loader/SpinnerLoader";
import { Button } from "../ui/button";
import { Input } from "../ui/input";
import { memo, useCallback, useEffect, useMemo, useState } from "react";
import { Edit, Plus, Trash2 } from "lucide-react";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "../ui/table";
import { Dialog, DialogContent, DialogHeader, DialogTitle } from "../ui/dialog";
import { Label } from "../ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "../ui/select";
import { Switch } from "../ui/switch";
import { useToast } from "../ui/ToastProvider";

type ActionType =
  | "createNotificationConfig"
  | "updateNotificationConfig"
  | "deleteNotificationConfig"
  | "updateWabToken";

const NOTIFICATION_TYPES = [
  "DailySalesReport",
  "OrderPlaced",
  "OrderDispatched",
  "PaymentReceived",
  "OrderDelivered"
] as const;

export type NotificationType = typeof NOTIFICATION_TYPES[number];
interface NotificationFormData {
  type: NotificationType;
  waEnabled: boolean;
  baEnabled: boolean;
}

export default function NetworkNotificationConfigs(
  {
    networkId,
    networkName,
    networkDomainId,
    notificationConfigs,
    wabToken,
    fetcher,
    loading
  }: {
    networkId: number,
    networkName: string,
    networkDomainId: number,
    notificationConfigs: NetworkNotificationConfig[],
    wabToken: WhatsappTokenType | null,
    fetcher: any,
    loading: boolean
  }
) {
  const { showToast } = useToast();
  const [searchTerm, setSearchTerm] = useState<string>('');
  const [isAddModalOpen, setIsAddModalOpen] = useState<boolean>(false);
  const [isEditModalOpen, setIsEditModalOpen] = useState<boolean>(false);
  const [editingConfig, setEditingConfig] = useState<NetworkNotificationConfig | null>(null);

  const filteredConfigs = useMemo(() => {
    if (searchTerm.length >= 2) {
      return notificationConfigs.filter(config =>
        config.type.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }
    return notificationConfigs;
  }, [searchTerm, notificationConfigs]);

  const [formData, setFormData] = useState<NotificationFormData>({
    type: "OrderPlaced" as NotificationType,
    waEnabled: false,
    baEnabled: false,
  });

  const [wabFormData, setWabFormData] = useState<WhatsappTokenType | null>(null);

  const handleSubmit = useCallback((isEdit: boolean = false) => {
    const formDataToSubmit = new FormData();
    const actionType: ActionType = isEdit ? "updateNotificationConfig" : "createNotificationConfig";
    formDataToSubmit.append("actionType", actionType);
    formDataToSubmit.append("networkId", networkId.toString());

    if (isEdit && editingConfig?.id) {
      formDataToSubmit.append("id", editingConfig.id.toString());
    }

    formDataToSubmit.append("type", formData.type);
    formDataToSubmit.append("waEnabled", formData.waEnabled.toString());
    formDataToSubmit.append("baEnabled", formData.baEnabled.toString());

    fetcher.submit(formDataToSubmit, { method: "post" });
  }, [fetcher, editingConfig, formData]);

  const handleEdit = useCallback((config: NetworkNotificationConfig) => {
    setIsEditModalOpen(true);
    setEditingConfig(config);
    const editFormData: NotificationFormData = {
      type: config.type as NotificationType,
      waEnabled: config.waEnabled,
      baEnabled: config.baEnabled,
    };
    setFormData(editFormData);
  }, []);

  const handleDelete = useCallback((config: NetworkNotificationConfig) => {
    if (confirm(`Are you sure you want to delete the notification config for "${config.type}"?`)) {
      const formData = new FormData();
      const actionType: ActionType = "deleteNotificationConfig";
      formData.append("actionType", actionType);
      formData.append("networkId", networkId.toString());
      formData.append("type", config.type);
      fetcher.submit(formData, { method: "post" });
    }
  }, [fetcher]);

  const handleWabTokenSubmit = useCallback(() => {
    if (!wabFormData) return;
    const formData = new FormData();
    const actionType: ActionType = "updateWabToken";
    formData.append("actionType", actionType);
    formData.append("networkId", networkId.toString());
    formData.append("networkDomainId", networkDomainId.toString());
    formData.append("data", JSON.stringify(wabFormData));
    fetcher.submit(formData, { method: "post" });
  }, [fetcher, wabFormData]);

  const resetFormData = useCallback(() => {
    const defaultFormData: NotificationFormData = {
      type: "OrderPlaced" as NotificationType,
      waEnabled: false,
      baEnabled: false,
    };
    setFormData(defaultFormData);
  }, []);

  useEffect(() => {
    if (fetcher.state === "idle" && fetcher.data) {
      const actionData = fetcher.data;
      if (actionData.success) {
        showToast("Operation completed successfully", "success");
        setIsAddModalOpen(false);
        setIsEditModalOpen(false);
        setEditingConfig(null);
        resetFormData();
        setWabFormData(null);
      } else {
        showToast(actionData.error || "Operation failed", "error");
      }
    }
  }, [fetcher.state, fetcher.data]);

  return (
    <div className="container mx-auto p-6">
      {loading && <SpinnerLoader loading={loading} />}
      {wabToken && (
        <div className="flex justify-between items-center mb-6">
          <div>
            <div className="flex flex-row items-center gap-4">
              <h1 className="text-2xl font-bold">WhatsApp Business Account</h1>
              <p className="leading-8 text-blue-500 cursor-pointer" onClick={() => setWabFormData(wabToken)}>Edit</p>
            </div>
            {wabToken.token && <p className="text-sm text-gray-600">Token: {wabToken.token}</p>}
            <p className="text-sm text-gray-600">Connected to: {wabToken.wabMobileNumber}</p>
            <p className="text-sm text-gray-600">Expires at: {wabToken.expiryTime}</p>
            {wabToken.wabPhoneNumberId && <p className="text-sm text-gray-600">Phone Number ID: {wabToken.wabPhoneNumberId}</p>}
          </div>
        </div>
      )}

      <div className="flex justify-between items-center mb-6">

        <div>
          <h1 className="text-2xl font-bold">Notification Configuration</h1>
          {networkName && (
            <p className="text-sm text-gray-600">Network: {networkName}</p>
          )}
        </div>
      </div>

      <div className="flex flex-col my-3 relative">
        <Input
          type="search"
          placeholder="Search by notification type"
          value={searchTerm}
          onChange={(e) => setSearchTerm(e.target.value)}
          className="max-w-sm rounded-full"
        />
      </div>

      <div className="flex justify-between items-center mb-4">
        <p className="text-sm text-gray-600">
          Total Configurations: {filteredConfigs.length} (Raw: {notificationConfigs.length})
        </p>
        <Button onClick={() => setIsAddModalOpen(true)}>
          <Plus className="h-4 w-4 mr-2" />
          Add Notification Config
        </Button>
      </div>

      <Table>
        <TableHeader className="bg-gray-100">
          <TableRow>
            <TableHead>Type</TableHead>
            <TableHead>WhatsApp Enabled</TableHead>
            <TableHead>Business Enabled</TableHead>
            <TableHead>Actions</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {filteredConfigs.length > 0 ? (
            filteredConfigs.map((config) => (
              <NotificationConfigRow
                key={config.id}
                config={config}
                onEdit={handleEdit}
                onDelete={handleDelete}
              />
            ))
          ) : (
            <TableRow>
              <TableCell colSpan={6} className="h-24 text-center">
                {notificationConfigs.length === 0 ? (
                  <div className="text-center">
                    <p className="text-gray-500 mb-2">No notification configurations found.</p>
                    <p className="text-sm text-gray-400">Click "Add Notification Config" to create your first configuration.</p>
                  </div>
                ) : (
                  "No configurations match your search."
                )}
              </TableCell>
            </TableRow>
          )}
        </TableBody>
      </Table>

      {/* Add Modal */}
      <Dialog open={isAddModalOpen} onOpenChange={setIsAddModalOpen}>
        <DialogContent className="sm:max-w-[425px]">
          <DialogHeader>
            <DialogTitle>Add Notification Configuration</DialogTitle>
          </DialogHeader>
          <div className="grid gap-4 py-4">
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="type" className="text-right">
                Type
              </Label>
              <Select
                value={formData.type}
                onValueChange={(value) => setFormData({ ...formData, type: value as NotificationType })}
              >
                <SelectTrigger className="col-span-3">
                  <SelectValue placeholder="Select notification type" />
                </SelectTrigger>
                <SelectContent>
                  {NOTIFICATION_TYPES.map((type) => (
                    <SelectItem key={type} value={type}>
                      {type}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            <div className="grid grid-cols-4 items-center gap-4">
              <Label className="text-right">WhatsApp Enabled</Label>
              <div className="col-span-3">
                <Switch
                  checked={formData.waEnabled}
                  onCheckedChange={(checked) => setFormData({ ...formData, waEnabled: checked })}
                />
              </div>
            </div>
            <div className="grid grid-cols-4 items-center gap-4">
              <Label className="text-right">Business Enabled</Label>
              <div className="col-span-3">
                <Switch
                  checked={formData.baEnabled}
                  onCheckedChange={(checked) => setFormData({ ...formData, baEnabled: checked })}
                />
              </div>
            </div>
          </div>
          <div className="flex justify-end gap-2">
            <Button variant="outline" onClick={() => setIsAddModalOpen(false)}>
              Cancel
            </Button>
            <Button
              onClick={() => handleSubmit(false)}
              disabled={loading || !formData.type}
            >
              Add Configuration
            </Button>
          </div>
        </DialogContent>
      </Dialog>

      {/* Edit Modal */}
      <Dialog open={isEditModalOpen} onOpenChange={setIsEditModalOpen}>
        <DialogContent className="sm:max-w-[425px]">
          <DialogHeader>
            <DialogTitle>Edit Notification Configuration</DialogTitle>
          </DialogHeader>
          <div className="grid gap-4 py-4">
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="edit-type" className="text-right">
                Type
              </Label>
              <Input
                id="edit-type"
                value={formData.type}
                className="col-span-3"
                readOnly
                disabled
              />
            </div>
            <div className="grid grid-cols-4 items-center gap-4">
              <Label className="text-right">WhatsApp Enabled</Label>
              <div className="col-span-3">
                <Switch
                  checked={formData.waEnabled}
                  onCheckedChange={(checked) => setFormData({ ...formData, waEnabled: checked })}
                />
              </div>
            </div>
            <div className="grid grid-cols-4 items-center gap-4">
              <Label className="text-right">Seller Enabled</Label>
              <div className="col-span-3">
                <Switch
                  checked={formData.baEnabled}
                  onCheckedChange={(checked) => setFormData({ ...formData, baEnabled: checked })}
                />
              </div>
            </div>
          </div>
          <div className="flex justify-end gap-2">
            <Button variant="outline" onClick={() => setIsEditModalOpen(false)}>
              Cancel
            </Button>
            <Button
              onClick={() => handleSubmit(true)}
              disabled={loading || !formData.type}
            >
              Update Configuration
            </Button>
          </div>
        </DialogContent>
      </Dialog>

      {/* Edit WAB Modal */}
      <Dialog open={wabFormData !== null} onOpenChange={() => setWabFormData(null)}>
        <DialogContent className="sm:max-w-[425px]">
          <DialogHeader>
            <DialogTitle>Edit WhatsApp Business Account</DialogTitle>
          </DialogHeader>
          <div className="grid gap-4 py-4">
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="wab-token" className="text-right">
                Token
              </Label>
              <Input
                id="wab-token"
                value={wabFormData?.token || ""}
                className="col-span-3"
                onChange={
                  (e) => setWabFormData((prev) => prev ? { ...prev, token: e.target.value } : null)
                }
              />
            </div>
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="wab-mobile-number" className="text-right">
                Connected to
              </Label>
              <Input
                id="wab-mobile-number"
                value={wabFormData?.wabMobileNumber || ""}
                className="col-span-3"
                onChange={
                  (e) => setWabFormData((prev) => prev ? { ...prev, wabMobileNumber: e.target.value } : null)
                }
              />
            </div>
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="wab-expiry-time" className="text-right">
                Expires at
              </Label>
              <Input
                id="wab-expiry-time"
                type="number"
                value={wabFormData?.expiryTime ? wabFormData.expiryTime.toString() : ""}
                className="col-span-3"
                onChange={
                  (e) => setWabFormData((prev) => prev ? { ...prev, expiryTime: Number(e.target.value) || 0 } : null)
                }
              />
            </div>
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="wab-phone-number-id" className="text-right">
                Phone Number ID
              </Label>
              <Input
                id="wab-phone-number-id"
                value={wabFormData?.wabPhoneNumberId || ""}
                className="col-span-3"
                onChange={
                  (e) => setWabFormData((prev) => prev ? { ...prev, wabPhoneNumberId: e.target.value } : null)
                }
              />
            </div>
          </div>
          <div className="flex justify-end gap-2">
            <Button variant="outline" onClick={() => setWabFormData(null)}>
              Close
            </Button>
            <Button
              onClick={handleWabTokenSubmit}
              disabled={loading}
            >
              Update
            </Button>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
}

const NotificationConfigRow = memo(({
  config,
  onEdit,
  onDelete
}: {
  config: NetworkNotificationConfig;
  onEdit: (config: NetworkNotificationConfig) => void;
  onDelete: (config: NetworkNotificationConfig) => void;
}) => (
  <TableRow>
    <TableCell className="font-medium">{config.type}</TableCell>
    <TableCell>
      <Switch checked={config.waEnabled} disabled />
    </TableCell>
    <TableCell>
      <Switch checked={config.baEnabled} disabled />
    </TableCell>
    <TableCell>
      <div className="flex gap-2">
        <Button
          variant="outline"
          size="sm"
          onClick={() => onEdit(config)}
        >
          <Edit className="h-4 w-4" />
        </Button>
        <Button
          variant="outline"
          size="sm"
          onClick={() => onDelete(config)}
          className="text-red-600 hover:text-red-700"
        >
          <Trash2 className="h-4 w-4" />
        </Button>
      </div>
    </TableCell>
  </TableRow>
));