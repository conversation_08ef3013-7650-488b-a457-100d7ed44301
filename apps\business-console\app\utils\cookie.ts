// cookie utils

/**
 * Get a cookie from the request
 * @param request The request object
 * @param name The name of the cookie
 */
export function getServer<PERSON>ookie(request: Request, name: string): string | null {
  const cookieHeader = request.headers.get("Cookie");
  if (!cookieHeader) return null;
  const cookies = cookieHeader.split('; ');
  for (const cookie of cookies) {
    const [key, value] = cookie.split('=');
    if (key === name) {
      return decodeURIComponent(value);
    }
  }
  return null;
}

/**
 * Set a cookie on the response
 * @param response The response object
 * @param name The name of the cookie
 * @param value The value of the cookie
 * @param maxAge The max age of the cookie in seconds
 */
export function setServerCookie(response: Response, name: string, value: string, maxAge: number) {
  const cookie = `${name}=${encodeURIComponent(value)}; Max-Age=${maxAge}; Path=/`;
  response.headers.append("Set-Cookie", cookie);
}

/**
 * Get a cookie from the client
 * @param name The name of the cookie
 */
export function getClientCookie(name: string): string | null {
  if (typeof document === 'undefined') return null;
  const cookies = document.cookie.split('; ');
  for (const cookie of cookies) {
    const [key, value] = cookie.split('=');
    if (key === name) {
      return decodeURIComponent(value);
    }
  }
  return null;
}

/**
 * Set a cookie on the client
 * @param name The name of the cookie
 * @param value The value of the cookie
 * @param maxAge The max age of the cookie in seconds
 */
export function setClientCookie(name: string, value: string, maxAge: number) {
  if (typeof document === 'undefined') return;
  document.cookie = `${name}=${encodeURIComponent(value)}; Max-Age=${maxAge}; Path=/`;
}