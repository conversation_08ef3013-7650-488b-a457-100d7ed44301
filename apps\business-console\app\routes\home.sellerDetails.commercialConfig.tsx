import { useLoaderD<PERSON>, useFetcher } from "@remix-run/react";
import { useState, useCallback, useEffect } from "react";
import { Edit, Save, X } from "lucide-react";
import { withAuth, withResponse } from "~/utils/auth-utils";
import { getSellerCommercialConfig, updateSellerCommercialConfig, SellerCommercialConfig } from "~/services/commercialConfig";
import { useToast } from "~/components/ui/ToastProvider";

interface LoaderData {
  sellerId: number;
  sellerName: string;
  commercialConfig: SellerCommercialConfig;
  error?: string;
}

interface ActionData {
  success: boolean;
  error?: string;
}

export const loader = withAuth(async ({ request }) => {
  const url = new URL(request.url);
  const sellerId = Number(url.searchParams.get("sellerId"));
  const sellerName = url.searchParams.get("sellerName");

  try {
    const response = await getSellerCommercialConfig(sellerId, request);
    const commercialConfig = response?.data || { id: 0 };

    return withResponse({
      sellerId,
      sellerName,
      commercialConfig,
    }, response?.headers);
  } catch (error) {
    console.error("Error loading commercial config:", error);
    return withResponse({
      sellerId,
      sellerName,
      commercialConfig: { id: 0 },
      error: "Failed to load commercial config",
    }, new Headers());
  }
});

export const action = withAuth(async ({ request }) => {
  const formData = await request.formData();
  const sellerId = Number(formData.get("sellerId"));
  const commConfigId = Number(formData.get("commConfigId"));

  const updatedFields: Partial<SellerCommercialConfig> = {};

  for (const [key, value] of formData.entries()) {
    if (key !== "sellerId" && key !== "commConfigId" && key !== "intent") {
      const numericValue = Number(value);
      if (!isNaN(numericValue)) {
        updatedFields[key as keyof SellerCommercialConfig] = numericValue;
      }
    }
  }

  try {
    const response = await updateSellerCommercialConfig(sellerId, commConfigId, updatedFields, request);
    return Response.json({ success: true, data: response.data });
  } catch (error) {
    console.error("Error updating commercial config:", error);
    return Response.json({ success: false, error: "Failed to update commercial config" }, { status: 500 });
  }
});

export default function CommercialConfig() {
  const { sellerId, commercialConfig } = useLoaderData<LoaderData>();
  const fetcher = useFetcher<ActionData>();
  const { showToast } = useToast();

  const [isEditable, setIsEditable] = useState(false);
  const [pendingConfig, setPendingConfig] = useState<Partial<SellerCommercialConfig>>({});
  const [visibleSaveButtons, setVisibleSaveButtons] = useState<Record<string, boolean>>({});

  // Define field configurations with proper labels and grouping
  const commercialConfigFields = [
    // Seller Sales Commission fields
    { label: "Seller Sales Comm per Kg", key: "sellerScPkg", type: "number", prefix: "₹", group: "Seller Sales Commission" },
    { label: "Seller Sales Comm Percentage", key: "sellerScPc", type: "number", prefix: "%", group: "Seller Sales Commission" },
    { label: "Seller Sales Comm Tax", key: "sellerScTax", type: "number", prefix: "%", group: "Seller Sales Commission" },

    // Seller Platform Commission fields
    { label: "Seller Platform Comm per Kg", key: "sellerPcPkg", type: "number", prefix: "₹", group: "Seller Platform Commission" },
    { label: "Seller Platform Comm Percentage", key: "sellerPcPc", type: "number", prefix: "%", group: "Seller Platform Commission" },
    { label: "Seller Platform Comm Monthly Minimum", key: "sellerPcMm", type: "number", prefix: "₹", group: "Seller Platform Commission" },
    { label: "Seller Platform Comm Monthly Fixed", key: "sellerPcMf", type: "number", prefix: "₹", group: "Seller Platform Commission" },
    { label: "Seller Platform Comm Tax", key: "sellerPcTax", type: "number", prefix: "%", group: "Seller Platform Commission" },

    // Supplier Sales Commission fields
    { label: "Supplier Sales Comm per Kg", key: "supplierScPkg", type: "number", prefix: "₹", group: "Supplier Sales Commission" },
    { label: "Supplier Sales Comm Percentage", key: "supplierScPc", type: "number", prefix: "%", group: "Supplier Sales Commission" },
    { label: "Supplier Sales Comm Tax", key: "supplierScTax", type: "number", prefix: "%", group: "Supplier Sales Commission" },

    // Supplier Platform Commission fields
    { label: "Supplier Platform Comm per Kg", key: "supplierPcPkg", type: "number", prefix: "₹", group: "Supplier Platform Commission" },
    { label: "Supplier Platform Comm Percentage", key: "supplierPcPc", type: "number", prefix: "%", group: "Supplier Platform Commission" },
    { label: "Supplier Platform Comm Tax", key: "supplierPcTax", type: "number", prefix: "%", group: "Supplier Platform Commission" },

    // Distributor Handling Charge fields
    { label: "Distributor Handling Charge per Kg", key: "distributorHcPkg", type: "number", prefix: "₹", group: "Distributor Handling Charge" },
    { label: "Distributor Handling Charge Percentage", key: "distributorHcPc", type: "number", prefix: "%", group: "Distributor Handling Charge" },
    { label: "Distributor Handling Charge Tax", key: "distributorHcTax", type: "number", prefix: "%", group: "Distributor Handling Charge" },

    // Seller Handling Charge fields
    { label: "Seller Handling Charge Slab", key: "sellerHcSlab", type: "number", prefix: "₹", group: "Seller Handling Charge" },
    { label: "Seller Handling Charge per Order", key: "sellerHcPo", type: "number", prefix: "₹", group: "Seller Handling Charge" },
    { label: "Seller Handling Charge Percentage", key: "sellerHcPc", type: "number", prefix: "%", group: "Seller Handling Charge" },
    { label: "Seller Handling Charge per Kg", key: "sellerHcPkg", type: "number", prefix: "₹", group: "Seller Handling Charge" },
    { label: "Seller Handling Charge Tax", key: "sellerHcTax", type: "number", prefix: "%", group: "Seller Handling Charge" },

    // Item Tax
    { label: "Item Tax", key: "itemTax", type: "number", prefix: "%", group: "Item Configuration" },

    // Customer Platform Fee fields
    { label: "Customer Platform Fee per Order", key: "cpfPo", type: "number", prefix: "₹", group: "Customer Platform Fee" },
    { label: "Customer Platform Fee Percentage", key: "cpfPc", type: "number", prefix: "%", group: "Customer Platform Fee" },
    { label: "Customer Platform Fee Tax", key: "cpfTax", type: "number", prefix: "%", group: "Customer Platform Fee" },

    // Seller Delivery Charge Tax
    { label: "Seller Delivery Charge Tax", key: "sellerDcTax", type: "number", prefix: "%", group: "Delivery Configuration" },
  ];

  const toggleEdit = useCallback(() => {
    if (!isEditable) {
      setPendingConfig({ ...commercialConfig });
    } else {
      setPendingConfig({});
      setVisibleSaveButtons({});
    }
    setIsEditable(!isEditable);
  }, [isEditable, commercialConfig]);

  const handleConfigChange = (key: keyof SellerCommercialConfig, value: string | number) => {
    setPendingConfig((prev) => {
      let newValue: number = 0;

      if (typeof value === "string") {
        if (value.trim() === "") {
          newValue = 0;
        } else {
          newValue = Math.max(0, Number(value));
        }
      } else {
        newValue = Math.max(0, value);
      }

      return {
        ...prev,
        [key]: newValue,
      };
    });

    setVisibleSaveButtons((prev) => ({
      ...prev,
      [key]: true,
    }));
  };

  const handleSave = (key: keyof SellerCommercialConfig) => {
    const updatedValue = pendingConfig[key];

    if (updatedValue !== undefined && commercialConfig.id > 0) {
      const formData = new FormData();
      formData.append("sellerId", sellerId.toString());
      formData.append("commConfigId", commercialConfig.id.toString());
      formData.append("intent", "updateField");
      formData.append(key, updatedValue.toString());

      fetcher.submit(formData, { method: "POST" });

      setVisibleSaveButtons((prev) => ({
        ...prev,
        [key]: false,
      }));
    }
  };

  useEffect(() => {
    if (fetcher.state === "idle" && fetcher.data?.success) {
      showToast("Field updated successfully", "success");
    } else if (fetcher.state === "idle" && fetcher.data?.error) {
      showToast("Failed to update field", "error");
      console.error("Error updating field:", fetcher.data.error);
    }
  }, [fetcher.state, fetcher.data])

  return (
    <div className='flex flex-col gap-4 border bg-white rounded-xl border-neutral-200 my-4 p-4'>
      <div className="flex flex-col gap-4 border rounded-xl border-neutral-400 p-4 shadow">
        <div className="flex justify-between w-full">
          <div className="text-lg font-semibold text-typography-700">Commercial Configurations</div>
          <button
            className={`inline-flex items-center gap-1 ${commercialConfig.id > 0
                ? "text-blue-600 hover:text-blue-800"
                : "text-gray-400 cursor-not-allowed"
              }`}
            onClick={commercialConfig.id > 0 ? toggleEdit : undefined}
            disabled={commercialConfig.id === 0}
          >
            {isEditable ? (
              <>
                <X className="h-4 w-4" />
                Cancel
              </>
            ) : (
              <>
                <Edit className="h-4 w-4" />
                Edit
              </>
            )}
          </button>
        </div>

        <div className="flex gap-4 items-center">
          <div className="text-md text-typography-500 w-[400px]">Configuration ID:</div>
          <div className="font-semibold text-typography-800">
            {commercialConfig.id > 0 ? commercialConfig.id : "No configuration found"}
          </div>
        </div>

        {commercialConfig.id === 0 && (
          <div className="bg-yellow-50 border border-yellow-200 rounded-md p-3">
            <p className="text-yellow-800 text-sm">
              No commercial configuration found for this seller.
            </p>
          </div>
        )}

        {/* Commercial Config Fields */}
        <div>
          {commercialConfigFields.map(({ label, key, type, prefix }) => (
            <div key={key} className="flex flex-col gap-2 text-md text-typography-400">
              <div className="flex gap-4 items-center">
                <div className="text-md text-typography-500 w-[400px] mt-2">{label}:</div>
                <span className="font-semibold text-typography-800 flex items-center gap-2">
                  <input
                    type={type}
                    value={
                      pendingConfig[key as keyof SellerCommercialConfig] !== undefined
                        ? String(pendingConfig[key as keyof SellerCommercialConfig])
                        : String(commercialConfig[key as keyof SellerCommercialConfig] || 0)
                    }
                    onChange={(e) => {
                      const value = e.target.value.trim();
                      if (value === '') {
                        handleConfigChange(key as keyof SellerCommercialConfig, 0);
                        return;
                      }
                      const numericValue = Math.max(0, Number(value));
                      handleConfigChange(key as keyof SellerCommercialConfig, numericValue);
                    }}
                    className="border border-neutral-400 rounded-md p-1 px-2 mt-2"
                    disabled={!isEditable}
                  />
                  <span className="text-sm">{prefix}</span>
                </span>

                {isEditable && visibleSaveButtons[key] && (
                  <button
                    className="flex flex-row text-blue-500 items-center gap-1 cursor-pointer text-lg font-bold"
                    onClick={() => handleSave(key as keyof SellerCommercialConfig)}
                  >
                    <Save className="h-5 w-5" />
                  </button>
                )}
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
}
