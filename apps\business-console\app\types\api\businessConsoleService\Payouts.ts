export type WalletInfo = {
  walletBalance: number;
  withdrawEnabled: boolean;
};

export type Payout = {
  payoutId: number;
  dateRange: string;
  paymentDate: string; // ISO date string, e.g. "2025-09-03"
  amount: number;
  status: string;
};

export type PayoutDetails = {
  sellerPayout: {
    payoutId: number;
    dateRange: string;
    paymentDate: string; // LocalDate → string (ISO date like "2025-09-03")
    amount: number;
    status: string;
  };
  supplierItemBds: SupplierItemBD[];
  distributorItemBds: DistributorItemBD[];
  orderGroupBds: OrderGroupBD[];
};

export type SupplierItemBD = {
  id: number;
  supplierPayoutId: number;
  sellerItemId: number;
  sellerItemName: string;
  unit: string;
  distributorId: number;
  distributorName: string;
  totalWeight: number;

  // Sales Comm fields
  sc: number;
  scTax: number;
  scTotal: number;

  // Platform Comm fields
  pc: number;
  pcTax: number;
  pcTotal: number;

  // Distributor Handling Charge fields
  dhc: number;
  dhcTax: number;
  dhcTotal: number;

  // Items fields
  itemsStrikeoffAmount: number;
  itemsDiscount: number;
  itemsAmount: number;

  netAmount: number;
};

export type DistributorItemBD = {
  id: number;
  distributorPayoutId: number;
  sellerItemId: number;
  sellerItemName: string;
  unit: string;
  supplierId: number;
  supplierName: string;
  totalWeight: number;

  // Distributor Handling Charge fields
  dhc: number;
  dhcTax: number;
  dhcTotal: number;

  // Items fields
  itemsStrikeoffAmount: number;
  itemsDiscount: number;
  itemsAmount: number;

  netAmount: number;
}

export type OrderGroupBD = {
  id: number;
  ogId: number;
  businessName: string;
  deliveryDate: string; // Timestamp → string (ISO datetime)
  driverName: string;
  agentName: string;
  codAmount: number;
  creditAmount: number;

  netAmount: number;
}

export type WalletHistory = {
  id: number;
  transactionTime: string; // LocalDateTime → ISO datetime string (e.g. "2025-09-04T14:30:00Z")
  creditValue: number;
  debitValue: number;
  balance: number;
  narration: string;
  note: string;
  transactionType: string;
  transactionId: number;
};

export type InitiatePayment = {
  accountNumber: string,
  accountName: string,
  ifscCode: string,
  paymentInitiationId: number
}