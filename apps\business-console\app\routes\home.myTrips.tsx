

import { <PERSON>, json, use<PERSON><PERSON><PERSON>, use<PERSON><PERSON>derD<PERSON> } from "@remix-run/react";
import { format } from "date-fns";
import { CalendarIcon } from "lucide-react";
import { useEffect, useState } from "react";
import { Button } from "~/components/ui/button";
import { Calendar } from "~/components/ui/calendar";
import { Input } from "~/components/ui/input";
import { Popover, PopoverContent, PopoverTrigger } from "~/components/ui/popover";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "~/components/ui/table";
import { TripSummary } from "~/types/api/businessConsoleService/tripSummaryDetails";
import { withAuth, withResponse } from "@utils/auth-utils";
import { getSellerTrips } from "~/services/myTripsService";
import { BcTripSummaryDto } from "~/types/api/businessConsoleService/MyTrips";



export const loader = withAuth(async ({ request }) => {
      try {
            const url = new URL(request.url);
            const date = url.searchParams.get("date") || new Date().toISOString().split("T")[0];

            if (!date) {
                  throw json(
                        { error: "Date is required" },
                        { status: 400 }
                  );
            }

            const response = await getSellerTrips(date, request);
            return withResponse({ data: response.data }, response.headers);
      } catch (error) {
            console.error('Trip summary error:', error);
            throw new Error(`Error fetching trip details: ${error}`);
      }
});

export default function MyTrips() {
      const data = useLoaderData<{ data: BcTripSummaryDto[] }>();
      const [date, setDate] = useState<Date | undefined>(new Date());
      const [searchTerm, setSearchTerm] = useState("")
      const fetcher = useFetcher<{ data: BcTripSummaryDto[] }>()
      const [tripData, setTriData] = useState<BcTripSummaryDto[] | []>(data.data || [])

      const handleSubmit = (date: Date | undefined) => {
            const formData = new FormData();
            const deliveryDate = format(date || "", "yyyy-MM-dd")
            formData.append("date", deliveryDate || "")
            fetcher.submit(formData, { method: "GET" });
      }
      const itemsPerPage = 200
      const [currentPage, setCurrentPage] = useState(1)

      const totalPages = Math.ceil(tripData.length / itemsPerPage);

      const startIndex = (currentPage - 1) * itemsPerPage;

      const currentTrips = tripData.slice(startIndex, startIndex + itemsPerPage);

      useEffect(() => {

            if (fetcher.data) {
                  setTriData(fetcher.data.data)
            }

      }, [fetcher.data?.data])

      const filterTrips = (trip: BcTripSummaryDto) => {
            return (

                  (trip.driverName?.toLowerCase() || "").includes(searchTerm.toLowerCase())
            );
      }


      const handleSetPage = (page: number) => {
            if (page >= 1 && page <= totalPages) {
                  setCurrentPage(page)
            }
      }

      const handleSort = (trip: BcTripSummaryDto) => {

            return trip.tripStatus === "Dispatched"

      }
      return (
            <div className="container mx-auto w-full py-4 sm:py-6" >
                  <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-2 sm:space-y-0">
                        <div>
                              <h1 className="text-2xl sm:text-3xl font-bold text-gray-900 break-words">My Trips</h1>
                              <p className="text-sm sm:text-base text-gray-600 mt-1">View your trips and trip history</p>
                        </div>
                  </div>
                  <div className="flex my-7 space-x-10">
                        <div className=" flex space-x-2">
                              <Popover>
                                    <PopoverTrigger asChild>
                                          <Button variant="outline" className="w-[280px]">
                                                <CalendarIcon className="mr-2 h-4 w-4" />
                                                {date ? format(date, "PPP") : "Pick a date"}
                                          </Button>
                                    </PopoverTrigger>
                                    <PopoverContent className="w-auto p-0" >
                                          <Calendar

                                                mode="single"
                                                selected={date}
                                                onSelect={setDate}
                                                initialFocus
                                          />
                                    </PopoverContent>
                              </Popover>

                              <Button onClick={() => handleSubmit(date)}  >
                                    Get Trips
                              </Button>
                        </div>
                        <Input placeholder="Search By DriverName"
                              value={searchTerm}
                              onChange={(e) => setSearchTerm(e.target.value)}
                              className="max-w-sm"
                        />
                  </div>
                  <Table>
                        <TableHeader className="bg-gray-100">
                              <TableHead>Driver</TableHead>
                              {/* <TableHead>Truck No</TableHead> */}
                              <TableHead>Booked</TableHead>
                              <TableHead>Status</TableHead>
                              <TableHead>Delivered</TableHead>


                        </TableHeader>
                        <TableBody>
                              {currentTrips.length > 0 ? currentTrips.sort((a, b) => {
                                    const Priority: { [key: string]: number } = { Dispatched: 1, Open: 2 };
                                    return (Priority[a.tripStatus] || 3) - (Priority[b.tripStatus] || 3);
                              }).filter((trip) => filterTrips(trip)).map((x) => (
                                    <TableRow key={x.tripId}>
                                          <TableCell>{x.driverName}</TableCell>



                                          <TableCell><p>{x.totalOrders}</p>

                                          </TableCell>
                                          <TableCell className={x.tripStatus === "Dispatched" ? "text-red-500" : x.tripStatus === "Open" ? "text-orange-500" : "text-green-600"}>{x.tripStatus}</TableCell>
                                          <TableCell>{x.totalDeliveredOrders}</TableCell>


                                    </TableRow>
                              )

                              ) : (
                                    <TableRow>
                                          <TableCell
                                                colSpan={9}
                                                className="h-24 text-center"
                                          >
                                                No results.
                                          </TableCell>
                                    </TableRow>
                              )
                              }
                        </TableBody>
                  </Table>
                  <div className="flex items-center space-x-2 my-2">
                        <Button
                              variant="outline"
                              size="sm"
                              onClick={() => handleSetPage(currentPage - 1)}
                              disabled={currentPage === 1}
                        >
                              Previous
                        </Button>
                        <span className="text-gray-700">
                              Page {currentPage} of {totalPages}
                        </span>
                        <Button
                              variant="outline"
                              size="sm"
                              onClick={() =>
                                    handleSetPage(currentPage + 1)
                              }
                              disabled={currentPage === totalPages}
                        >
                              Next
                        </Button>
                  </div>
            </div>
      )
}
