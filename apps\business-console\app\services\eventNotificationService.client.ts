// app/services/eventNotificationService.client.ts
// Client-side types and configurations for event notifications

// Re-export types from server service for client use
export type {
  EventType,
  BaseEventPayload,
  OrderDelayedPayload,
  RefundProcessedPayload,
  EventPayload,
  SendEventNotificationRequest,
  SendEventNotificationResponse,
} from "./eventNotificationService.server";

// Import types for internal use
import type {
  EventType,
  OrderDelayedPayload,
  RefundProcessedPayload,
  EventPayload,
} from "./eventNotificationService.server";

// Event configuration for UI
export interface EventConfig {
  type: EventType;
  label: string;
  description: string;
  fields: EventField[];
}

export interface EventField {
  name: string;
  label: string;
  type: "text" | "textarea" | "select";
  required: boolean;
  placeholder?: string;
  options?: { value: string; label: string }[];
}

// Event configurations
export const EVENT_CONFIGS: EventConfig[] = [
  {
    type: "ORDER_DELAYED",
    label: "Order Delayed",
    description: "Notify customer about order delay with new ETA and reason",
    fields: [
      {
        name: "newEta",
        label: "New ETA",
        type: "text",
        required: true,
        placeholder: "e.g., 45 minutes, 2 hours"
      },
      {
        name: "reason",
        label: "Reason",
        type: "textarea",
        required: true,
        placeholder: "e.g., Heavy traffic in your area, Weather conditions"
      }
    ]
  },
  {
    type: "REFUND_PROCESSED",
    label: "Process Refund",
    description: "Notify customer about refund processing",
    fields: [
      {
        name: "refundAmount",
        label: "Refund Amount",
        type: "text",
        required: true,
        placeholder: "e.g., 299.00"
      },
      {
        name: "refundTo",
        label: "Refund To",
        type: "select",
        required: true,
        options: [
          { value: "Original payment method", label: "Original payment method" },
          { value: "Wallet", label: "Wallet" },
          { value: "Bank", label: "Bank" },
        ]
      },
      {
        name: "tat",
        label: "Turn Around Time (TAT)",
        type: "text",
        required: true,
        placeholder: "e.g., 3-5 business days"
      }
    ]
  }
];

/**
 * Get event configuration by type
 */
export function getEventConfig(eventType: EventType): EventConfig | undefined {
  return EVENT_CONFIGS.find(config => config.type === eventType);
}

/**
 * Get all available event types
 */
export function getAvailableEventTypes(): EventConfig[] {
  return EVENT_CONFIGS;
}

/**
 * Validate event payload based on event type
 */
export function validateEventPayload(
  eventType: EventType,
  payload: Partial<EventPayload>
): { isValid: boolean; errors: string[] } {
  const config = getEventConfig(eventType);
  if (!config) {
    return { isValid: false, errors: ["Invalid event type"] };
  }

  const errors: string[] = [];

  // Check required fields
  config.fields.forEach(field => {
    if (field.required) {
      const value = (payload as Record<string, unknown>)[field.name];
      if (!value || (typeof value === "string" && value.trim() === "")) {
        errors.push(`${field.label} is required`);
      }
    }
  });

  // Type-specific validations
  if (eventType === "ORDER_DELAYED") {
    const orderDelayedPayload = payload as Partial<OrderDelayedPayload>;
    if (orderDelayedPayload.newEta && !/^\d+\s*(minutes?|hours?|mins?|hrs?)$/i.test(orderDelayedPayload.newEta)) {
      errors.push("New ETA should be in format like '45 minutes' or '2 hours'");
    }
  }

  if (eventType === "REFUND_PROCESSED") {
    const refundPayload = payload as Partial<RefundProcessedPayload>;
    if (refundPayload.refundAmount && !/^\d+(\.\d{1,2})?$/.test(refundPayload.refundAmount)) {
      errors.push("Refund amount should be a valid number (e.g., 299.00)");
    }
  }

  return {
    isValid: errors.length === 0,
    errors
  };
}
