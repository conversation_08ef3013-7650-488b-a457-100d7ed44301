import pkg from 'file-saver';
const { saveAs } = pkg;

// Function to convert array to CSV string
const arrayToCSV = (data: any[]): string => {
  if (!data || data.length === 0) return 'No data available\n';

  const headers = Object.keys(data[0]);

  const escapeValue = (value: any): string => {
    if (value === null) return 'null';
    if (value === undefined) return 'undefined';

    // If value is an object or array, JSON stringify it
    if (typeof value === 'object') {
      return `"${JSON.stringify(value).replace(/"/g, '""')}"`;
    }

    // Convert other types (number, boolean) to string
    const stringValue = String(value);

    // If value contains commas, quotes, or newlines, quote it and escape quotes
    if (/[",\n]/.test(stringValue)) {
      return `"${stringValue.replace(/"/g, '""')}"`;
    }

    return stringValue;
  };

  // Create header row
  const headerRow = headers.map(escapeValue).join(',');

  // Create data rows
  const dataRows = data.map(item =>
    headers.map(key => escapeValue(item[key])).join(',')
  );

  return `${headerRow}\n${dataRows.join('\n')}`;
};

// Function to generate and download Excel (CSV) file
export const downloadExcelAsCSV = (data: any[], name:string) => {
  try {
    const CSV = arrayToCSV(data || []);
    
    // Create a Blob and trigger download
    const blob = new Blob([CSV], { type: 'text/csv;charset=utf-8' });
    saveAs(blob, `${name || 'export_data'}.csv`);
    
  } catch (error) {
    console.error('Error generating or downloading CSV file:', error);
  }
};