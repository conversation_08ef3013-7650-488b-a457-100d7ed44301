import "./chunk-VRMXEQCD.js";

// node_modules/@googlemaps/polyline-codec/dist/index.esm.js
var decode = function(encodedPath, precision) {
  if (precision === void 0) {
    precision = 5;
  }
  var factor = Math.pow(10, precision);
  var len = encodedPath.length;
  var path = new Array(Math.floor(encodedPath.length / 2));
  var index = 0;
  var lat = 0;
  var lng = 0;
  var pointIndex = 0;
  for (; index < len; ++pointIndex) {
    var result = 1;
    var shift = 0;
    var b = void 0;
    do {
      b = encodedPath.charCodeAt(index++) - 63 - 1;
      result += b << shift;
      shift += 5;
    } while (b >= 31);
    lat += result & 1 ? ~(result >> 1) : result >> 1;
    result = 1;
    shift = 0;
    do {
      b = encodedPath.charCodeAt(index++) - 63 - 1;
      result += b << shift;
      shift += 5;
    } while (b >= 31);
    lng += result & 1 ? ~(result >> 1) : result >> 1;
    path[pointIndex] = [lat / factor, lng / factor];
  }
  path.length = pointIndex;
  return path;
};
var encode = function(path, precision) {
  if (precision === void 0) {
    precision = 5;
  }
  var factor = Math.pow(10, precision);
  var transform = function latLngToFixed(latLng) {
    if (!Array.isArray(latLng)) {
      latLng = [latLng.lat, latLng.lng];
    }
    return [round(latLng[0] * factor), round(latLng[1] * factor)];
  };
  return polylineEncodeLine(path, transform);
};
var polylineEncodeLine = function(array, transform) {
  var v = [];
  var start = [0, 0];
  var end;
  for (var i = 0, I = array.length; i < I; ++i) {
    end = transform(array[i]);
    polylineEncodeSigned(round(end[0]) - round(start[0]), v);
    polylineEncodeSigned(round(end[1]) - round(start[1]), v);
    start = end;
  }
  return v.join("");
};
var polylineEncodeSigned = function(value, array) {
  return polylineEncodeUnsigned(value < 0 ? ~(value << 1) : value << 1, array);
};
var polylineEncodeUnsigned = function(value, array) {
  while (value >= 32) {
    array.push(String.fromCharCode((32 | value & 31) + 63));
    value >>= 5;
  }
  array.push(String.fromCharCode(value + 63));
  return array;
};
var round = function(v) {
  return Math.floor(Math.abs(v) + 0.5) * (v >= 0 ? 1 : -1);
};
export {
  decode,
  encode,
  polylineEncodeLine
};
//# sourceMappingURL=@googlemaps_polyline-codec.js.map
