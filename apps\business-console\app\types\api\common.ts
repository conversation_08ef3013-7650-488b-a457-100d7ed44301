// (/bc/seller/theme) api
export interface NetworkTheme {
  id: number;
  domain: string;
  businessLogo: string;
  homePageBanner: string;
  pwaAppIcon: string;
  footerAppIcon: string;
  networkId: number;
  multiSeller: boolean;
  defaultSellerId: number;
  wabEnabled: boolean;
  wabMobileNumber: string;
  defaultStartPage: string;
  imageBaseUrl: string;
  networkType: "B2B" | "B2C";
  ondcDomain: "RET10" | "RET11";
  platformId: number;
}
