import { LoaderFunction } from "@remix-run/node";
import { useLoaderData, useNavigate } from "@remix-run/react";
import { useState } from "react";
import { getPayoutDetails } from "~/services/payments";
import type { DistributorItemBD, OrderGroupBD, PayoutDetails, SupplierItemBD } from "~/types/api/businessConsoleService/Payouts";
import { withAuth, withResponse } from "~/utils/auth-utils";
import { Card, CardContent, CardHeader, CardTitle } from "~/components/ui/card";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "~/components/ui/table";
import { Badge } from "~/components/ui/badge";
import ResponsivePagination from "~/components/ui/responsivePagination";
import { Button } from "~/components/ui/button";
import { ArrowLeft, Download, ChevronDown, ChevronRight } from "lucide-react";
import { downloadExcelAsCSV } from "~/utils/excel";
import { removerZeroSumKeys } from "~/utils/format";

interface LoaderData {
  payoutDetails: PayoutDetails;
}

export const loader: LoaderFunction = withAuth(async ({ request, params }) => {
  const payoutId = params?.id;
  if (!payoutId) {
    throw new Response("Invalid payout ID", { status: 400 });
  }

  try {
    const response = await getPayoutDetails(Number(payoutId), request);
    return withResponse({
      payoutDetails: response.data
    }, response?.headers);
  } catch (error) {
    console.error("Error in loader:", error);
    throw new Response("Failed to get payout details", { status: 500 });
  }
});

// Utility functions
const formatDate = (dateString: string) => {
  return new Date(dateString).toLocaleDateString('en-IN', {
    year: 'numeric',
    month: 'short',
    day: 'numeric'
  });
};

const formatDateTime = (dateString: string) => {
  return new Date(dateString).toLocaleString('en-IN', {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  });
};

const formatCurrency = (amount: number) => {
  return new Intl.NumberFormat('en-IN', {
    style: 'decimal',
    minimumFractionDigits: 2,
    maximumFractionDigits: 2,
  }).format(amount);
};

const getStatusBadge = (status: string) => {
  const statusColors: Record<string, string> = {
    'PAID': 'bg-green-100 text-green-800',
    'PENDING': 'bg-yellow-100 text-yellow-800',
    'FAILED': 'bg-red-100 text-red-800',
    'PROCESSING': 'bg-blue-100 text-blue-800'
  };

  return (
    <Badge className={statusColors[status] || 'bg-gray-100 text-gray-800'}>
      {status}
    </Badge>
  );
};

export function showDecimalAsSubscript(value: string) {
  if (!value || !value.includes('.')) return value;
  const [intPart, fracPart] = value.split('.');
  return (
    <span>
      {intPart}
      {fracPart !== undefined && (
        <span className="text-xs">.{fracPart}</span>
      )}
    </span>
  )
}

// Summary calculation functions
const getSupplierItemsSummary = (data: SupplierItemBD[]) => {
  if (!data) return { totalItems: 0, totalNetAmount: 0, uniqueDistributors: 0 };
  const totalItems = data.length;
  const totalNetAmount = data.reduce((sum, item) => sum + (item.netAmount || 0), 0);
  const uniqueDistributors = new Set(data.map(item => item.distributorName)).size;

  return {
    totalItems,
    totalNetAmount,
    uniqueDistributors
  };
};

const getDistributorItemsSummary = (data: DistributorItemBD[]) => {
  if (!data) return { totalItems: 0, totalNetAmount: 0, uniqueSuppliers: 0 };
  const totalItems = data.length;
  const totalNetAmount = data.reduce((sum, item) => sum + (item.netAmount || 0), 0);
  const uniqueSuppliers = new Set(data.map(item => item.supplierName)).size;

  return {
    totalItems,
    totalNetAmount,
    uniqueSuppliers
  };
};

const getOrderGroupsSummary = (data: OrderGroupBD[]) => {
  if (!data) return { totalGroups: 0, totalCodAmount: 0, totalCreditAmount: 0, totalNetAmount: 0 };
  const totalGroups = data.length;
  const totalCodAmount = data.reduce((sum, item) => sum + (item.codAmount || 0), 0);
  const totalCreditAmount = data.reduce((sum, item) => sum + (item.creditAmount || 0), 0);
  const totalNetAmount = data.reduce((sum, item) => sum + (item.netAmount || 0), 0);

  return {
    totalGroups,
    totalCodAmount,
    totalCreditAmount,
    totalNetAmount
  };
};

// Collapsible Section Component
interface CollapsibleSectionProps {
  title: string;
  count: number;
  isExpanded: boolean;
  onToggle: () => void;
  summaryContent: React.ReactNode;
  children: React.ReactNode;
  downloadAction?: () => void;
}

const CollapsibleSection = ({
  title,
  count,
  isExpanded,
  onToggle,
  summaryContent,
  children,
  downloadAction
}: CollapsibleSectionProps) => {
  return (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between cursor-pointer" onClick={onToggle}>
        <div className="flex items-center space-x-2">
          {isExpanded ? (
            <ChevronDown className="h-5 w-5 text-muted-foreground" />
          ) : (
            <ChevronRight className="h-5 w-5 text-muted-foreground" />
          )}
          <CardTitle className="text-xl">
            {title} ({count})
          </CardTitle>
        </div>

        <div className="flex items-center space-x-2">
          {downloadAction && (
            <Button
              size="sm"
              variant="outline"
              onClick={(e) => {
                e.stopPropagation();
                downloadAction();
              }}
            >
              <Download className="h-4 w-4 mr-2" />
              Download
            </Button>
          )}
        </div>
      </CardHeader>

      <CardContent>
        {isExpanded ? (
          children
        ) : (
          <div className="py-4">
            {summaryContent}
          </div>
        )}
      </CardContent>
    </Card>
  );
};

// Pagination hook
const usePagination = (data: any[], itemsPerPage: number = 10) => {
  const [currentPage, setCurrentPage] = useState(0);
  const totalPages = Math.ceil(data.length / itemsPerPage);

  const paginatedData = data.slice(
    currentPage * itemsPerPage,
    (currentPage + 1) * itemsPerPage
  );

  return {
    currentPage,
    totalPages,
    paginatedData,
    setCurrentPage
  };
};

// Table components
const SupplierItemsTable = ({ data }: { data: SupplierItemBD[] }) => {
  const { currentPage, totalPages, paginatedData, setCurrentPage } = usePagination(data);

  return (
    <div className="space-y-4">
      <div className="rounded-md border">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Item Name</TableHead>
              <TableHead>Distributor</TableHead>
              {paginatedData[0]?.totalWeight !== undefined && <TableHead>Weight</TableHead>}
              {paginatedData[0]?.sc !== undefined && <TableHead className="whitespace-nowrap text-right">SC (₹)</TableHead>}
              {paginatedData[0]?.scTax !== undefined && <TableHead className="whitespace-nowrap text-right">SC Tax (₹)</TableHead>}
              {paginatedData[0]?.scTotal !== undefined && <TableHead className="whitespace-nowrap text-right">SC Total (₹)</TableHead>}
              {paginatedData[0]?.pc !== undefined && <TableHead className="whitespace-nowrap text-right">PC (₹)</TableHead>}
              {paginatedData[0]?.pcTax !== undefined && <TableHead className="whitespace-nowrap text-right">PC Tax (₹)</TableHead>}
              {paginatedData[0]?.pcTotal !== undefined && <TableHead className="whitespace-nowrap text-right">PC Total (₹)</TableHead>}
              {paginatedData[0]?.dhc !== undefined && <TableHead className="whitespace-nowrap text-right">DHC (₹)</TableHead>}
              {paginatedData[0]?.dhcTax !== undefined && <TableHead className="whitespace-nowrap text-right">DHC Tax (₹)</TableHead>}
              {paginatedData[0]?.dhcTotal !== undefined && <TableHead className="whitespace-nowrap text-right">DHC Total (₹)</TableHead>}
              {paginatedData[0]?.itemsStrikeoffAmount !== undefined && <TableHead className="whitespace-nowrap text-right">Strikeoff Amount (₹)</TableHead>}
              {paginatedData[0]?.itemsDiscount !== undefined && <TableHead className="whitespace-nowrap text-right">Items Discount (₹)</TableHead>}
              {paginatedData[0]?.itemsAmount !== undefined && <TableHead className="whitespace-nowrap text-right">Items Amount (₹)</TableHead>}
              {paginatedData[0]?.netAmount !== undefined && <TableHead className="whitespace-nowrap text-right bg-muted/80">Net Amount (₹)</TableHead>}
            </TableRow>
          </TableHeader>
          <TableBody>
            {paginatedData.map((item) => (
              <TableRow key={item.id}>
                <TableCell className="font-medium">{item.sellerItemName}</TableCell>
                <TableCell>{item.distributorName}</TableCell>
                {item.totalWeight !== undefined && <TableCell className="whitespace-nowrap">{item.totalWeight} {item.unit}</TableCell>}
                {item.sc !== undefined && <TableCell className="whitespace-nowrap" align="right">{item.sc === 0 ? "-" : showDecimalAsSubscript(formatCurrency(item.sc))}</TableCell>}
                {item.scTax !== undefined && <TableCell className="whitespace-nowrap" align="right">{item.scTax === 0 ? "-" : showDecimalAsSubscript(formatCurrency(item.scTax))}</TableCell>}
                {item.scTotal !== undefined && <TableCell className="whitespace-nowrap" align="right">{item.scTotal === 0 ? "-" : showDecimalAsSubscript(formatCurrency(item.scTotal))}</TableCell>}
                {item.pc !== undefined && <TableCell className="whitespace-nowrap" align="right">{item.pc === 0 ? "-" : showDecimalAsSubscript(formatCurrency(item.pc))}</TableCell>}
                {item.pcTax !== undefined && <TableCell className="whitespace-nowrap" align="right">{item.pcTax === 0 ? "-" : showDecimalAsSubscript(formatCurrency(item.pcTax))}</TableCell>}
                {item.pcTotal !== undefined && <TableCell className="whitespace-nowrap" align="right">{item.pcTotal === 0 ? "-" : showDecimalAsSubscript(formatCurrency(item.pcTotal))}</TableCell>}
                {item.dhc !== undefined && <TableCell className="whitespace-nowrap" align="right">{item.dhc === 0 ? "-" : showDecimalAsSubscript(formatCurrency(item.dhc))}</TableCell>}
                {item.dhcTax !== undefined && <TableCell className="whitespace-nowrap" align="right">{item.dhcTax === 0 ? "-" : showDecimalAsSubscript(formatCurrency(item.dhcTax))}</TableCell>}
                {item.dhcTotal !== undefined && <TableCell className="whitespace-nowrap" align="right">{item.dhcTotal === 0 ? "-" : showDecimalAsSubscript(formatCurrency(item.dhcTotal))}</TableCell>}
                {item.itemsStrikeoffAmount !== undefined && <TableCell className="whitespace-nowrap" align="right">{item.itemsStrikeoffAmount === 0 ? "-" : showDecimalAsSubscript(formatCurrency(item.itemsStrikeoffAmount))}</TableCell>}
                {item.itemsDiscount !== undefined && <TableCell className="whitespace-nowrap" align="right">{item.itemsDiscount === 0 ? "-" : showDecimalAsSubscript(formatCurrency(item.itemsDiscount))}</TableCell>}
                {item.itemsAmount !== undefined && <TableCell className="whitespace-nowrap" align="right">{item.itemsAmount === 0 ? "-" : showDecimalAsSubscript(formatCurrency(item.itemsAmount))}</TableCell>}
                {item.netAmount !== undefined && <TableCell className="whitespace-nowrap bg-muted/80" align="right">{item.netAmount === 0 ? "-" : showDecimalAsSubscript(formatCurrency(item.netAmount))}</TableCell>}
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </div>
      {totalPages > 1 && (
        <ResponsivePagination
          currentPage={currentPage}
          totalPages={totalPages}
          onPageChange={setCurrentPage}
        />
      )}
    </div>
  );
};

const DistributorItemsTable = ({ data }: { data: DistributorItemBD[] }) => {
  const { currentPage, totalPages, paginatedData, setCurrentPage } = usePagination(data);

  return (
    <div className="space-y-4">
      <div className="rounded-md border">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Item Name</TableHead>
              <TableHead>Supplier</TableHead>
              {paginatedData[0]?.totalWeight !== undefined && <TableHead>Weight</TableHead>}
              {paginatedData[0]?.dhc !== undefined && <TableHead className="whitespace-nowrap text-right">DHC (₹)</TableHead>}
              {paginatedData[0]?.dhcTax !== undefined && <TableHead className="whitespace-nowrap text-right">DHC Tax (₹)</TableHead>}
              {paginatedData[0]?.dhcTotal !== undefined && <TableHead className="whitespace-nowrap text-right">DHC Total (₹)</TableHead>}
              {paginatedData[0]?.itemsStrikeoffAmount !== undefined && <TableHead className="whitespace-nowrap text-right">Strikeoff Amount (₹)</TableHead>}
              {paginatedData[0]?.itemsDiscount !== undefined && <TableHead className="whitespace-nowrap text-right">Item Discount (₹)</TableHead>}
              {paginatedData[0]?.itemsAmount !== undefined && <TableHead className="whitespace-nowrap text-right">Items Amount (₹)</TableHead>}
              {paginatedData[0]?.netAmount !== undefined && <TableHead className="whitespace-nowrap text-right bg-muted/80">Net Amount (₹)</TableHead>}
            </TableRow>
          </TableHeader>
          <TableBody>
            {paginatedData.map((item) => (
              <TableRow key={item.id}>
                <TableCell className="font-medium">{item.sellerItemName}</TableCell>
                <TableCell>{item.supplierName}</TableCell>
                {item.totalWeight !== undefined && <TableCell className="whitespace-nowrap">{item.totalWeight} {item.unit}</TableCell>}
                {item.dhc !== undefined && <TableCell className="whitespace-nowrap" align="right">{item.dhc === 0 ? "-" : showDecimalAsSubscript(formatCurrency(item.dhc))}</TableCell>}
                {item.dhcTax !== undefined && <TableCell className="whitespace-nowrap" align="right">{item.dhcTax === 0 ? "-" : showDecimalAsSubscript(formatCurrency(item.dhcTax))}</TableCell>}
                {item.dhcTotal !== undefined && <TableCell className="whitespace-nowrap" align="right">{item.dhcTotal === 0 ? "-" : showDecimalAsSubscript(formatCurrency(item.dhcTotal))}</TableCell>}
                {item.itemsStrikeoffAmount !== undefined && <TableCell className="whitespace-nowrap" align="right">{item.itemsStrikeoffAmount === 0 ? "-" : showDecimalAsSubscript(formatCurrency(item.itemsStrikeoffAmount))}</TableCell>}
                {item.itemsDiscount !== undefined && <TableCell className="whitespace-nowrap" align="right">{item.itemsDiscount === 0 ? "-" : showDecimalAsSubscript(formatCurrency(item.itemsDiscount))}</TableCell>}
                {item.itemsAmount !== undefined && <TableCell className="whitespace-nowrap" align="right">{item.itemsAmount === 0 ? "-" : showDecimalAsSubscript(formatCurrency(item.itemsAmount))}</TableCell>}
                {item.netAmount !== undefined && <TableCell className="whitespace-nowrap bg-muted/80" align="right">{item.netAmount === 0 ? "-" : showDecimalAsSubscript(formatCurrency(item.netAmount))}</TableCell>}
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </div>
      {totalPages > 1 && (
        <ResponsivePagination
          currentPage={currentPage}
          totalPages={totalPages}
          onPageChange={setCurrentPage}
        />
      )}
    </div>
  );
};

const OrderGroupsTable = ({ data }: { data: OrderGroupBD[] }) => {
  const { currentPage, totalPages, paginatedData, setCurrentPage } = usePagination(data);

  return (
    <div className="space-y-4">
      <div className="rounded-md border">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Order Group ID</TableHead>
              <TableHead>Business Name</TableHead>
              <TableHead>Delivery Date</TableHead>
              <TableHead>Driver</TableHead>
              <TableHead>Agent</TableHead>
              {paginatedData[0]?.codAmount !== undefined && <TableHead className="whitespace-nowrap text-right">COD Amount (₹)</TableHead>}
              {paginatedData[0]?.creditAmount !== undefined && <TableHead className="whitespace-nowrap text-right">Credit Amount (₹)</TableHead>}
              {paginatedData[0]?.netAmount !== undefined && <TableHead className="whitespace-nowrap text-right bg-muted/80">Net Amount (₹)</TableHead>}
            </TableRow>
          </TableHeader>
          <TableBody>
            {paginatedData.map((item) => (
              <TableRow key={item.id}>
                <TableCell className="font-medium">{item.ogId}</TableCell>
                <TableCell>{item.businessName}</TableCell>
                <TableCell className="whitespace-nowrap">{formatDate(item.deliveryDate)}</TableCell>
                <TableCell>{item.driverName}</TableCell>
                <TableCell>{item.agentName}</TableCell>
                {item.codAmount !== undefined && <TableCell align="right">{item.codAmount === 0 ? "-" : showDecimalAsSubscript(formatCurrency(item.codAmount))}</TableCell>}
                {item.creditAmount !== undefined && <TableCell align="right">{item.creditAmount === 0 ? "-" : showDecimalAsSubscript(formatCurrency(item.creditAmount))}</TableCell>}
                {item.netAmount !== undefined && <TableCell align="right" className="bg-muted/80">{item.netAmount === 0 ? "-" : showDecimalAsSubscript(formatCurrency(item.netAmount))}</TableCell>}
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </div>
      {totalPages > 1 && (
        <ResponsivePagination
          currentPage={currentPage}
          totalPages={totalPages}
          onPageChange={setCurrentPage}
        />
      )}
    </div>
  );
};

export default function PayoutDetails() {
  const { payoutDetails } = useLoaderData<LoaderData>();
  const navigate = useNavigate();

  const [expandedSections, setExpandedSections] = useState({
    supplierItems: false,
    distributorItems: false,
    orderGroups: false
  });

  const correctedSupplierItemBds = removerZeroSumKeys(payoutDetails.supplierItemBds)
  const correctedDistributorItemBds = removerZeroSumKeys(payoutDetails.distributorItemBds)
  const correctedOrderGroupBds = removerZeroSumKeys(payoutDetails.orderGroupBds)

  // Calculate summaries
  const supplierSummary = getSupplierItemsSummary(correctedSupplierItemBds);
  const distributorSummary = getDistributorItemsSummary(correctedDistributorItemBds);
  const orderGroupsSummary = getOrderGroupsSummary(correctedOrderGroupBds);

  const toggleSection = (section: keyof typeof expandedSections) => {
    setExpandedSections(prev => ({
      ...prev,
      [section]: !prev[section]
    }));
  };

  return (
    <div className="container mx-auto p-4 space-y-6">
      {/* Page Header */}
      <div>
        <Button variant="secondary" size="sm" onClick={() => navigate(-1)}>
          <ArrowLeft className="h-4 w-4 mr-2" />
          Go Back
        </Button>
        <div className="flex items-center justify-between sm:px-3 mt-3">
          <h1 className="text-2xl font-bold">Payout Details</h1>
          <Badge variant="outline" className="text-base px-3 py-1">
            ID: {payoutDetails?.sellerPayout?.payoutId}
          </Badge>
        </div>
      </div>

      {/* Seller Payout Details */}
      <Card>
        <CardHeader>
          <CardTitle className="text-xl">Payout Summary</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <div className="space-y-1">
              <p className="text-sm font-medium text-muted-foreground">Date Range</p>
              <p className="text-lg font-semibold">{payoutDetails?.sellerPayout?.dateRange}</p>
            </div>
            <div className="space-y-1">
              <p className="text-sm font-medium text-muted-foreground">Payment Date</p>
              <p className="text-lg font-semibold">{formatDate(payoutDetails?.sellerPayout?.paymentDate)}</p>
            </div>
            <div className="space-y-1">
              <p className="text-sm font-medium text-muted-foreground">Amount</p>
              <p className="text-lg font-semibold text-green-600">₹{formatCurrency(payoutDetails?.sellerPayout?.amount)}</p>
            </div>
            <div className="space-y-1">
              <p className="text-sm font-medium text-muted-foreground">Status</p>
              <div>{getStatusBadge(payoutDetails?.sellerPayout?.status)}</div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Supplier Items Section */}
      {payoutDetails?.supplierItemBds?.length > 0 && (
        <CollapsibleSection
          title="My Supplies"
          count={payoutDetails?.supplierItemBds?.length}
          isExpanded={expandedSections.supplierItems}
          onToggle={() => toggleSection('supplierItems')}
          downloadAction={() => downloadExcelAsCSV(payoutDetails?.supplierItemBds, "supplier_items")}
          summaryContent={
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="space-y-1">
                <p className="text-sm font-medium text-muted-foreground">Total Items</p>
                <p className="text-lg font-semibold">{supplierSummary.totalItems}</p>
              </div>
              <div className="space-y-1">
                <p className="text-sm font-medium text-muted-foreground">Unique Distributors</p>
                <p className="text-lg font-semibold">{supplierSummary.uniqueDistributors}</p>
              </div>
              <div className="space-y-1">
                <p className="text-sm font-medium text-muted-foreground">Total Net Amount</p>
                <p className="text-lg font-semibold text-green-600">₹{formatCurrency(supplierSummary.totalNetAmount)}</p>
              </div>
            </div>
          }
        >
          <SupplierItemsTable data={correctedSupplierItemBds} />
        </CollapsibleSection>
      )}

      {/* Distributor Items Section */}
      {payoutDetails?.distributorItemBds?.length > 0 && (
        <CollapsibleSection
          title="Handling Revenue"
          count={payoutDetails?.distributorItemBds?.length}
          isExpanded={expandedSections.distributorItems}
          onToggle={() => toggleSection('distributorItems')}
          downloadAction={() => downloadExcelAsCSV(payoutDetails?.distributorItemBds, "distributor_items")}
          summaryContent={
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="space-y-1">
                <p className="text-sm font-medium text-muted-foreground">Total Items</p>
                <p className="text-lg font-semibold">{distributorSummary.totalItems}</p>
              </div>
              <div className="space-y-1">
                <p className="text-sm font-medium text-muted-foreground">Unique Suppliers</p>
                <p className="text-lg font-semibold">{distributorSummary.uniqueSuppliers}</p>
              </div>
              <div className="space-y-1">
                <p className="text-sm font-medium text-muted-foreground">Total Net Amount</p>
                <p className="text-lg font-semibold text-green-600">₹{formatCurrency(distributorSummary.totalNetAmount)}</p>
              </div>
            </div>
          }
        >
          <DistributorItemsTable data={correctedDistributorItemBds} />
        </CollapsibleSection>
      )}

      {/* Order Groups Section */}
      {payoutDetails?.orderGroupBds?.length > 0 && (
        <CollapsibleSection
          title="Cod & Credit"
          count={payoutDetails?.orderGroupBds?.length}
          isExpanded={expandedSections.orderGroups}
          onToggle={() => toggleSection('orderGroups')}
          downloadAction={() => downloadExcelAsCSV(payoutDetails?.orderGroupBds, "order_groups")}
          summaryContent={
            <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
              <div className="space-y-1">
                <p className="text-sm font-medium text-muted-foreground">Total Groups</p>
                <p className="text-lg font-semibold">{orderGroupsSummary.totalGroups}</p>
              </div>
              <div className="space-y-1">
                <p className="text-sm font-medium text-muted-foreground">COD Amount</p>
                <p className="text-lg font-semibold text-blue-600">₹{formatCurrency(orderGroupsSummary.totalCodAmount)}</p>
              </div>
              <div className="space-y-1">
                <p className="text-sm font-medium text-muted-foreground">Credit Amount</p>
                <p className="text-lg font-semibold text-orange-600">₹{formatCurrency(orderGroupsSummary.totalCreditAmount)}</p>
              </div>
              <div className="space-y-1">
                <p className="text-sm font-medium text-muted-foreground">Total Net Amount</p>
                <p className="text-lg font-semibold text-green-600">₹{formatCurrency(orderGroupsSummary.totalNetAmount)}</p>
              </div>
            </div>
          }
        >
          <OrderGroupsTable data={correctedOrderGroupBds} />
        </CollapsibleSection>
      )}
    </div>
  );
};