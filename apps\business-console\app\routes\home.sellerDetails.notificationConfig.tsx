import { ActionFunction, LoaderFunction, json } from "@remix-run/node";
import { useF<PERSON>cher, useLoaderData, useNavigate } from "@remix-run/react";
import { useEffect, useState, useCallback, useMemo, memo } from "react";
import {
  getSellerNotificationConfigs,
  createSellerNotificationConfig,
  updateSellerNotificationConfig,
  deleteSellerNotificationConfig,
  NotificationConfig
} from "~/services/notificationConfig";
import { withAuth, withResponse } from "~/utils/auth-utils";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "~/components/ui/table";
import { Button } from "~/components/ui/button";
import { Switch } from "~/components/ui/switch";
import { Input } from "~/components/ui/input";
import { Dialog, DialogContent, DialogHeader, DialogTitle } from "~/components/ui/dialog";
import { Label } from "~/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "~/components/ui/select";
import { useToast } from "~/components/ui/ToastProvider";
import { Plus, Edit, Trash2, ArrowLeft } from "lucide-react";
import SpinnerLoader from "~/components/loader/SpinnerLoader";

// Type-safe loader data interface
interface LoaderData {
  notificationConfigs: NotificationConfig[];
  sellerId: number;
  sellerName?: string;
}

// Type-safe action data interface
interface ActionData {
  success: boolean;
  error?: string;
  data?: NotificationConfig;
}

// Type-safe action types
type ActionType =
  | "createNotificationConfig"
  | "updateNotificationConfig"
  | "deleteNotificationConfig";

// Predefined notification types
const NOTIFICATION_TYPES = [
  "DailySalesReport",
  "OrderPlaced",
  "OrderDispatched",
  "PaymentReceived",
  "OrderDelivered"
] as const;

type NotificationType = typeof NOTIFICATION_TYPES[number];

// Type-safe form data interface
interface NotificationFormData {
  type: NotificationType;
  waMobileNumbers: string;
  saMobileNumbers: string;
  waEnabled: boolean;
  saEnabled: boolean;
}

// Memoized table row component to prevent unnecessary re-renders
const NotificationConfigRow = memo(({
  config,
  onEdit,
  onDelete
}: {
  config: NotificationConfig;
  onEdit: (config: NotificationConfig) => void;
  onDelete: (config: NotificationConfig) => void;
}) => (
  <TableRow>
    <TableCell className="font-medium">{config.type}</TableCell>
    <TableCell>
      <Switch checked={config.waEnabled} disabled />
    </TableCell>
    <TableCell>
      <Switch checked={config.saEnabled} disabled />
    </TableCell>
    <TableCell>
      <div className="max-w-xs truncate" title={config.waMobileNumbers}>
        {config.waMobileNumbers || "-"}
      </div>
    </TableCell>
    <TableCell>
      <div className="max-w-xs truncate" title={config.saMobileNumbers}>
        {config.saMobileNumbers || "-"}
      </div>
    </TableCell>
    <TableCell>
      <div className="flex gap-2">
        <Button
          variant="outline"
          size="sm"
          onClick={() => onEdit(config)}
        >
          <Edit className="h-4 w-4" />
        </Button>
        <Button
          variant="outline"
          size="sm"
          onClick={() => onDelete(config)}
          className="text-red-600 hover:text-red-700"
        >
          <Trash2 className="h-4 w-4" />
        </Button>
      </div>
    </TableCell>
  </TableRow>
));

NotificationConfigRow.displayName = 'NotificationConfigRow';

export const loader: LoaderFunction = withAuth(async ({ request }): Promise<Response> => {
  try {
    const url = new URL(request.url);
    const sellerId = Number(url.searchParams.get("sellerId"));
    const sellerName = url.searchParams.get("sellerName");

    console.log("NotificationConfig Loader - URL params:", { sellerId, sellerName });

    if (!sellerId) {
      throw new Response("Seller ID is required", { status: 400 });
    }

    const response = await getSellerNotificationConfigs(sellerId, request);

    console.log("NotificationConfig Loader - API Response:", response);
    console.log("NotificationConfig Loader - Response data:", response.data);

    const result: LoaderData = {
      notificationConfigs: response.data || [],
      sellerId,
      sellerName: sellerName || undefined,
    };

    console.log("NotificationConfig Loader - Final result:", result);

    return withResponse(result, response.headers);
  } catch (error) {
    console.error("Error in loader:", error);
    throw new Response("Failed to get notification configs", { status: 500 });
  }
});

export const action: ActionFunction = async ({ request }): Promise<Response> => {
  const formData = await request.formData();
  const actionType = formData.get("actionType") as ActionType;

  try {
    if (actionType === "createNotificationConfig") {
      const sellerId = Number(formData.get("sellerId"));
      const notificationData = {
        sellerId,
        type: formData.get("type") as NotificationType,
        waMobileNumbers: formData.get("waMobileNumbers") as string,
        saMobileNumbers: formData.get("saMobileNumbers") as string,
        waEnabled: formData.get("waEnabled") === "true",
        saEnabled: formData.get("saEnabled") === "true",
      };

      const response = await createSellerNotificationConfig(notificationData, request);
      const actionData: ActionData = { success: true, data: response.data };
      return json(actionData);
    }

    if (actionType === "updateNotificationConfig") {
      const notificationData = {
        id: Number(formData.get("id")),
        sellerId: Number(formData.get("sellerId")),
        type: formData.get("type") as NotificationType,
        waMobileNumbers: formData.get("waMobileNumbers") as string,
        saMobileNumbers: formData.get("saMobileNumbers") as string,
        waEnabled: formData.get("waEnabled") === "true",
        saEnabled: formData.get("saEnabled") === "true",
      };

      const response = await updateSellerNotificationConfig(notificationData, request);
      const actionData: ActionData = { success: true, data: response.data };
      return json(actionData);
    }

    if (actionType === "deleteNotificationConfig") {
      const sellerId = Number(formData.get("sellerId"));
      const type = formData.get("type") as string;

      await deleteSellerNotificationConfig(sellerId, type, request);
      const actionData: ActionData = { success: true };
      return json(actionData);
    }

    const actionData: ActionData = { success: false, error: "Invalid action type" };
    return json(actionData);
  } catch (error) {
    console.error("Error in action:", error);
    const actionData: ActionData = {
      success: false,
      error: error instanceof Error ? error.message : "An unexpected error occurred"
    };
    return json(actionData, { status: 500 });
  }
};

export default function NotificationConfigPage() {
  const { showToast } = useToast();
  const { notificationConfigs, sellerId, sellerName } = useLoaderData<LoaderData>();
  const navigate = useNavigate();
  const fetcher = useFetcher<ActionData>();

  const [isAddModalOpen, setIsAddModalOpen] = useState<boolean>(false);
  const [isEditModalOpen, setIsEditModalOpen] = useState<boolean>(false);
  const [editingConfig, setEditingConfig] = useState<NotificationConfig | null>(null);
  const [searchTerm, setSearchTerm] = useState<string>('');

  // Form states for add/edit
  const [formData, setFormData] = useState<NotificationFormData>({
    type: "OrderPlaced" as NotificationType,
    waMobileNumbers: '',
    saMobileNumbers: '',
    waEnabled: false,
    saEnabled: false,
  });

  // Memoized filtered configs to prevent unnecessary recalculations
  const filteredConfigs = useMemo(() => {
    if (searchTerm.length >= 2) {
      return notificationConfigs.filter(config =>
        config.type.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }
    return notificationConfigs;
  }, [searchTerm, notificationConfigs]);

  // Handle form submission
  const handleSubmit = useCallback((isEdit: boolean = false) => {
    const formDataToSubmit = new FormData();
    const actionType: ActionType = isEdit ? "updateNotificationConfig" : "createNotificationConfig";
    formDataToSubmit.append("actionType", actionType);
    formDataToSubmit.append("sellerId", sellerId.toString());

    if (isEdit && editingConfig?.id) {
      formDataToSubmit.append("id", editingConfig.id.toString());
    }

    formDataToSubmit.append("type", formData.type);
    formDataToSubmit.append("waMobileNumbers", formData.waMobileNumbers);
    formDataToSubmit.append("saMobileNumbers", formData.saMobileNumbers);
    formDataToSubmit.append("waEnabled", formData.waEnabled.toString());
    formDataToSubmit.append("saEnabled", formData.saEnabled.toString());

    fetcher.submit(formDataToSubmit, { method: "post" });
  }, [fetcher, sellerId, editingConfig, formData]);

  // Handle edit
  const handleEdit = useCallback((config: NotificationConfig) => {
    setEditingConfig(config);
    const editFormData: NotificationFormData = {
      type: config.type as NotificationType,
      waMobileNumbers: config.waMobileNumbers,
      saMobileNumbers: config.saMobileNumbers,
      waEnabled: config.waEnabled,
      saEnabled: config.saEnabled,
    };
    setFormData(editFormData);
    setIsEditModalOpen(true);
  }, []);

  // Handle delete
  const handleDelete = useCallback((config: NotificationConfig) => {
    if (confirm(`Are you sure you want to delete the notification config for "${config.type}"?`)) {
      const formData = new FormData();
      const actionType: ActionType = "deleteNotificationConfig";
      formData.append("actionType", actionType);
      formData.append("sellerId", sellerId.toString());
      formData.append("type", config.type);
      fetcher.submit(formData, { method: "post" });
    }
  }, [fetcher, sellerId]);

  // Reset form data
  const resetFormData = useCallback(() => {
    const defaultFormData: NotificationFormData = {
      type: "OrderPlaced" as NotificationType,
      waMobileNumbers: '',
      saMobileNumbers: '',
      waEnabled: false,
      saEnabled: false,
    };
    setFormData(defaultFormData);
  }, []);

  // Handle fetcher responses - optimized to prevent unnecessary re-renders
  useEffect(() => {
    if (fetcher.state === "idle" && fetcher.data) {
      const actionData = fetcher.data as ActionData;
      if (actionData.success) {
        showToast("Operation completed successfully", "success");
        setIsAddModalOpen(false);
        setIsEditModalOpen(false);
        setEditingConfig(null);
        resetFormData();
        // Let Remix handle revalidation automatically
      } else {
        showToast(actionData.error || "Operation failed", "error");
      }
    }
  }, [fetcher.state, fetcher.data]);

  const loading = fetcher.state !== "idle";

  const handleBack = useCallback(() => {
    navigate(-1);
  }, [navigate]);

  return (
    <div className="container mx-auto p-6">
      {loading && <SpinnerLoader loading={loading} />}

      <div className="flex justify-between items-center mb-6">
        <div className="flex items-center gap-4">
          <Button
            variant="outline"
            size="sm"
            onClick={handleBack}
            className="flex items-center gap-2"
          >
            <ArrowLeft className="h-4 w-4" />
            Back
          </Button>
          <div>
            <h1 className="text-2xl font-bold">Notification Configuration</h1>
            {sellerName && (
              <p className="text-sm text-gray-600">Seller: {sellerName}</p>
            )}
          </div>
        </div>
      </div>

      <div className="flex flex-col my-3 relative">
        <Input
          type="search"
          placeholder="Search by notification type"
          value={searchTerm}
          onChange={(e) => setSearchTerm(e.target.value)}
          className="max-w-sm rounded-full"
        />
      </div>

      <div className="flex justify-between items-center mb-4">
        <p className="text-sm text-gray-600">
          Total Configurations: {filteredConfigs.length} (Raw: {notificationConfigs.length})
        </p>
        <Button onClick={() => setIsAddModalOpen(true)}>
          <Plus className="h-4 w-4 mr-2" />
          Add Notification Config
        </Button>
      </div>

      <Table>
        <TableHeader className="bg-gray-100">
          <TableRow>
            <TableHead>Type</TableHead>
            <TableHead>WhatsApp Enabled</TableHead>
            <TableHead>Seller Enabled</TableHead>
            <TableHead>WhatsApp Numbers</TableHead>
            <TableHead>Seller Numbers</TableHead>
            <TableHead>Actions</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {filteredConfigs.length > 0 ? (
            filteredConfigs.map((config) => (
              <NotificationConfigRow
                key={config.id}
                config={config}
                onEdit={handleEdit}
                onDelete={handleDelete}
              />
            ))
          ) : (
            <TableRow>
              <TableCell colSpan={6} className="h-24 text-center">
                {notificationConfigs.length === 0 ? (
                  <div className="text-center">
                    <p className="text-gray-500 mb-2">No notification configurations found.</p>
                    <p className="text-sm text-gray-400">Click "Add Notification Config" to create your first configuration.</p>
                  </div>
                ) : (
                  "No configurations match your search."
                )}
              </TableCell>
            </TableRow>
          )}
        </TableBody>
      </Table>

      {/* Add Modal */}
      <Dialog open={isAddModalOpen} onOpenChange={setIsAddModalOpen}>
        <DialogContent className="sm:max-w-[425px]">
          <DialogHeader>
            <DialogTitle>Add Notification Configuration</DialogTitle>
          </DialogHeader>
          <div className="grid gap-4 py-4">
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="type" className="text-right">
                Type
              </Label>
              <Select
                value={formData.type}
                onValueChange={(value) => setFormData({ ...formData, type: value as NotificationType })}
              >
                <SelectTrigger className="col-span-3">
                  <SelectValue placeholder="Select notification type" />
                </SelectTrigger>
                <SelectContent>
                  {NOTIFICATION_TYPES.map((type) => (
                    <SelectItem key={type} value={type}>
                      {type}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="waMobileNumbers" className="text-right">
                WhatsApp Numbers
              </Label>
              <Input
                id="waMobileNumbers"
                value={formData.waMobileNumbers}
                onChange={(e) => setFormData({ ...formData, waMobileNumbers: e.target.value })}
                className="col-span-3"
                placeholder="9876543210,9876543211"
              />
            </div>
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="saMobileNumbers" className="text-right">
                Seller Numbers
              </Label>
              <Input
                id="saMobileNumbers"
                value={formData.saMobileNumbers}
                onChange={(e) => setFormData({ ...formData, saMobileNumbers: e.target.value })}
                className="col-span-3"
                placeholder="9876543212,9876543213"
              />
            </div>
            <div className="grid grid-cols-4 items-center gap-4">
              <Label className="text-right">WhatsApp Enabled</Label>
              <div className="col-span-3">
                <Switch
                  checked={formData.waEnabled}
                  onCheckedChange={(checked) => setFormData({ ...formData, waEnabled: checked })}
                />
              </div>
            </div>
            <div className="grid grid-cols-4 items-center gap-4">
              <Label className="text-right">Seller Enabled</Label>
              <div className="col-span-3">
                <Switch
                  checked={formData.saEnabled}
                  onCheckedChange={(checked) => setFormData({ ...formData, saEnabled: checked })}
                />
              </div>
            </div>
          </div>
          <div className="flex justify-end gap-2">
            <Button variant="outline" onClick={() => setIsAddModalOpen(false)}>
              Cancel
            </Button>
            <Button
              onClick={() => handleSubmit(false)}
              disabled={loading || !formData.type}
            >
              Add Configuration
            </Button>
          </div>
        </DialogContent>
      </Dialog>

      {/* Edit Modal */}
      <Dialog open={isEditModalOpen} onOpenChange={setIsEditModalOpen}>
        <DialogContent className="sm:max-w-[425px]">
          <DialogHeader>
            <DialogTitle>Edit Notification Configuration</DialogTitle>
          </DialogHeader>
          <div className="grid gap-4 py-4">
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="edit-type" className="text-right">
                Type
              </Label>
              <Input
                id="edit-type"
                value={formData.type}
                className="col-span-3"
                readOnly
                disabled
              />
            </div>
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="edit-waMobileNumbers" className="text-right">
                WhatsApp Numbers
              </Label>
              <Input
                id="edit-waMobileNumbers"
                value={formData.waMobileNumbers}
                onChange={(e) => setFormData({ ...formData, waMobileNumbers: e.target.value })}
                className="col-span-3"
                placeholder="9876543210,9876543211"
              />
            </div>
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="edit-saMobileNumbers" className="text-right">
                Seller Numbers
              </Label>
              <Input
                id="edit-saMobileNumbers"
                value={formData.saMobileNumbers}
                onChange={(e) => setFormData({ ...formData, saMobileNumbers: e.target.value })}
                className="col-span-3"
                placeholder="9876543212,9876543213"
              />
            </div>
            <div className="grid grid-cols-4 items-center gap-4">
              <Label className="text-right">WhatsApp Enabled</Label>
              <div className="col-span-3">
                <Switch
                  checked={formData.waEnabled}
                  onCheckedChange={(checked) => setFormData({ ...formData, waEnabled: checked })}
                />
              </div>
            </div>
            <div className="grid grid-cols-4 items-center gap-4">
              <Label className="text-right">Seller Enabled</Label>
              <div className="col-span-3">
                <Switch
                  checked={formData.saEnabled}
                  onCheckedChange={(checked) => setFormData({ ...formData, saEnabled: checked })}
                />
              </div>
            </div>
          </div>
          <div className="flex justify-end gap-2">
            <Button variant="outline" onClick={() => setIsEditModalOpen(false)}>
              Cancel
            </Button>
            <Button
              onClick={() => handleSubmit(true)}
              disabled={loading || !formData.type}
            >
              Update Configuration
            </Button>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
}
