{"version": 3, "sources": ["../../events/events.js", "../../readable-stream/lib/internal/streams/stream-browser.js", "browser-external:util", "../../readable-stream/lib/internal/streams/buffer_list.js", "../../readable-stream/lib/internal/streams/destroy.js", "../../readable-stream/errors-browser.js", "../../readable-stream/lib/internal/streams/state.js", "../../util-deprecate/browser.js", "../../readable-stream/lib/_stream_writable.js", "../../readable-stream/lib/_stream_duplex.js", "../../readable-stream/lib/internal/streams/end-of-stream.js", "../../readable-stream/lib/internal/streams/async_iterator.js", "../../readable-stream/lib/internal/streams/from-browser.js", "../../readable-stream/lib/_stream_readable.js", "../../readable-stream/lib/_stream_transform.js", "../../readable-stream/lib/_stream_passthrough.js", "../../readable-stream/lib/internal/streams/pipeline.js", "../../stream-browserify/index.js", "../../@smithy/abort-controller/dist-es/AbortSignal.js", "../../@smithy/abort-controller/dist-es/AbortController.js", "../../@aws-sdk/lib-storage/dist-es/Upload.js", "../../@aws-sdk/lib-storage/dist-es/bytelength.js", "../../@aws-sdk/lib-storage/dist-es/runtimeConfig.shared.js", "../../@aws-sdk/lib-storage/dist-es/runtimeConfig.browser.js", "../../@aws-sdk/lib-storage/dist-es/chunker.js", "../../@aws-sdk/lib-storage/dist-es/chunks/getChunkStream.js", "../../@aws-sdk/lib-storage/dist-es/chunks/getChunkUint8Array.js", "../../@aws-sdk/lib-storage/dist-es/chunks/getDataReadable.js", "../../@aws-sdk/lib-storage/dist-es/chunks/getDataReadableStream.js"], "sourcesContent": ["// Copyright Joyent, Inc. and other Node contributors.\n//\n// Permission is hereby granted, free of charge, to any person obtaining a\n// copy of this software and associated documentation files (the\n// \"Software\"), to deal in the Software without restriction, including\n// without limitation the rights to use, copy, modify, merge, publish,\n// distribute, sublicense, and/or sell copies of the Software, and to permit\n// persons to whom the Software is furnished to do so, subject to the\n// following conditions:\n//\n// The above copyright notice and this permission notice shall be included\n// in all copies or substantial portions of the Software.\n//\n// THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS\n// OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\n// MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN\n// NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM,\n// DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR\n// OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE\n// USE OR OTHER DEALINGS IN THE SOFTWARE.\n\n'use strict';\n\nvar R = typeof Reflect === 'object' ? Reflect : null\nvar ReflectApply = R && typeof R.apply === 'function'\n  ? R.apply\n  : function ReflectApply(target, receiver, args) {\n    return Function.prototype.apply.call(target, receiver, args);\n  }\n\nvar ReflectOwnKeys\nif (R && typeof R.ownKeys === 'function') {\n  ReflectOwnKeys = R.ownKeys\n} else if (Object.getOwnPropertySymbols) {\n  ReflectOwnKeys = function ReflectOwnKeys(target) {\n    return Object.getOwnPropertyNames(target)\n      .concat(Object.getOwnPropertySymbols(target));\n  };\n} else {\n  ReflectOwnKeys = function ReflectOwnKeys(target) {\n    return Object.getOwnPropertyNames(target);\n  };\n}\n\nfunction ProcessEmitWarning(warning) {\n  if (console && console.warn) console.warn(warning);\n}\n\nvar NumberIsNaN = Number.isNaN || function NumberIsNaN(value) {\n  return value !== value;\n}\n\nfunction EventEmitter() {\n  EventEmitter.init.call(this);\n}\nmodule.exports = EventEmitter;\nmodule.exports.once = once;\n\n// Backwards-compat with node 0.10.x\nEventEmitter.EventEmitter = EventEmitter;\n\nEventEmitter.prototype._events = undefined;\nEventEmitter.prototype._eventsCount = 0;\nEventEmitter.prototype._maxListeners = undefined;\n\n// By default EventEmitters will print a warning if more than 10 listeners are\n// added to it. This is a useful default which helps finding memory leaks.\nvar defaultMaxListeners = 10;\n\nfunction checkListener(listener) {\n  if (typeof listener !== 'function') {\n    throw new TypeError('The \"listener\" argument must be of type Function. Received type ' + typeof listener);\n  }\n}\n\nObject.defineProperty(EventEmitter, 'defaultMaxListeners', {\n  enumerable: true,\n  get: function() {\n    return defaultMaxListeners;\n  },\n  set: function(arg) {\n    if (typeof arg !== 'number' || arg < 0 || NumberIsNaN(arg)) {\n      throw new RangeError('The value of \"defaultMaxListeners\" is out of range. It must be a non-negative number. Received ' + arg + '.');\n    }\n    defaultMaxListeners = arg;\n  }\n});\n\nEventEmitter.init = function() {\n\n  if (this._events === undefined ||\n      this._events === Object.getPrototypeOf(this)._events) {\n    this._events = Object.create(null);\n    this._eventsCount = 0;\n  }\n\n  this._maxListeners = this._maxListeners || undefined;\n};\n\n// Obviously not all Emitters should be limited to 10. This function allows\n// that to be increased. Set to zero for unlimited.\nEventEmitter.prototype.setMaxListeners = function setMaxListeners(n) {\n  if (typeof n !== 'number' || n < 0 || NumberIsNaN(n)) {\n    throw new RangeError('The value of \"n\" is out of range. It must be a non-negative number. Received ' + n + '.');\n  }\n  this._maxListeners = n;\n  return this;\n};\n\nfunction _getMaxListeners(that) {\n  if (that._maxListeners === undefined)\n    return EventEmitter.defaultMaxListeners;\n  return that._maxListeners;\n}\n\nEventEmitter.prototype.getMaxListeners = function getMaxListeners() {\n  return _getMaxListeners(this);\n};\n\nEventEmitter.prototype.emit = function emit(type) {\n  var args = [];\n  for (var i = 1; i < arguments.length; i++) args.push(arguments[i]);\n  var doError = (type === 'error');\n\n  var events = this._events;\n  if (events !== undefined)\n    doError = (doError && events.error === undefined);\n  else if (!doError)\n    return false;\n\n  // If there is no 'error' event listener then throw.\n  if (doError) {\n    var er;\n    if (args.length > 0)\n      er = args[0];\n    if (er instanceof Error) {\n      // Note: The comments on the `throw` lines are intentional, they show\n      // up in Node's output if this results in an unhandled exception.\n      throw er; // Unhandled 'error' event\n    }\n    // At least give some kind of context to the user\n    var err = new Error('Unhandled error.' + (er ? ' (' + er.message + ')' : ''));\n    err.context = er;\n    throw err; // Unhandled 'error' event\n  }\n\n  var handler = events[type];\n\n  if (handler === undefined)\n    return false;\n\n  if (typeof handler === 'function') {\n    ReflectApply(handler, this, args);\n  } else {\n    var len = handler.length;\n    var listeners = arrayClone(handler, len);\n    for (var i = 0; i < len; ++i)\n      ReflectApply(listeners[i], this, args);\n  }\n\n  return true;\n};\n\nfunction _addListener(target, type, listener, prepend) {\n  var m;\n  var events;\n  var existing;\n\n  checkListener(listener);\n\n  events = target._events;\n  if (events === undefined) {\n    events = target._events = Object.create(null);\n    target._eventsCount = 0;\n  } else {\n    // To avoid recursion in the case that type === \"newListener\"! Before\n    // adding it to the listeners, first emit \"newListener\".\n    if (events.newListener !== undefined) {\n      target.emit('newListener', type,\n                  listener.listener ? listener.listener : listener);\n\n      // Re-assign `events` because a newListener handler could have caused the\n      // this._events to be assigned to a new object\n      events = target._events;\n    }\n    existing = events[type];\n  }\n\n  if (existing === undefined) {\n    // Optimize the case of one listener. Don't need the extra array object.\n    existing = events[type] = listener;\n    ++target._eventsCount;\n  } else {\n    if (typeof existing === 'function') {\n      // Adding the second element, need to change to array.\n      existing = events[type] =\n        prepend ? [listener, existing] : [existing, listener];\n      // If we've already got an array, just append.\n    } else if (prepend) {\n      existing.unshift(listener);\n    } else {\n      existing.push(listener);\n    }\n\n    // Check for listener leak\n    m = _getMaxListeners(target);\n    if (m > 0 && existing.length > m && !existing.warned) {\n      existing.warned = true;\n      // No error code for this since it is a Warning\n      // eslint-disable-next-line no-restricted-syntax\n      var w = new Error('Possible EventEmitter memory leak detected. ' +\n                          existing.length + ' ' + String(type) + ' listeners ' +\n                          'added. Use emitter.setMaxListeners() to ' +\n                          'increase limit');\n      w.name = 'MaxListenersExceededWarning';\n      w.emitter = target;\n      w.type = type;\n      w.count = existing.length;\n      ProcessEmitWarning(w);\n    }\n  }\n\n  return target;\n}\n\nEventEmitter.prototype.addListener = function addListener(type, listener) {\n  return _addListener(this, type, listener, false);\n};\n\nEventEmitter.prototype.on = EventEmitter.prototype.addListener;\n\nEventEmitter.prototype.prependListener =\n    function prependListener(type, listener) {\n      return _addListener(this, type, listener, true);\n    };\n\nfunction onceWrapper() {\n  if (!this.fired) {\n    this.target.removeListener(this.type, this.wrapFn);\n    this.fired = true;\n    if (arguments.length === 0)\n      return this.listener.call(this.target);\n    return this.listener.apply(this.target, arguments);\n  }\n}\n\nfunction _onceWrap(target, type, listener) {\n  var state = { fired: false, wrapFn: undefined, target: target, type: type, listener: listener };\n  var wrapped = onceWrapper.bind(state);\n  wrapped.listener = listener;\n  state.wrapFn = wrapped;\n  return wrapped;\n}\n\nEventEmitter.prototype.once = function once(type, listener) {\n  checkListener(listener);\n  this.on(type, _onceWrap(this, type, listener));\n  return this;\n};\n\nEventEmitter.prototype.prependOnceListener =\n    function prependOnceListener(type, listener) {\n      checkListener(listener);\n      this.prependListener(type, _onceWrap(this, type, listener));\n      return this;\n    };\n\n// Emits a 'removeListener' event if and only if the listener was removed.\nEventEmitter.prototype.removeListener =\n    function removeListener(type, listener) {\n      var list, events, position, i, originalListener;\n\n      checkListener(listener);\n\n      events = this._events;\n      if (events === undefined)\n        return this;\n\n      list = events[type];\n      if (list === undefined)\n        return this;\n\n      if (list === listener || list.listener === listener) {\n        if (--this._eventsCount === 0)\n          this._events = Object.create(null);\n        else {\n          delete events[type];\n          if (events.removeListener)\n            this.emit('removeListener', type, list.listener || listener);\n        }\n      } else if (typeof list !== 'function') {\n        position = -1;\n\n        for (i = list.length - 1; i >= 0; i--) {\n          if (list[i] === listener || list[i].listener === listener) {\n            originalListener = list[i].listener;\n            position = i;\n            break;\n          }\n        }\n\n        if (position < 0)\n          return this;\n\n        if (position === 0)\n          list.shift();\n        else {\n          spliceOne(list, position);\n        }\n\n        if (list.length === 1)\n          events[type] = list[0];\n\n        if (events.removeListener !== undefined)\n          this.emit('removeListener', type, originalListener || listener);\n      }\n\n      return this;\n    };\n\nEventEmitter.prototype.off = EventEmitter.prototype.removeListener;\n\nEventEmitter.prototype.removeAllListeners =\n    function removeAllListeners(type) {\n      var listeners, events, i;\n\n      events = this._events;\n      if (events === undefined)\n        return this;\n\n      // not listening for removeListener, no need to emit\n      if (events.removeListener === undefined) {\n        if (arguments.length === 0) {\n          this._events = Object.create(null);\n          this._eventsCount = 0;\n        } else if (events[type] !== undefined) {\n          if (--this._eventsCount === 0)\n            this._events = Object.create(null);\n          else\n            delete events[type];\n        }\n        return this;\n      }\n\n      // emit removeListener for all listeners on all events\n      if (arguments.length === 0) {\n        var keys = Object.keys(events);\n        var key;\n        for (i = 0; i < keys.length; ++i) {\n          key = keys[i];\n          if (key === 'removeListener') continue;\n          this.removeAllListeners(key);\n        }\n        this.removeAllListeners('removeListener');\n        this._events = Object.create(null);\n        this._eventsCount = 0;\n        return this;\n      }\n\n      listeners = events[type];\n\n      if (typeof listeners === 'function') {\n        this.removeListener(type, listeners);\n      } else if (listeners !== undefined) {\n        // LIFO order\n        for (i = listeners.length - 1; i >= 0; i--) {\n          this.removeListener(type, listeners[i]);\n        }\n      }\n\n      return this;\n    };\n\nfunction _listeners(target, type, unwrap) {\n  var events = target._events;\n\n  if (events === undefined)\n    return [];\n\n  var evlistener = events[type];\n  if (evlistener === undefined)\n    return [];\n\n  if (typeof evlistener === 'function')\n    return unwrap ? [evlistener.listener || evlistener] : [evlistener];\n\n  return unwrap ?\n    unwrapListeners(evlistener) : arrayClone(evlistener, evlistener.length);\n}\n\nEventEmitter.prototype.listeners = function listeners(type) {\n  return _listeners(this, type, true);\n};\n\nEventEmitter.prototype.rawListeners = function rawListeners(type) {\n  return _listeners(this, type, false);\n};\n\nEventEmitter.listenerCount = function(emitter, type) {\n  if (typeof emitter.listenerCount === 'function') {\n    return emitter.listenerCount(type);\n  } else {\n    return listenerCount.call(emitter, type);\n  }\n};\n\nEventEmitter.prototype.listenerCount = listenerCount;\nfunction listenerCount(type) {\n  var events = this._events;\n\n  if (events !== undefined) {\n    var evlistener = events[type];\n\n    if (typeof evlistener === 'function') {\n      return 1;\n    } else if (evlistener !== undefined) {\n      return evlistener.length;\n    }\n  }\n\n  return 0;\n}\n\nEventEmitter.prototype.eventNames = function eventNames() {\n  return this._eventsCount > 0 ? ReflectOwnKeys(this._events) : [];\n};\n\nfunction arrayClone(arr, n) {\n  var copy = new Array(n);\n  for (var i = 0; i < n; ++i)\n    copy[i] = arr[i];\n  return copy;\n}\n\nfunction spliceOne(list, index) {\n  for (; index + 1 < list.length; index++)\n    list[index] = list[index + 1];\n  list.pop();\n}\n\nfunction unwrapListeners(arr) {\n  var ret = new Array(arr.length);\n  for (var i = 0; i < ret.length; ++i) {\n    ret[i] = arr[i].listener || arr[i];\n  }\n  return ret;\n}\n\nfunction once(emitter, name) {\n  return new Promise(function (resolve, reject) {\n    function errorListener(err) {\n      emitter.removeListener(name, resolver);\n      reject(err);\n    }\n\n    function resolver() {\n      if (typeof emitter.removeListener === 'function') {\n        emitter.removeListener('error', errorListener);\n      }\n      resolve([].slice.call(arguments));\n    };\n\n    eventTargetAgnosticAddListener(emitter, name, resolver, { once: true });\n    if (name !== 'error') {\n      addErrorHandlerIfEventEmitter(emitter, errorListener, { once: true });\n    }\n  });\n}\n\nfunction addErrorHandlerIfEventEmitter(emitter, handler, flags) {\n  if (typeof emitter.on === 'function') {\n    eventTargetAgnosticAddListener(emitter, 'error', handler, flags);\n  }\n}\n\nfunction eventTargetAgnosticAddListener(emitter, name, listener, flags) {\n  if (typeof emitter.on === 'function') {\n    if (flags.once) {\n      emitter.once(name, listener);\n    } else {\n      emitter.on(name, listener);\n    }\n  } else if (typeof emitter.addEventListener === 'function') {\n    // EventTarget does not have `error` event semantics like Node\n    // EventEmitters, we do not listen for `error` events here.\n    emitter.addEventListener(name, function wrapListener(arg) {\n      // IE does not have builtin `{ once: true }` support so we\n      // have to do it manually.\n      if (flags.once) {\n        emitter.removeEventListener(name, wrapListener);\n      }\n      listener(arg);\n    });\n  } else {\n    throw new TypeError('The \"emitter\" argument must be of type EventEmitter. Received type ' + typeof emitter);\n  }\n}\n", "module.exports = require('events').EventEmitter;\n", "module.exports = Object.create(new Proxy({}, {\n  get(_, key) {\n    if (\n      key !== '__esModule' &&\n      key !== '__proto__' &&\n      key !== 'constructor' &&\n      key !== 'splice'\n    ) {\n      console.warn(`Module \"util\" has been externalized for browser compatibility. Cannot access \"util.${key}\" in client code. See https://vite.dev/guide/troubleshooting.html#module-externalized-for-browser-compatibility for more details.`)\n    }\n  }\n}))", "'use strict';\n\nfunction ownKeys(object, enumerableOnly) { var keys = Object.keys(object); if (Object.getOwnPropertySymbols) { var symbols = Object.getOwnPropertySymbols(object); enumerableOnly && (symbols = symbols.filter(function (sym) { return Object.getOwnPropertyDescriptor(object, sym).enumerable; })), keys.push.apply(keys, symbols); } return keys; }\nfunction _objectSpread(target) { for (var i = 1; i < arguments.length; i++) { var source = null != arguments[i] ? arguments[i] : {}; i % 2 ? ownKeys(Object(source), !0).forEach(function (key) { _defineProperty(target, key, source[key]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(target, Object.getOwnPropertyDescriptors(source)) : ownKeys(Object(source)).forEach(function (key) { Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key)); }); } return target; }\nfunction _defineProperty(obj, key, value) { key = _toPropertyKey(key); if (key in obj) { Object.defineProperty(obj, key, { value: value, enumerable: true, configurable: true, writable: true }); } else { obj[key] = value; } return obj; }\nfunction _classCallCheck(instance, Constructor) { if (!(instance instanceof Constructor)) { throw new TypeError(\"Cannot call a class as a function\"); } }\nfunction _defineProperties(target, props) { for (var i = 0; i < props.length; i++) { var descriptor = props[i]; descriptor.enumerable = descriptor.enumerable || false; descriptor.configurable = true; if (\"value\" in descriptor) descriptor.writable = true; Object.defineProperty(target, _toPropertyKey(descriptor.key), descriptor); } }\nfunction _createClass(Constructor, protoProps, staticProps) { if (protoProps) _defineProperties(Constructor.prototype, protoProps); if (staticProps) _defineProperties(Constructor, staticProps); Object.defineProperty(Constructor, \"prototype\", { writable: false }); return Constructor; }\nfunction _toPropertyKey(arg) { var key = _toPrimitive(arg, \"string\"); return typeof key === \"symbol\" ? key : String(key); }\nfunction _toPrimitive(input, hint) { if (typeof input !== \"object\" || input === null) return input; var prim = input[Symbol.toPrimitive]; if (prim !== undefined) { var res = prim.call(input, hint || \"default\"); if (typeof res !== \"object\") return res; throw new TypeError(\"@@toPrimitive must return a primitive value.\"); } return (hint === \"string\" ? String : Number)(input); }\nvar _require = require('buffer'),\n  Buffer = _require.Buffer;\nvar _require2 = require('util'),\n  inspect = _require2.inspect;\nvar custom = inspect && inspect.custom || 'inspect';\nfunction copyBuffer(src, target, offset) {\n  Buffer.prototype.copy.call(src, target, offset);\n}\nmodule.exports = /*#__PURE__*/function () {\n  function BufferList() {\n    _classCallCheck(this, BufferList);\n    this.head = null;\n    this.tail = null;\n    this.length = 0;\n  }\n  _createClass(BufferList, [{\n    key: \"push\",\n    value: function push(v) {\n      var entry = {\n        data: v,\n        next: null\n      };\n      if (this.length > 0) this.tail.next = entry;else this.head = entry;\n      this.tail = entry;\n      ++this.length;\n    }\n  }, {\n    key: \"unshift\",\n    value: function unshift(v) {\n      var entry = {\n        data: v,\n        next: this.head\n      };\n      if (this.length === 0) this.tail = entry;\n      this.head = entry;\n      ++this.length;\n    }\n  }, {\n    key: \"shift\",\n    value: function shift() {\n      if (this.length === 0) return;\n      var ret = this.head.data;\n      if (this.length === 1) this.head = this.tail = null;else this.head = this.head.next;\n      --this.length;\n      return ret;\n    }\n  }, {\n    key: \"clear\",\n    value: function clear() {\n      this.head = this.tail = null;\n      this.length = 0;\n    }\n  }, {\n    key: \"join\",\n    value: function join(s) {\n      if (this.length === 0) return '';\n      var p = this.head;\n      var ret = '' + p.data;\n      while (p = p.next) ret += s + p.data;\n      return ret;\n    }\n  }, {\n    key: \"concat\",\n    value: function concat(n) {\n      if (this.length === 0) return Buffer.alloc(0);\n      var ret = Buffer.allocUnsafe(n >>> 0);\n      var p = this.head;\n      var i = 0;\n      while (p) {\n        copyBuffer(p.data, ret, i);\n        i += p.data.length;\n        p = p.next;\n      }\n      return ret;\n    }\n\n    // Consumes a specified amount of bytes or characters from the buffered data.\n  }, {\n    key: \"consume\",\n    value: function consume(n, hasStrings) {\n      var ret;\n      if (n < this.head.data.length) {\n        // `slice` is the same for buffers and strings.\n        ret = this.head.data.slice(0, n);\n        this.head.data = this.head.data.slice(n);\n      } else if (n === this.head.data.length) {\n        // First chunk is a perfect match.\n        ret = this.shift();\n      } else {\n        // Result spans more than one buffer.\n        ret = hasStrings ? this._getString(n) : this._getBuffer(n);\n      }\n      return ret;\n    }\n  }, {\n    key: \"first\",\n    value: function first() {\n      return this.head.data;\n    }\n\n    // Consumes a specified amount of characters from the buffered data.\n  }, {\n    key: \"_getString\",\n    value: function _getString(n) {\n      var p = this.head;\n      var c = 1;\n      var ret = p.data;\n      n -= ret.length;\n      while (p = p.next) {\n        var str = p.data;\n        var nb = n > str.length ? str.length : n;\n        if (nb === str.length) ret += str;else ret += str.slice(0, n);\n        n -= nb;\n        if (n === 0) {\n          if (nb === str.length) {\n            ++c;\n            if (p.next) this.head = p.next;else this.head = this.tail = null;\n          } else {\n            this.head = p;\n            p.data = str.slice(nb);\n          }\n          break;\n        }\n        ++c;\n      }\n      this.length -= c;\n      return ret;\n    }\n\n    // Consumes a specified amount of bytes from the buffered data.\n  }, {\n    key: \"_getBuffer\",\n    value: function _getBuffer(n) {\n      var ret = Buffer.allocUnsafe(n);\n      var p = this.head;\n      var c = 1;\n      p.data.copy(ret);\n      n -= p.data.length;\n      while (p = p.next) {\n        var buf = p.data;\n        var nb = n > buf.length ? buf.length : n;\n        buf.copy(ret, ret.length - n, 0, nb);\n        n -= nb;\n        if (n === 0) {\n          if (nb === buf.length) {\n            ++c;\n            if (p.next) this.head = p.next;else this.head = this.tail = null;\n          } else {\n            this.head = p;\n            p.data = buf.slice(nb);\n          }\n          break;\n        }\n        ++c;\n      }\n      this.length -= c;\n      return ret;\n    }\n\n    // Make sure the linked list only shows the minimal necessary information.\n  }, {\n    key: custom,\n    value: function value(_, options) {\n      return inspect(this, _objectSpread(_objectSpread({}, options), {}, {\n        // Only inspect one level.\n        depth: 0,\n        // It should not recurse.\n        customInspect: false\n      }));\n    }\n  }]);\n  return BufferList;\n}();", "'use strict';\n\n// undocumented cb() API, needed for core, not for public API\nfunction destroy(err, cb) {\n  var _this = this;\n  var readableDestroyed = this._readableState && this._readableState.destroyed;\n  var writableDestroyed = this._writableState && this._writableState.destroyed;\n  if (readableDestroyed || writableDestroyed) {\n    if (cb) {\n      cb(err);\n    } else if (err) {\n      if (!this._writableState) {\n        process.nextTick(emitErrorNT, this, err);\n      } else if (!this._writableState.errorEmitted) {\n        this._writableState.errorEmitted = true;\n        process.nextTick(emitErrorNT, this, err);\n      }\n    }\n    return this;\n  }\n\n  // we set destroyed to true before firing error callbacks in order\n  // to make it re-entrance safe in case destroy() is called within callbacks\n\n  if (this._readableState) {\n    this._readableState.destroyed = true;\n  }\n\n  // if this is a duplex stream mark the writable part as destroyed as well\n  if (this._writableState) {\n    this._writableState.destroyed = true;\n  }\n  this._destroy(err || null, function (err) {\n    if (!cb && err) {\n      if (!_this._writableState) {\n        process.nextTick(emitErrorAndCloseNT, _this, err);\n      } else if (!_this._writableState.errorEmitted) {\n        _this._writableState.errorEmitted = true;\n        process.nextTick(emitErrorAndCloseNT, _this, err);\n      } else {\n        process.nextTick(emitCloseNT, _this);\n      }\n    } else if (cb) {\n      process.nextTick(emitCloseNT, _this);\n      cb(err);\n    } else {\n      process.nextTick(emitCloseNT, _this);\n    }\n  });\n  return this;\n}\nfunction emitErrorAndCloseNT(self, err) {\n  emitErrorNT(self, err);\n  emitCloseNT(self);\n}\nfunction emitCloseNT(self) {\n  if (self._writableState && !self._writableState.emitClose) return;\n  if (self._readableState && !self._readableState.emitClose) return;\n  self.emit('close');\n}\nfunction undestroy() {\n  if (this._readableState) {\n    this._readableState.destroyed = false;\n    this._readableState.reading = false;\n    this._readableState.ended = false;\n    this._readableState.endEmitted = false;\n  }\n  if (this._writableState) {\n    this._writableState.destroyed = false;\n    this._writableState.ended = false;\n    this._writableState.ending = false;\n    this._writableState.finalCalled = false;\n    this._writableState.prefinished = false;\n    this._writableState.finished = false;\n    this._writableState.errorEmitted = false;\n  }\n}\nfunction emitErrorNT(self, err) {\n  self.emit('error', err);\n}\nfunction errorOrDestroy(stream, err) {\n  // We have tests that rely on errors being emitted\n  // in the same tick, so changing this is semver major.\n  // For now when you opt-in to autoDestroy we allow\n  // the error to be emitted nextTick. In a future\n  // semver major update we should change the default to this.\n\n  var rState = stream._readableState;\n  var wState = stream._writableState;\n  if (rState && rState.autoDestroy || wState && wState.autoDestroy) stream.destroy(err);else stream.emit('error', err);\n}\nmodule.exports = {\n  destroy: destroy,\n  undestroy: undestroy,\n  errorOrDestroy: errorOrDestroy\n};", "'use strict';\n\nfunction _inheritsLoose(subClass, superClass) { subClass.prototype = Object.create(superClass.prototype); subClass.prototype.constructor = subClass; subClass.__proto__ = superClass; }\n\nvar codes = {};\n\nfunction createErrorType(code, message, Base) {\n  if (!Base) {\n    Base = Error;\n  }\n\n  function getMessage(arg1, arg2, arg3) {\n    if (typeof message === 'string') {\n      return message;\n    } else {\n      return message(arg1, arg2, arg3);\n    }\n  }\n\n  var NodeError =\n  /*#__PURE__*/\n  function (_Base) {\n    _inheritsLoose(NodeError, _Base);\n\n    function NodeError(arg1, arg2, arg3) {\n      return _Base.call(this, getMessage(arg1, arg2, arg3)) || this;\n    }\n\n    return NodeError;\n  }(Base);\n\n  NodeError.prototype.name = Base.name;\n  NodeError.prototype.code = code;\n  codes[code] = NodeError;\n} // https://github.com/nodejs/node/blob/v10.8.0/lib/internal/errors.js\n\n\nfunction oneOf(expected, thing) {\n  if (Array.isArray(expected)) {\n    var len = expected.length;\n    expected = expected.map(function (i) {\n      return String(i);\n    });\n\n    if (len > 2) {\n      return \"one of \".concat(thing, \" \").concat(expected.slice(0, len - 1).join(', '), \", or \") + expected[len - 1];\n    } else if (len === 2) {\n      return \"one of \".concat(thing, \" \").concat(expected[0], \" or \").concat(expected[1]);\n    } else {\n      return \"of \".concat(thing, \" \").concat(expected[0]);\n    }\n  } else {\n    return \"of \".concat(thing, \" \").concat(String(expected));\n  }\n} // https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String/startsWith\n\n\nfunction startsWith(str, search, pos) {\n  return str.substr(!pos || pos < 0 ? 0 : +pos, search.length) === search;\n} // https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String/endsWith\n\n\nfunction endsWith(str, search, this_len) {\n  if (this_len === undefined || this_len > str.length) {\n    this_len = str.length;\n  }\n\n  return str.substring(this_len - search.length, this_len) === search;\n} // https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String/includes\n\n\nfunction includes(str, search, start) {\n  if (typeof start !== 'number') {\n    start = 0;\n  }\n\n  if (start + search.length > str.length) {\n    return false;\n  } else {\n    return str.indexOf(search, start) !== -1;\n  }\n}\n\ncreateErrorType('ERR_INVALID_OPT_VALUE', function (name, value) {\n  return 'The value \"' + value + '\" is invalid for option \"' + name + '\"';\n}, TypeError);\ncreateErrorType('ERR_INVALID_ARG_TYPE', function (name, expected, actual) {\n  // determiner: 'must be' or 'must not be'\n  var determiner;\n\n  if (typeof expected === 'string' && startsWith(expected, 'not ')) {\n    determiner = 'must not be';\n    expected = expected.replace(/^not /, '');\n  } else {\n    determiner = 'must be';\n  }\n\n  var msg;\n\n  if (endsWith(name, ' argument')) {\n    // For cases like 'first argument'\n    msg = \"The \".concat(name, \" \").concat(determiner, \" \").concat(oneOf(expected, 'type'));\n  } else {\n    var type = includes(name, '.') ? 'property' : 'argument';\n    msg = \"The \\\"\".concat(name, \"\\\" \").concat(type, \" \").concat(determiner, \" \").concat(oneOf(expected, 'type'));\n  }\n\n  msg += \". Received type \".concat(typeof actual);\n  return msg;\n}, TypeError);\ncreateErrorType('ERR_STREAM_PUSH_AFTER_EOF', 'stream.push() after EOF');\ncreateErrorType('ERR_METHOD_NOT_IMPLEMENTED', function (name) {\n  return 'The ' + name + ' method is not implemented';\n});\ncreateErrorType('ERR_STREAM_PREMATURE_CLOSE', 'Premature close');\ncreateErrorType('ERR_STREAM_DESTROYED', function (name) {\n  return 'Cannot call ' + name + ' after a stream was destroyed';\n});\ncreateErrorType('ERR_MULTIPLE_CALLBACK', 'Callback called multiple times');\ncreateErrorType('ERR_STREAM_CANNOT_PIPE', 'Cannot pipe, not readable');\ncreateErrorType('ERR_STREAM_WRITE_AFTER_END', 'write after end');\ncreateErrorType('ERR_STREAM_NULL_VALUES', 'May not write null values to stream', TypeError);\ncreateErrorType('ERR_UNKNOWN_ENCODING', function (arg) {\n  return 'Unknown encoding: ' + arg;\n}, TypeError);\ncreateErrorType('ERR_STREAM_UNSHIFT_AFTER_END_EVENT', 'stream.unshift() after end event');\nmodule.exports.codes = codes;\n", "'use strict';\n\nvar ERR_INVALID_OPT_VALUE = require('../../../errors').codes.ERR_INVALID_OPT_VALUE;\nfunction highWaterMarkFrom(options, isDuplex, duplexKey) {\n  return options.highWaterMark != null ? options.highWaterMark : isDuplex ? options[duplexKey] : null;\n}\nfunction getHighWaterMark(state, options, duplexKey, isDuplex) {\n  var hwm = highWaterMarkFrom(options, isDuplex, duplexKey);\n  if (hwm != null) {\n    if (!(isFinite(hwm) && Math.floor(hwm) === hwm) || hwm < 0) {\n      var name = isDuplex ? duplexKey : 'highWaterMark';\n      throw new ERR_INVALID_OPT_VALUE(name, hwm);\n    }\n    return Math.floor(hwm);\n  }\n\n  // Default value\n  return state.objectMode ? 16 : 16 * 1024;\n}\nmodule.exports = {\n  getHighWaterMark: getHighWaterMark\n};", "\n/**\n * Module exports.\n */\n\nmodule.exports = deprecate;\n\n/**\n * Mark that a method should not be used.\n * Returns a modified function which warns once by default.\n *\n * If `localStorage.noDeprecation = true` is set, then it is a no-op.\n *\n * If `localStorage.throwDeprecation = true` is set, then deprecated functions\n * will throw an Error when invoked.\n *\n * If `localStorage.traceDeprecation = true` is set, then deprecated functions\n * will invoke `console.trace()` instead of `console.error()`.\n *\n * @param {Function} fn - the function to deprecate\n * @param {String} msg - the string to print to the console when `fn` is invoked\n * @returns {Function} a new \"deprecated\" version of `fn`\n * @api public\n */\n\nfunction deprecate (fn, msg) {\n  if (config('noDeprecation')) {\n    return fn;\n  }\n\n  var warned = false;\n  function deprecated() {\n    if (!warned) {\n      if (config('throwDeprecation')) {\n        throw new Error(msg);\n      } else if (config('traceDeprecation')) {\n        console.trace(msg);\n      } else {\n        console.warn(msg);\n      }\n      warned = true;\n    }\n    return fn.apply(this, arguments);\n  }\n\n  return deprecated;\n}\n\n/**\n * Checks `localStorage` for boolean values for the given `name`.\n *\n * @param {String} name\n * @returns {Boolean}\n * @api private\n */\n\nfunction config (name) {\n  // accessing global.localStorage can trigger a DOMException in sandboxed iframes\n  try {\n    if (!global.localStorage) return false;\n  } catch (_) {\n    return false;\n  }\n  var val = global.localStorage[name];\n  if (null == val) return false;\n  return String(val).toLowerCase() === 'true';\n}\n", "// Copyright Joyent, Inc. and other Node contributors.\n//\n// Permission is hereby granted, free of charge, to any person obtaining a\n// copy of this software and associated documentation files (the\n// \"Software\"), to deal in the Software without restriction, including\n// without limitation the rights to use, copy, modify, merge, publish,\n// distribute, sublicense, and/or sell copies of the Software, and to permit\n// persons to whom the Software is furnished to do so, subject to the\n// following conditions:\n//\n// The above copyright notice and this permission notice shall be included\n// in all copies or substantial portions of the Software.\n//\n// THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS\n// OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\n// MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN\n// NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM,\n// DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR\n// OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE\n// USE OR OTHER DEALINGS IN THE SOFTWARE.\n\n// A bit simpler than readable streams.\n// Implement an async ._write(chunk, encoding, cb), and it'll handle all\n// the drain event emission and buffering.\n\n'use strict';\n\nmodule.exports = Writable;\n\n/* <replacement> */\nfunction WriteReq(chunk, encoding, cb) {\n  this.chunk = chunk;\n  this.encoding = encoding;\n  this.callback = cb;\n  this.next = null;\n}\n\n// It seems a linked list but it is not\n// there will be only 2 of these for each stream\nfunction CorkedRequest(state) {\n  var _this = this;\n  this.next = null;\n  this.entry = null;\n  this.finish = function () {\n    onCorkedFinish(_this, state);\n  };\n}\n/* </replacement> */\n\n/*<replacement>*/\nvar Duplex;\n/*</replacement>*/\n\nWritable.WritableState = WritableState;\n\n/*<replacement>*/\nvar internalUtil = {\n  deprecate: require('util-deprecate')\n};\n/*</replacement>*/\n\n/*<replacement>*/\nvar Stream = require('./internal/streams/stream');\n/*</replacement>*/\n\nvar Buffer = require('buffer').Buffer;\nvar OurUint8Array = (typeof global !== 'undefined' ? global : typeof window !== 'undefined' ? window : typeof self !== 'undefined' ? self : {}).Uint8Array || function () {};\nfunction _uint8ArrayToBuffer(chunk) {\n  return Buffer.from(chunk);\n}\nfunction _isUint8Array(obj) {\n  return Buffer.isBuffer(obj) || obj instanceof OurUint8Array;\n}\nvar destroyImpl = require('./internal/streams/destroy');\nvar _require = require('./internal/streams/state'),\n  getHighWaterMark = _require.getHighWaterMark;\nvar _require$codes = require('../errors').codes,\n  ERR_INVALID_ARG_TYPE = _require$codes.ERR_INVALID_ARG_TYPE,\n  ERR_METHOD_NOT_IMPLEMENTED = _require$codes.ERR_METHOD_NOT_IMPLEMENTED,\n  ERR_MULTIPLE_CALLBACK = _require$codes.ERR_MULTIPLE_CALLBACK,\n  ERR_STREAM_CANNOT_PIPE = _require$codes.ERR_STREAM_CANNOT_PIPE,\n  ERR_STREAM_DESTROYED = _require$codes.ERR_STREAM_DESTROYED,\n  ERR_STREAM_NULL_VALUES = _require$codes.ERR_STREAM_NULL_VALUES,\n  ERR_STREAM_WRITE_AFTER_END = _require$codes.ERR_STREAM_WRITE_AFTER_END,\n  ERR_UNKNOWN_ENCODING = _require$codes.ERR_UNKNOWN_ENCODING;\nvar errorOrDestroy = destroyImpl.errorOrDestroy;\nrequire('inherits')(Writable, Stream);\nfunction nop() {}\nfunction WritableState(options, stream, isDuplex) {\n  Duplex = Duplex || require('./_stream_duplex');\n  options = options || {};\n\n  // Duplex streams are both readable and writable, but share\n  // the same options object.\n  // However, some cases require setting options to different\n  // values for the readable and the writable sides of the duplex stream,\n  // e.g. options.readableObjectMode vs. options.writableObjectMode, etc.\n  if (typeof isDuplex !== 'boolean') isDuplex = stream instanceof Duplex;\n\n  // object stream flag to indicate whether or not this stream\n  // contains buffers or objects.\n  this.objectMode = !!options.objectMode;\n  if (isDuplex) this.objectMode = this.objectMode || !!options.writableObjectMode;\n\n  // the point at which write() starts returning false\n  // Note: 0 is a valid value, means that we always return false if\n  // the entire buffer is not flushed immediately on write()\n  this.highWaterMark = getHighWaterMark(this, options, 'writableHighWaterMark', isDuplex);\n\n  // if _final has been called\n  this.finalCalled = false;\n\n  // drain event flag.\n  this.needDrain = false;\n  // at the start of calling end()\n  this.ending = false;\n  // when end() has been called, and returned\n  this.ended = false;\n  // when 'finish' is emitted\n  this.finished = false;\n\n  // has it been destroyed\n  this.destroyed = false;\n\n  // should we decode strings into buffers before passing to _write?\n  // this is here so that some node-core streams can optimize string\n  // handling at a lower level.\n  var noDecode = options.decodeStrings === false;\n  this.decodeStrings = !noDecode;\n\n  // Crypto is kind of old and crusty.  Historically, its default string\n  // encoding is 'binary' so we have to make this configurable.\n  // Everything else in the universe uses 'utf8', though.\n  this.defaultEncoding = options.defaultEncoding || 'utf8';\n\n  // not an actual buffer we keep track of, but a measurement\n  // of how much we're waiting to get pushed to some underlying\n  // socket or file.\n  this.length = 0;\n\n  // a flag to see when we're in the middle of a write.\n  this.writing = false;\n\n  // when true all writes will be buffered until .uncork() call\n  this.corked = 0;\n\n  // a flag to be able to tell if the onwrite cb is called immediately,\n  // or on a later tick.  We set this to true at first, because any\n  // actions that shouldn't happen until \"later\" should generally also\n  // not happen before the first write call.\n  this.sync = true;\n\n  // a flag to know if we're processing previously buffered items, which\n  // may call the _write() callback in the same tick, so that we don't\n  // end up in an overlapped onwrite situation.\n  this.bufferProcessing = false;\n\n  // the callback that's passed to _write(chunk,cb)\n  this.onwrite = function (er) {\n    onwrite(stream, er);\n  };\n\n  // the callback that the user supplies to write(chunk,encoding,cb)\n  this.writecb = null;\n\n  // the amount that is being written when _write is called.\n  this.writelen = 0;\n  this.bufferedRequest = null;\n  this.lastBufferedRequest = null;\n\n  // number of pending user-supplied write callbacks\n  // this must be 0 before 'finish' can be emitted\n  this.pendingcb = 0;\n\n  // emit prefinish if the only thing we're waiting for is _write cbs\n  // This is relevant for synchronous Transform streams\n  this.prefinished = false;\n\n  // True if the error was already emitted and should not be thrown again\n  this.errorEmitted = false;\n\n  // Should close be emitted on destroy. Defaults to true.\n  this.emitClose = options.emitClose !== false;\n\n  // Should .destroy() be called after 'finish' (and potentially 'end')\n  this.autoDestroy = !!options.autoDestroy;\n\n  // count buffered requests\n  this.bufferedRequestCount = 0;\n\n  // allocate the first CorkedRequest, there is always\n  // one allocated and free to use, and we maintain at most two\n  this.corkedRequestsFree = new CorkedRequest(this);\n}\nWritableState.prototype.getBuffer = function getBuffer() {\n  var current = this.bufferedRequest;\n  var out = [];\n  while (current) {\n    out.push(current);\n    current = current.next;\n  }\n  return out;\n};\n(function () {\n  try {\n    Object.defineProperty(WritableState.prototype, 'buffer', {\n      get: internalUtil.deprecate(function writableStateBufferGetter() {\n        return this.getBuffer();\n      }, '_writableState.buffer is deprecated. Use _writableState.getBuffer ' + 'instead.', 'DEP0003')\n    });\n  } catch (_) {}\n})();\n\n// Test _writableState for inheritance to account for Duplex streams,\n// whose prototype chain only points to Readable.\nvar realHasInstance;\nif (typeof Symbol === 'function' && Symbol.hasInstance && typeof Function.prototype[Symbol.hasInstance] === 'function') {\n  realHasInstance = Function.prototype[Symbol.hasInstance];\n  Object.defineProperty(Writable, Symbol.hasInstance, {\n    value: function value(object) {\n      if (realHasInstance.call(this, object)) return true;\n      if (this !== Writable) return false;\n      return object && object._writableState instanceof WritableState;\n    }\n  });\n} else {\n  realHasInstance = function realHasInstance(object) {\n    return object instanceof this;\n  };\n}\nfunction Writable(options) {\n  Duplex = Duplex || require('./_stream_duplex');\n\n  // Writable ctor is applied to Duplexes, too.\n  // `realHasInstance` is necessary because using plain `instanceof`\n  // would return false, as no `_writableState` property is attached.\n\n  // Trying to use the custom `instanceof` for Writable here will also break the\n  // Node.js LazyTransform implementation, which has a non-trivial getter for\n  // `_writableState` that would lead to infinite recursion.\n\n  // Checking for a Stream.Duplex instance is faster here instead of inside\n  // the WritableState constructor, at least with V8 6.5\n  var isDuplex = this instanceof Duplex;\n  if (!isDuplex && !realHasInstance.call(Writable, this)) return new Writable(options);\n  this._writableState = new WritableState(options, this, isDuplex);\n\n  // legacy.\n  this.writable = true;\n  if (options) {\n    if (typeof options.write === 'function') this._write = options.write;\n    if (typeof options.writev === 'function') this._writev = options.writev;\n    if (typeof options.destroy === 'function') this._destroy = options.destroy;\n    if (typeof options.final === 'function') this._final = options.final;\n  }\n  Stream.call(this);\n}\n\n// Otherwise people can pipe Writable streams, which is just wrong.\nWritable.prototype.pipe = function () {\n  errorOrDestroy(this, new ERR_STREAM_CANNOT_PIPE());\n};\nfunction writeAfterEnd(stream, cb) {\n  var er = new ERR_STREAM_WRITE_AFTER_END();\n  // TODO: defer error events consistently everywhere, not just the cb\n  errorOrDestroy(stream, er);\n  process.nextTick(cb, er);\n}\n\n// Checks that a user-supplied chunk is valid, especially for the particular\n// mode the stream is in. Currently this means that `null` is never accepted\n// and undefined/non-string values are only allowed in object mode.\nfunction validChunk(stream, state, chunk, cb) {\n  var er;\n  if (chunk === null) {\n    er = new ERR_STREAM_NULL_VALUES();\n  } else if (typeof chunk !== 'string' && !state.objectMode) {\n    er = new ERR_INVALID_ARG_TYPE('chunk', ['string', 'Buffer'], chunk);\n  }\n  if (er) {\n    errorOrDestroy(stream, er);\n    process.nextTick(cb, er);\n    return false;\n  }\n  return true;\n}\nWritable.prototype.write = function (chunk, encoding, cb) {\n  var state = this._writableState;\n  var ret = false;\n  var isBuf = !state.objectMode && _isUint8Array(chunk);\n  if (isBuf && !Buffer.isBuffer(chunk)) {\n    chunk = _uint8ArrayToBuffer(chunk);\n  }\n  if (typeof encoding === 'function') {\n    cb = encoding;\n    encoding = null;\n  }\n  if (isBuf) encoding = 'buffer';else if (!encoding) encoding = state.defaultEncoding;\n  if (typeof cb !== 'function') cb = nop;\n  if (state.ending) writeAfterEnd(this, cb);else if (isBuf || validChunk(this, state, chunk, cb)) {\n    state.pendingcb++;\n    ret = writeOrBuffer(this, state, isBuf, chunk, encoding, cb);\n  }\n  return ret;\n};\nWritable.prototype.cork = function () {\n  this._writableState.corked++;\n};\nWritable.prototype.uncork = function () {\n  var state = this._writableState;\n  if (state.corked) {\n    state.corked--;\n    if (!state.writing && !state.corked && !state.bufferProcessing && state.bufferedRequest) clearBuffer(this, state);\n  }\n};\nWritable.prototype.setDefaultEncoding = function setDefaultEncoding(encoding) {\n  // node::ParseEncoding() requires lower case.\n  if (typeof encoding === 'string') encoding = encoding.toLowerCase();\n  if (!(['hex', 'utf8', 'utf-8', 'ascii', 'binary', 'base64', 'ucs2', 'ucs-2', 'utf16le', 'utf-16le', 'raw'].indexOf((encoding + '').toLowerCase()) > -1)) throw new ERR_UNKNOWN_ENCODING(encoding);\n  this._writableState.defaultEncoding = encoding;\n  return this;\n};\nObject.defineProperty(Writable.prototype, 'writableBuffer', {\n  // making it explicit this property is not enumerable\n  // because otherwise some prototype manipulation in\n  // userland will fail\n  enumerable: false,\n  get: function get() {\n    return this._writableState && this._writableState.getBuffer();\n  }\n});\nfunction decodeChunk(state, chunk, encoding) {\n  if (!state.objectMode && state.decodeStrings !== false && typeof chunk === 'string') {\n    chunk = Buffer.from(chunk, encoding);\n  }\n  return chunk;\n}\nObject.defineProperty(Writable.prototype, 'writableHighWaterMark', {\n  // making it explicit this property is not enumerable\n  // because otherwise some prototype manipulation in\n  // userland will fail\n  enumerable: false,\n  get: function get() {\n    return this._writableState.highWaterMark;\n  }\n});\n\n// if we're already writing something, then just put this\n// in the queue, and wait our turn.  Otherwise, call _write\n// If we return false, then we need a drain event, so set that flag.\nfunction writeOrBuffer(stream, state, isBuf, chunk, encoding, cb) {\n  if (!isBuf) {\n    var newChunk = decodeChunk(state, chunk, encoding);\n    if (chunk !== newChunk) {\n      isBuf = true;\n      encoding = 'buffer';\n      chunk = newChunk;\n    }\n  }\n  var len = state.objectMode ? 1 : chunk.length;\n  state.length += len;\n  var ret = state.length < state.highWaterMark;\n  // we must ensure that previous needDrain will not be reset to false.\n  if (!ret) state.needDrain = true;\n  if (state.writing || state.corked) {\n    var last = state.lastBufferedRequest;\n    state.lastBufferedRequest = {\n      chunk: chunk,\n      encoding: encoding,\n      isBuf: isBuf,\n      callback: cb,\n      next: null\n    };\n    if (last) {\n      last.next = state.lastBufferedRequest;\n    } else {\n      state.bufferedRequest = state.lastBufferedRequest;\n    }\n    state.bufferedRequestCount += 1;\n  } else {\n    doWrite(stream, state, false, len, chunk, encoding, cb);\n  }\n  return ret;\n}\nfunction doWrite(stream, state, writev, len, chunk, encoding, cb) {\n  state.writelen = len;\n  state.writecb = cb;\n  state.writing = true;\n  state.sync = true;\n  if (state.destroyed) state.onwrite(new ERR_STREAM_DESTROYED('write'));else if (writev) stream._writev(chunk, state.onwrite);else stream._write(chunk, encoding, state.onwrite);\n  state.sync = false;\n}\nfunction onwriteError(stream, state, sync, er, cb) {\n  --state.pendingcb;\n  if (sync) {\n    // defer the callback if we are being called synchronously\n    // to avoid piling up things on the stack\n    process.nextTick(cb, er);\n    // this can emit finish, and it will always happen\n    // after error\n    process.nextTick(finishMaybe, stream, state);\n    stream._writableState.errorEmitted = true;\n    errorOrDestroy(stream, er);\n  } else {\n    // the caller expect this to happen before if\n    // it is async\n    cb(er);\n    stream._writableState.errorEmitted = true;\n    errorOrDestroy(stream, er);\n    // this can emit finish, but finish must\n    // always follow error\n    finishMaybe(stream, state);\n  }\n}\nfunction onwriteStateUpdate(state) {\n  state.writing = false;\n  state.writecb = null;\n  state.length -= state.writelen;\n  state.writelen = 0;\n}\nfunction onwrite(stream, er) {\n  var state = stream._writableState;\n  var sync = state.sync;\n  var cb = state.writecb;\n  if (typeof cb !== 'function') throw new ERR_MULTIPLE_CALLBACK();\n  onwriteStateUpdate(state);\n  if (er) onwriteError(stream, state, sync, er, cb);else {\n    // Check if we're actually ready to finish, but don't emit yet\n    var finished = needFinish(state) || stream.destroyed;\n    if (!finished && !state.corked && !state.bufferProcessing && state.bufferedRequest) {\n      clearBuffer(stream, state);\n    }\n    if (sync) {\n      process.nextTick(afterWrite, stream, state, finished, cb);\n    } else {\n      afterWrite(stream, state, finished, cb);\n    }\n  }\n}\nfunction afterWrite(stream, state, finished, cb) {\n  if (!finished) onwriteDrain(stream, state);\n  state.pendingcb--;\n  cb();\n  finishMaybe(stream, state);\n}\n\n// Must force callback to be called on nextTick, so that we don't\n// emit 'drain' before the write() consumer gets the 'false' return\n// value, and has a chance to attach a 'drain' listener.\nfunction onwriteDrain(stream, state) {\n  if (state.length === 0 && state.needDrain) {\n    state.needDrain = false;\n    stream.emit('drain');\n  }\n}\n\n// if there's something in the buffer waiting, then process it\nfunction clearBuffer(stream, state) {\n  state.bufferProcessing = true;\n  var entry = state.bufferedRequest;\n  if (stream._writev && entry && entry.next) {\n    // Fast case, write everything using _writev()\n    var l = state.bufferedRequestCount;\n    var buffer = new Array(l);\n    var holder = state.corkedRequestsFree;\n    holder.entry = entry;\n    var count = 0;\n    var allBuffers = true;\n    while (entry) {\n      buffer[count] = entry;\n      if (!entry.isBuf) allBuffers = false;\n      entry = entry.next;\n      count += 1;\n    }\n    buffer.allBuffers = allBuffers;\n    doWrite(stream, state, true, state.length, buffer, '', holder.finish);\n\n    // doWrite is almost always async, defer these to save a bit of time\n    // as the hot path ends with doWrite\n    state.pendingcb++;\n    state.lastBufferedRequest = null;\n    if (holder.next) {\n      state.corkedRequestsFree = holder.next;\n      holder.next = null;\n    } else {\n      state.corkedRequestsFree = new CorkedRequest(state);\n    }\n    state.bufferedRequestCount = 0;\n  } else {\n    // Slow case, write chunks one-by-one\n    while (entry) {\n      var chunk = entry.chunk;\n      var encoding = entry.encoding;\n      var cb = entry.callback;\n      var len = state.objectMode ? 1 : chunk.length;\n      doWrite(stream, state, false, len, chunk, encoding, cb);\n      entry = entry.next;\n      state.bufferedRequestCount--;\n      // if we didn't call the onwrite immediately, then\n      // it means that we need to wait until it does.\n      // also, that means that the chunk and cb are currently\n      // being processed, so move the buffer counter past them.\n      if (state.writing) {\n        break;\n      }\n    }\n    if (entry === null) state.lastBufferedRequest = null;\n  }\n  state.bufferedRequest = entry;\n  state.bufferProcessing = false;\n}\nWritable.prototype._write = function (chunk, encoding, cb) {\n  cb(new ERR_METHOD_NOT_IMPLEMENTED('_write()'));\n};\nWritable.prototype._writev = null;\nWritable.prototype.end = function (chunk, encoding, cb) {\n  var state = this._writableState;\n  if (typeof chunk === 'function') {\n    cb = chunk;\n    chunk = null;\n    encoding = null;\n  } else if (typeof encoding === 'function') {\n    cb = encoding;\n    encoding = null;\n  }\n  if (chunk !== null && chunk !== undefined) this.write(chunk, encoding);\n\n  // .end() fully uncorks\n  if (state.corked) {\n    state.corked = 1;\n    this.uncork();\n  }\n\n  // ignore unnecessary end() calls.\n  if (!state.ending) endWritable(this, state, cb);\n  return this;\n};\nObject.defineProperty(Writable.prototype, 'writableLength', {\n  // making it explicit this property is not enumerable\n  // because otherwise some prototype manipulation in\n  // userland will fail\n  enumerable: false,\n  get: function get() {\n    return this._writableState.length;\n  }\n});\nfunction needFinish(state) {\n  return state.ending && state.length === 0 && state.bufferedRequest === null && !state.finished && !state.writing;\n}\nfunction callFinal(stream, state) {\n  stream._final(function (err) {\n    state.pendingcb--;\n    if (err) {\n      errorOrDestroy(stream, err);\n    }\n    state.prefinished = true;\n    stream.emit('prefinish');\n    finishMaybe(stream, state);\n  });\n}\nfunction prefinish(stream, state) {\n  if (!state.prefinished && !state.finalCalled) {\n    if (typeof stream._final === 'function' && !state.destroyed) {\n      state.pendingcb++;\n      state.finalCalled = true;\n      process.nextTick(callFinal, stream, state);\n    } else {\n      state.prefinished = true;\n      stream.emit('prefinish');\n    }\n  }\n}\nfunction finishMaybe(stream, state) {\n  var need = needFinish(state);\n  if (need) {\n    prefinish(stream, state);\n    if (state.pendingcb === 0) {\n      state.finished = true;\n      stream.emit('finish');\n      if (state.autoDestroy) {\n        // In case of duplex streams we need a way to detect\n        // if the readable side is ready for autoDestroy as well\n        var rState = stream._readableState;\n        if (!rState || rState.autoDestroy && rState.endEmitted) {\n          stream.destroy();\n        }\n      }\n    }\n  }\n  return need;\n}\nfunction endWritable(stream, state, cb) {\n  state.ending = true;\n  finishMaybe(stream, state);\n  if (cb) {\n    if (state.finished) process.nextTick(cb);else stream.once('finish', cb);\n  }\n  state.ended = true;\n  stream.writable = false;\n}\nfunction onCorkedFinish(corkReq, state, err) {\n  var entry = corkReq.entry;\n  corkReq.entry = null;\n  while (entry) {\n    var cb = entry.callback;\n    state.pendingcb--;\n    cb(err);\n    entry = entry.next;\n  }\n\n  // reuse the free corkReq.\n  state.corkedRequestsFree.next = corkReq;\n}\nObject.defineProperty(Writable.prototype, 'destroyed', {\n  // making it explicit this property is not enumerable\n  // because otherwise some prototype manipulation in\n  // userland will fail\n  enumerable: false,\n  get: function get() {\n    if (this._writableState === undefined) {\n      return false;\n    }\n    return this._writableState.destroyed;\n  },\n  set: function set(value) {\n    // we ignore the value if the stream\n    // has not been initialized yet\n    if (!this._writableState) {\n      return;\n    }\n\n    // backward compatibility, the user is explicitly\n    // managing destroyed\n    this._writableState.destroyed = value;\n  }\n});\nWritable.prototype.destroy = destroyImpl.destroy;\nWritable.prototype._undestroy = destroyImpl.undestroy;\nWritable.prototype._destroy = function (err, cb) {\n  cb(err);\n};", "// Copyright Joyent, Inc. and other Node contributors.\n//\n// Permission is hereby granted, free of charge, to any person obtaining a\n// copy of this software and associated documentation files (the\n// \"Software\"), to deal in the Software without restriction, including\n// without limitation the rights to use, copy, modify, merge, publish,\n// distribute, sublicense, and/or sell copies of the Software, and to permit\n// persons to whom the Software is furnished to do so, subject to the\n// following conditions:\n//\n// The above copyright notice and this permission notice shall be included\n// in all copies or substantial portions of the Software.\n//\n// THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS\n// OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\n// MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN\n// NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM,\n// DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR\n// OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE\n// USE OR OTHER DEALINGS IN THE SOFTWARE.\n\n// a duplex stream is just a stream that is both readable and writable.\n// Since JS doesn't have multiple prototypal inheritance, this class\n// prototypally inherits from Readable, and then parasitically from\n// Writable.\n\n'use strict';\n\n/*<replacement>*/\nvar objectKeys = Object.keys || function (obj) {\n  var keys = [];\n  for (var key in obj) keys.push(key);\n  return keys;\n};\n/*</replacement>*/\n\nmodule.exports = Duplex;\nvar Readable = require('./_stream_readable');\nvar Writable = require('./_stream_writable');\nrequire('inherits')(Duplex, Readable);\n{\n  // Allow the keys array to be GC'ed.\n  var keys = objectKeys(Writable.prototype);\n  for (var v = 0; v < keys.length; v++) {\n    var method = keys[v];\n    if (!Duplex.prototype[method]) Duplex.prototype[method] = Writable.prototype[method];\n  }\n}\nfunction Duplex(options) {\n  if (!(this instanceof Duplex)) return new Duplex(options);\n  Readable.call(this, options);\n  Writable.call(this, options);\n  this.allowHalfOpen = true;\n  if (options) {\n    if (options.readable === false) this.readable = false;\n    if (options.writable === false) this.writable = false;\n    if (options.allowHalfOpen === false) {\n      this.allowHalfOpen = false;\n      this.once('end', onend);\n    }\n  }\n}\nObject.defineProperty(Duplex.prototype, 'writableHighWaterMark', {\n  // making it explicit this property is not enumerable\n  // because otherwise some prototype manipulation in\n  // userland will fail\n  enumerable: false,\n  get: function get() {\n    return this._writableState.highWaterMark;\n  }\n});\nObject.defineProperty(Duplex.prototype, 'writableBuffer', {\n  // making it explicit this property is not enumerable\n  // because otherwise some prototype manipulation in\n  // userland will fail\n  enumerable: false,\n  get: function get() {\n    return this._writableState && this._writableState.getBuffer();\n  }\n});\nObject.defineProperty(Duplex.prototype, 'writableLength', {\n  // making it explicit this property is not enumerable\n  // because otherwise some prototype manipulation in\n  // userland will fail\n  enumerable: false,\n  get: function get() {\n    return this._writableState.length;\n  }\n});\n\n// the no-half-open enforcer\nfunction onend() {\n  // If the writable side ended, then we're ok.\n  if (this._writableState.ended) return;\n\n  // no more data can be written.\n  // But allow more writes to happen in this tick.\n  process.nextTick(onEndNT, this);\n}\nfunction onEndNT(self) {\n  self.end();\n}\nObject.defineProperty(Duplex.prototype, 'destroyed', {\n  // making it explicit this property is not enumerable\n  // because otherwise some prototype manipulation in\n  // userland will fail\n  enumerable: false,\n  get: function get() {\n    if (this._readableState === undefined || this._writableState === undefined) {\n      return false;\n    }\n    return this._readableState.destroyed && this._writableState.destroyed;\n  },\n  set: function set(value) {\n    // we ignore the value if the stream\n    // has not been initialized yet\n    if (this._readableState === undefined || this._writableState === undefined) {\n      return;\n    }\n\n    // backward compatibility, the user is explicitly\n    // managing destroyed\n    this._readableState.destroyed = value;\n    this._writableState.destroyed = value;\n  }\n});", "// Ported from https://github.com/mafintosh/end-of-stream with\n// permission from the author, <PERSON> (@mafintosh).\n\n'use strict';\n\nvar ERR_STREAM_PREMATURE_CLOSE = require('../../../errors').codes.ERR_STREAM_PREMATURE_CLOSE;\nfunction once(callback) {\n  var called = false;\n  return function () {\n    if (called) return;\n    called = true;\n    for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n      args[_key] = arguments[_key];\n    }\n    callback.apply(this, args);\n  };\n}\nfunction noop() {}\nfunction isRequest(stream) {\n  return stream.setHeader && typeof stream.abort === 'function';\n}\nfunction eos(stream, opts, callback) {\n  if (typeof opts === 'function') return eos(stream, null, opts);\n  if (!opts) opts = {};\n  callback = once(callback || noop);\n  var readable = opts.readable || opts.readable !== false && stream.readable;\n  var writable = opts.writable || opts.writable !== false && stream.writable;\n  var onlegacyfinish = function onlegacyfinish() {\n    if (!stream.writable) onfinish();\n  };\n  var writableEnded = stream._writableState && stream._writableState.finished;\n  var onfinish = function onfinish() {\n    writable = false;\n    writableEnded = true;\n    if (!readable) callback.call(stream);\n  };\n  var readableEnded = stream._readableState && stream._readableState.endEmitted;\n  var onend = function onend() {\n    readable = false;\n    readableEnded = true;\n    if (!writable) callback.call(stream);\n  };\n  var onerror = function onerror(err) {\n    callback.call(stream, err);\n  };\n  var onclose = function onclose() {\n    var err;\n    if (readable && !readableEnded) {\n      if (!stream._readableState || !stream._readableState.ended) err = new ERR_STREAM_PREMATURE_CLOSE();\n      return callback.call(stream, err);\n    }\n    if (writable && !writableEnded) {\n      if (!stream._writableState || !stream._writableState.ended) err = new ERR_STREAM_PREMATURE_CLOSE();\n      return callback.call(stream, err);\n    }\n  };\n  var onrequest = function onrequest() {\n    stream.req.on('finish', onfinish);\n  };\n  if (isRequest(stream)) {\n    stream.on('complete', onfinish);\n    stream.on('abort', onclose);\n    if (stream.req) onrequest();else stream.on('request', onrequest);\n  } else if (writable && !stream._writableState) {\n    // legacy streams\n    stream.on('end', onlegacyfinish);\n    stream.on('close', onlegacyfinish);\n  }\n  stream.on('end', onend);\n  stream.on('finish', onfinish);\n  if (opts.error !== false) stream.on('error', onerror);\n  stream.on('close', onclose);\n  return function () {\n    stream.removeListener('complete', onfinish);\n    stream.removeListener('abort', onclose);\n    stream.removeListener('request', onrequest);\n    if (stream.req) stream.req.removeListener('finish', onfinish);\n    stream.removeListener('end', onlegacyfinish);\n    stream.removeListener('close', onlegacyfinish);\n    stream.removeListener('finish', onfinish);\n    stream.removeListener('end', onend);\n    stream.removeListener('error', onerror);\n    stream.removeListener('close', onclose);\n  };\n}\nmodule.exports = eos;", "'use strict';\n\nvar _Object$setPrototypeO;\nfunction _defineProperty(obj, key, value) { key = _toPropertyKey(key); if (key in obj) { Object.defineProperty(obj, key, { value: value, enumerable: true, configurable: true, writable: true }); } else { obj[key] = value; } return obj; }\nfunction _toPropertyKey(arg) { var key = _toPrimitive(arg, \"string\"); return typeof key === \"symbol\" ? key : String(key); }\nfunction _toPrimitive(input, hint) { if (typeof input !== \"object\" || input === null) return input; var prim = input[Symbol.toPrimitive]; if (prim !== undefined) { var res = prim.call(input, hint || \"default\"); if (typeof res !== \"object\") return res; throw new TypeError(\"@@toPrimitive must return a primitive value.\"); } return (hint === \"string\" ? String : Number)(input); }\nvar finished = require('./end-of-stream');\nvar kLastResolve = Symbol('lastResolve');\nvar kLastReject = Symbol('lastReject');\nvar kError = Symbol('error');\nvar kEnded = Symbol('ended');\nvar kLastPromise = Symbol('lastPromise');\nvar kHandlePromise = Symbol('handlePromise');\nvar kStream = Symbol('stream');\nfunction createIterResult(value, done) {\n  return {\n    value: value,\n    done: done\n  };\n}\nfunction readAndResolve(iter) {\n  var resolve = iter[kLastResolve];\n  if (resolve !== null) {\n    var data = iter[kStream].read();\n    // we defer if data is null\n    // we can be expecting either 'end' or\n    // 'error'\n    if (data !== null) {\n      iter[kLastPromise] = null;\n      iter[kLastResolve] = null;\n      iter[kLastReject] = null;\n      resolve(createIterResult(data, false));\n    }\n  }\n}\nfunction onReadable(iter) {\n  // we wait for the next tick, because it might\n  // emit an error with process.nextTick\n  process.nextTick(readAndResolve, iter);\n}\nfunction wrapForNext(lastPromise, iter) {\n  return function (resolve, reject) {\n    lastPromise.then(function () {\n      if (iter[kEnded]) {\n        resolve(createIterResult(undefined, true));\n        return;\n      }\n      iter[kHandlePromise](resolve, reject);\n    }, reject);\n  };\n}\nvar AsyncIteratorPrototype = Object.getPrototypeOf(function () {});\nvar ReadableStreamAsyncIteratorPrototype = Object.setPrototypeOf((_Object$setPrototypeO = {\n  get stream() {\n    return this[kStream];\n  },\n  next: function next() {\n    var _this = this;\n    // if we have detected an error in the meanwhile\n    // reject straight away\n    var error = this[kError];\n    if (error !== null) {\n      return Promise.reject(error);\n    }\n    if (this[kEnded]) {\n      return Promise.resolve(createIterResult(undefined, true));\n    }\n    if (this[kStream].destroyed) {\n      // We need to defer via nextTick because if .destroy(err) is\n      // called, the error will be emitted via nextTick, and\n      // we cannot guarantee that there is no error lingering around\n      // waiting to be emitted.\n      return new Promise(function (resolve, reject) {\n        process.nextTick(function () {\n          if (_this[kError]) {\n            reject(_this[kError]);\n          } else {\n            resolve(createIterResult(undefined, true));\n          }\n        });\n      });\n    }\n\n    // if we have multiple next() calls\n    // we will wait for the previous Promise to finish\n    // this logic is optimized to support for await loops,\n    // where next() is only called once at a time\n    var lastPromise = this[kLastPromise];\n    var promise;\n    if (lastPromise) {\n      promise = new Promise(wrapForNext(lastPromise, this));\n    } else {\n      // fast path needed to support multiple this.push()\n      // without triggering the next() queue\n      var data = this[kStream].read();\n      if (data !== null) {\n        return Promise.resolve(createIterResult(data, false));\n      }\n      promise = new Promise(this[kHandlePromise]);\n    }\n    this[kLastPromise] = promise;\n    return promise;\n  }\n}, _defineProperty(_Object$setPrototypeO, Symbol.asyncIterator, function () {\n  return this;\n}), _defineProperty(_Object$setPrototypeO, \"return\", function _return() {\n  var _this2 = this;\n  // destroy(err, cb) is a private API\n  // we can guarantee we have that here, because we control the\n  // Readable class this is attached to\n  return new Promise(function (resolve, reject) {\n    _this2[kStream].destroy(null, function (err) {\n      if (err) {\n        reject(err);\n        return;\n      }\n      resolve(createIterResult(undefined, true));\n    });\n  });\n}), _Object$setPrototypeO), AsyncIteratorPrototype);\nvar createReadableStreamAsyncIterator = function createReadableStreamAsyncIterator(stream) {\n  var _Object$create;\n  var iterator = Object.create(ReadableStreamAsyncIteratorPrototype, (_Object$create = {}, _defineProperty(_Object$create, kStream, {\n    value: stream,\n    writable: true\n  }), _defineProperty(_Object$create, kLastResolve, {\n    value: null,\n    writable: true\n  }), _defineProperty(_Object$create, kLastReject, {\n    value: null,\n    writable: true\n  }), _defineProperty(_Object$create, kError, {\n    value: null,\n    writable: true\n  }), _defineProperty(_Object$create, kEnded, {\n    value: stream._readableState.endEmitted,\n    writable: true\n  }), _defineProperty(_Object$create, kHandlePromise, {\n    value: function value(resolve, reject) {\n      var data = iterator[kStream].read();\n      if (data) {\n        iterator[kLastPromise] = null;\n        iterator[kLastResolve] = null;\n        iterator[kLastReject] = null;\n        resolve(createIterResult(data, false));\n      } else {\n        iterator[kLastResolve] = resolve;\n        iterator[kLastReject] = reject;\n      }\n    },\n    writable: true\n  }), _Object$create));\n  iterator[kLastPromise] = null;\n  finished(stream, function (err) {\n    if (err && err.code !== 'ERR_STREAM_PREMATURE_CLOSE') {\n      var reject = iterator[kLastReject];\n      // reject if we are waiting for data in the Promise\n      // returned by next() and store the error\n      if (reject !== null) {\n        iterator[kLastPromise] = null;\n        iterator[kLastResolve] = null;\n        iterator[kLastReject] = null;\n        reject(err);\n      }\n      iterator[kError] = err;\n      return;\n    }\n    var resolve = iterator[kLastResolve];\n    if (resolve !== null) {\n      iterator[kLastPromise] = null;\n      iterator[kLastResolve] = null;\n      iterator[kLastReject] = null;\n      resolve(createIterResult(undefined, true));\n    }\n    iterator[kEnded] = true;\n  });\n  stream.on('readable', onReadable.bind(null, iterator));\n  return iterator;\n};\nmodule.exports = createReadableStreamAsyncIterator;", "module.exports = function () {\n  throw new Error('Readable.from is not available in the browser')\n};\n", "// Copyright Joyent, Inc. and other Node contributors.\n//\n// Permission is hereby granted, free of charge, to any person obtaining a\n// copy of this software and associated documentation files (the\n// \"Software\"), to deal in the Software without restriction, including\n// without limitation the rights to use, copy, modify, merge, publish,\n// distribute, sublicense, and/or sell copies of the Software, and to permit\n// persons to whom the Software is furnished to do so, subject to the\n// following conditions:\n//\n// The above copyright notice and this permission notice shall be included\n// in all copies or substantial portions of the Software.\n//\n// THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS\n// OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\n// MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN\n// NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM,\n// DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR\n// OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE\n// USE OR OTHER DEALINGS IN THE SOFTWARE.\n\n'use strict';\n\nmodule.exports = Readable;\n\n/*<replacement>*/\nvar Duplex;\n/*</replacement>*/\n\nReadable.ReadableState = ReadableState;\n\n/*<replacement>*/\nvar EE = require('events').EventEmitter;\nvar EElistenerCount = function EElistenerCount(emitter, type) {\n  return emitter.listeners(type).length;\n};\n/*</replacement>*/\n\n/*<replacement>*/\nvar Stream = require('./internal/streams/stream');\n/*</replacement>*/\n\nvar Buffer = require('buffer').Buffer;\nvar OurUint8Array = (typeof global !== 'undefined' ? global : typeof window !== 'undefined' ? window : typeof self !== 'undefined' ? self : {}).Uint8Array || function () {};\nfunction _uint8ArrayToBuffer(chunk) {\n  return Buffer.from(chunk);\n}\nfunction _isUint8Array(obj) {\n  return Buffer.isBuffer(obj) || obj instanceof OurUint8Array;\n}\n\n/*<replacement>*/\nvar debugUtil = require('util');\nvar debug;\nif (debugUtil && debugUtil.debuglog) {\n  debug = debugUtil.debuglog('stream');\n} else {\n  debug = function debug() {};\n}\n/*</replacement>*/\n\nvar BufferList = require('./internal/streams/buffer_list');\nvar destroyImpl = require('./internal/streams/destroy');\nvar _require = require('./internal/streams/state'),\n  getHighWaterMark = _require.getHighWaterMark;\nvar _require$codes = require('../errors').codes,\n  ERR_INVALID_ARG_TYPE = _require$codes.ERR_INVALID_ARG_TYPE,\n  ERR_STREAM_PUSH_AFTER_EOF = _require$codes.ERR_STREAM_PUSH_AFTER_EOF,\n  ERR_METHOD_NOT_IMPLEMENTED = _require$codes.ERR_METHOD_NOT_IMPLEMENTED,\n  ERR_STREAM_UNSHIFT_AFTER_END_EVENT = _require$codes.ERR_STREAM_UNSHIFT_AFTER_END_EVENT;\n\n// Lazy loaded to improve the startup performance.\nvar StringDecoder;\nvar createReadableStreamAsyncIterator;\nvar from;\nrequire('inherits')(Readable, Stream);\nvar errorOrDestroy = destroyImpl.errorOrDestroy;\nvar kProxyEvents = ['error', 'close', 'destroy', 'pause', 'resume'];\nfunction prependListener(emitter, event, fn) {\n  // Sadly this is not cacheable as some libraries bundle their own\n  // event emitter implementation with them.\n  if (typeof emitter.prependListener === 'function') return emitter.prependListener(event, fn);\n\n  // This is a hack to make sure that our error handler is attached before any\n  // userland ones.  NEVER DO THIS. This is here only because this code needs\n  // to continue to work with older versions of Node.js that do not include\n  // the prependListener() method. The goal is to eventually remove this hack.\n  if (!emitter._events || !emitter._events[event]) emitter.on(event, fn);else if (Array.isArray(emitter._events[event])) emitter._events[event].unshift(fn);else emitter._events[event] = [fn, emitter._events[event]];\n}\nfunction ReadableState(options, stream, isDuplex) {\n  Duplex = Duplex || require('./_stream_duplex');\n  options = options || {};\n\n  // Duplex streams are both readable and writable, but share\n  // the same options object.\n  // However, some cases require setting options to different\n  // values for the readable and the writable sides of the duplex stream.\n  // These options can be provided separately as readableXXX and writableXXX.\n  if (typeof isDuplex !== 'boolean') isDuplex = stream instanceof Duplex;\n\n  // object stream flag. Used to make read(n) ignore n and to\n  // make all the buffer merging and length checks go away\n  this.objectMode = !!options.objectMode;\n  if (isDuplex) this.objectMode = this.objectMode || !!options.readableObjectMode;\n\n  // the point at which it stops calling _read() to fill the buffer\n  // Note: 0 is a valid value, means \"don't call _read preemptively ever\"\n  this.highWaterMark = getHighWaterMark(this, options, 'readableHighWaterMark', isDuplex);\n\n  // A linked list is used to store data chunks instead of an array because the\n  // linked list can remove elements from the beginning faster than\n  // array.shift()\n  this.buffer = new BufferList();\n  this.length = 0;\n  this.pipes = null;\n  this.pipesCount = 0;\n  this.flowing = null;\n  this.ended = false;\n  this.endEmitted = false;\n  this.reading = false;\n\n  // a flag to be able to tell if the event 'readable'/'data' is emitted\n  // immediately, or on a later tick.  We set this to true at first, because\n  // any actions that shouldn't happen until \"later\" should generally also\n  // not happen before the first read call.\n  this.sync = true;\n\n  // whenever we return null, then we set a flag to say\n  // that we're awaiting a 'readable' event emission.\n  this.needReadable = false;\n  this.emittedReadable = false;\n  this.readableListening = false;\n  this.resumeScheduled = false;\n  this.paused = true;\n\n  // Should close be emitted on destroy. Defaults to true.\n  this.emitClose = options.emitClose !== false;\n\n  // Should .destroy() be called after 'end' (and potentially 'finish')\n  this.autoDestroy = !!options.autoDestroy;\n\n  // has it been destroyed\n  this.destroyed = false;\n\n  // Crypto is kind of old and crusty.  Historically, its default string\n  // encoding is 'binary' so we have to make this configurable.\n  // Everything else in the universe uses 'utf8', though.\n  this.defaultEncoding = options.defaultEncoding || 'utf8';\n\n  // the number of writers that are awaiting a drain event in .pipe()s\n  this.awaitDrain = 0;\n\n  // if true, a maybeReadMore has been scheduled\n  this.readingMore = false;\n  this.decoder = null;\n  this.encoding = null;\n  if (options.encoding) {\n    if (!StringDecoder) StringDecoder = require('string_decoder/').StringDecoder;\n    this.decoder = new StringDecoder(options.encoding);\n    this.encoding = options.encoding;\n  }\n}\nfunction Readable(options) {\n  Duplex = Duplex || require('./_stream_duplex');\n  if (!(this instanceof Readable)) return new Readable(options);\n\n  // Checking for a Stream.Duplex instance is faster here instead of inside\n  // the ReadableState constructor, at least with V8 6.5\n  var isDuplex = this instanceof Duplex;\n  this._readableState = new ReadableState(options, this, isDuplex);\n\n  // legacy\n  this.readable = true;\n  if (options) {\n    if (typeof options.read === 'function') this._read = options.read;\n    if (typeof options.destroy === 'function') this._destroy = options.destroy;\n  }\n  Stream.call(this);\n}\nObject.defineProperty(Readable.prototype, 'destroyed', {\n  // making it explicit this property is not enumerable\n  // because otherwise some prototype manipulation in\n  // userland will fail\n  enumerable: false,\n  get: function get() {\n    if (this._readableState === undefined) {\n      return false;\n    }\n    return this._readableState.destroyed;\n  },\n  set: function set(value) {\n    // we ignore the value if the stream\n    // has not been initialized yet\n    if (!this._readableState) {\n      return;\n    }\n\n    // backward compatibility, the user is explicitly\n    // managing destroyed\n    this._readableState.destroyed = value;\n  }\n});\nReadable.prototype.destroy = destroyImpl.destroy;\nReadable.prototype._undestroy = destroyImpl.undestroy;\nReadable.prototype._destroy = function (err, cb) {\n  cb(err);\n};\n\n// Manually shove something into the read() buffer.\n// This returns true if the highWaterMark has not been hit yet,\n// similar to how Writable.write() returns true if you should\n// write() some more.\nReadable.prototype.push = function (chunk, encoding) {\n  var state = this._readableState;\n  var skipChunkCheck;\n  if (!state.objectMode) {\n    if (typeof chunk === 'string') {\n      encoding = encoding || state.defaultEncoding;\n      if (encoding !== state.encoding) {\n        chunk = Buffer.from(chunk, encoding);\n        encoding = '';\n      }\n      skipChunkCheck = true;\n    }\n  } else {\n    skipChunkCheck = true;\n  }\n  return readableAddChunk(this, chunk, encoding, false, skipChunkCheck);\n};\n\n// Unshift should *always* be something directly out of read()\nReadable.prototype.unshift = function (chunk) {\n  return readableAddChunk(this, chunk, null, true, false);\n};\nfunction readableAddChunk(stream, chunk, encoding, addToFront, skipChunkCheck) {\n  debug('readableAddChunk', chunk);\n  var state = stream._readableState;\n  if (chunk === null) {\n    state.reading = false;\n    onEofChunk(stream, state);\n  } else {\n    var er;\n    if (!skipChunkCheck) er = chunkInvalid(state, chunk);\n    if (er) {\n      errorOrDestroy(stream, er);\n    } else if (state.objectMode || chunk && chunk.length > 0) {\n      if (typeof chunk !== 'string' && !state.objectMode && Object.getPrototypeOf(chunk) !== Buffer.prototype) {\n        chunk = _uint8ArrayToBuffer(chunk);\n      }\n      if (addToFront) {\n        if (state.endEmitted) errorOrDestroy(stream, new ERR_STREAM_UNSHIFT_AFTER_END_EVENT());else addChunk(stream, state, chunk, true);\n      } else if (state.ended) {\n        errorOrDestroy(stream, new ERR_STREAM_PUSH_AFTER_EOF());\n      } else if (state.destroyed) {\n        return false;\n      } else {\n        state.reading = false;\n        if (state.decoder && !encoding) {\n          chunk = state.decoder.write(chunk);\n          if (state.objectMode || chunk.length !== 0) addChunk(stream, state, chunk, false);else maybeReadMore(stream, state);\n        } else {\n          addChunk(stream, state, chunk, false);\n        }\n      }\n    } else if (!addToFront) {\n      state.reading = false;\n      maybeReadMore(stream, state);\n    }\n  }\n\n  // We can push more data if we are below the highWaterMark.\n  // Also, if we have no data yet, we can stand some more bytes.\n  // This is to work around cases where hwm=0, such as the repl.\n  return !state.ended && (state.length < state.highWaterMark || state.length === 0);\n}\nfunction addChunk(stream, state, chunk, addToFront) {\n  if (state.flowing && state.length === 0 && !state.sync) {\n    state.awaitDrain = 0;\n    stream.emit('data', chunk);\n  } else {\n    // update the buffer info.\n    state.length += state.objectMode ? 1 : chunk.length;\n    if (addToFront) state.buffer.unshift(chunk);else state.buffer.push(chunk);\n    if (state.needReadable) emitReadable(stream);\n  }\n  maybeReadMore(stream, state);\n}\nfunction chunkInvalid(state, chunk) {\n  var er;\n  if (!_isUint8Array(chunk) && typeof chunk !== 'string' && chunk !== undefined && !state.objectMode) {\n    er = new ERR_INVALID_ARG_TYPE('chunk', ['string', 'Buffer', 'Uint8Array'], chunk);\n  }\n  return er;\n}\nReadable.prototype.isPaused = function () {\n  return this._readableState.flowing === false;\n};\n\n// backwards compatibility.\nReadable.prototype.setEncoding = function (enc) {\n  if (!StringDecoder) StringDecoder = require('string_decoder/').StringDecoder;\n  var decoder = new StringDecoder(enc);\n  this._readableState.decoder = decoder;\n  // If setEncoding(null), decoder.encoding equals utf8\n  this._readableState.encoding = this._readableState.decoder.encoding;\n\n  // Iterate over current buffer to convert already stored Buffers:\n  var p = this._readableState.buffer.head;\n  var content = '';\n  while (p !== null) {\n    content += decoder.write(p.data);\n    p = p.next;\n  }\n  this._readableState.buffer.clear();\n  if (content !== '') this._readableState.buffer.push(content);\n  this._readableState.length = content.length;\n  return this;\n};\n\n// Don't raise the hwm > 1GB\nvar MAX_HWM = 0x40000000;\nfunction computeNewHighWaterMark(n) {\n  if (n >= MAX_HWM) {\n    // TODO(ronag): Throw ERR_VALUE_OUT_OF_RANGE.\n    n = MAX_HWM;\n  } else {\n    // Get the next highest power of 2 to prevent increasing hwm excessively in\n    // tiny amounts\n    n--;\n    n |= n >>> 1;\n    n |= n >>> 2;\n    n |= n >>> 4;\n    n |= n >>> 8;\n    n |= n >>> 16;\n    n++;\n  }\n  return n;\n}\n\n// This function is designed to be inlinable, so please take care when making\n// changes to the function body.\nfunction howMuchToRead(n, state) {\n  if (n <= 0 || state.length === 0 && state.ended) return 0;\n  if (state.objectMode) return 1;\n  if (n !== n) {\n    // Only flow one buffer at a time\n    if (state.flowing && state.length) return state.buffer.head.data.length;else return state.length;\n  }\n  // If we're asking for more than the current hwm, then raise the hwm.\n  if (n > state.highWaterMark) state.highWaterMark = computeNewHighWaterMark(n);\n  if (n <= state.length) return n;\n  // Don't have enough\n  if (!state.ended) {\n    state.needReadable = true;\n    return 0;\n  }\n  return state.length;\n}\n\n// you can override either this method, or the async _read(n) below.\nReadable.prototype.read = function (n) {\n  debug('read', n);\n  n = parseInt(n, 10);\n  var state = this._readableState;\n  var nOrig = n;\n  if (n !== 0) state.emittedReadable = false;\n\n  // if we're doing read(0) to trigger a readable event, but we\n  // already have a bunch of data in the buffer, then just trigger\n  // the 'readable' event and move on.\n  if (n === 0 && state.needReadable && ((state.highWaterMark !== 0 ? state.length >= state.highWaterMark : state.length > 0) || state.ended)) {\n    debug('read: emitReadable', state.length, state.ended);\n    if (state.length === 0 && state.ended) endReadable(this);else emitReadable(this);\n    return null;\n  }\n  n = howMuchToRead(n, state);\n\n  // if we've ended, and we're now clear, then finish it up.\n  if (n === 0 && state.ended) {\n    if (state.length === 0) endReadable(this);\n    return null;\n  }\n\n  // All the actual chunk generation logic needs to be\n  // *below* the call to _read.  The reason is that in certain\n  // synthetic stream cases, such as passthrough streams, _read\n  // may be a completely synchronous operation which may change\n  // the state of the read buffer, providing enough data when\n  // before there was *not* enough.\n  //\n  // So, the steps are:\n  // 1. Figure out what the state of things will be after we do\n  // a read from the buffer.\n  //\n  // 2. If that resulting state will trigger a _read, then call _read.\n  // Note that this may be asynchronous, or synchronous.  Yes, it is\n  // deeply ugly to write APIs this way, but that still doesn't mean\n  // that the Readable class should behave improperly, as streams are\n  // designed to be sync/async agnostic.\n  // Take note if the _read call is sync or async (ie, if the read call\n  // has returned yet), so that we know whether or not it's safe to emit\n  // 'readable' etc.\n  //\n  // 3. Actually pull the requested chunks out of the buffer and return.\n\n  // if we need a readable event, then we need to do some reading.\n  var doRead = state.needReadable;\n  debug('need readable', doRead);\n\n  // if we currently have less than the highWaterMark, then also read some\n  if (state.length === 0 || state.length - n < state.highWaterMark) {\n    doRead = true;\n    debug('length less than watermark', doRead);\n  }\n\n  // however, if we've ended, then there's no point, and if we're already\n  // reading, then it's unnecessary.\n  if (state.ended || state.reading) {\n    doRead = false;\n    debug('reading or ended', doRead);\n  } else if (doRead) {\n    debug('do read');\n    state.reading = true;\n    state.sync = true;\n    // if the length is currently zero, then we *need* a readable event.\n    if (state.length === 0) state.needReadable = true;\n    // call internal read method\n    this._read(state.highWaterMark);\n    state.sync = false;\n    // If _read pushed data synchronously, then `reading` will be false,\n    // and we need to re-evaluate how much data we can return to the user.\n    if (!state.reading) n = howMuchToRead(nOrig, state);\n  }\n  var ret;\n  if (n > 0) ret = fromList(n, state);else ret = null;\n  if (ret === null) {\n    state.needReadable = state.length <= state.highWaterMark;\n    n = 0;\n  } else {\n    state.length -= n;\n    state.awaitDrain = 0;\n  }\n  if (state.length === 0) {\n    // If we have nothing in the buffer, then we want to know\n    // as soon as we *do* get something into the buffer.\n    if (!state.ended) state.needReadable = true;\n\n    // If we tried to read() past the EOF, then emit end on the next tick.\n    if (nOrig !== n && state.ended) endReadable(this);\n  }\n  if (ret !== null) this.emit('data', ret);\n  return ret;\n};\nfunction onEofChunk(stream, state) {\n  debug('onEofChunk');\n  if (state.ended) return;\n  if (state.decoder) {\n    var chunk = state.decoder.end();\n    if (chunk && chunk.length) {\n      state.buffer.push(chunk);\n      state.length += state.objectMode ? 1 : chunk.length;\n    }\n  }\n  state.ended = true;\n  if (state.sync) {\n    // if we are sync, wait until next tick to emit the data.\n    // Otherwise we risk emitting data in the flow()\n    // the readable code triggers during a read() call\n    emitReadable(stream);\n  } else {\n    // emit 'readable' now to make sure it gets picked up.\n    state.needReadable = false;\n    if (!state.emittedReadable) {\n      state.emittedReadable = true;\n      emitReadable_(stream);\n    }\n  }\n}\n\n// Don't emit readable right away in sync mode, because this can trigger\n// another read() call => stack overflow.  This way, it might trigger\n// a nextTick recursion warning, but that's not so bad.\nfunction emitReadable(stream) {\n  var state = stream._readableState;\n  debug('emitReadable', state.needReadable, state.emittedReadable);\n  state.needReadable = false;\n  if (!state.emittedReadable) {\n    debug('emitReadable', state.flowing);\n    state.emittedReadable = true;\n    process.nextTick(emitReadable_, stream);\n  }\n}\nfunction emitReadable_(stream) {\n  var state = stream._readableState;\n  debug('emitReadable_', state.destroyed, state.length, state.ended);\n  if (!state.destroyed && (state.length || state.ended)) {\n    stream.emit('readable');\n    state.emittedReadable = false;\n  }\n\n  // The stream needs another readable event if\n  // 1. It is not flowing, as the flow mechanism will take\n  //    care of it.\n  // 2. It is not ended.\n  // 3. It is below the highWaterMark, so we can schedule\n  //    another readable later.\n  state.needReadable = !state.flowing && !state.ended && state.length <= state.highWaterMark;\n  flow(stream);\n}\n\n// at this point, the user has presumably seen the 'readable' event,\n// and called read() to consume some data.  that may have triggered\n// in turn another _read(n) call, in which case reading = true if\n// it's in progress.\n// However, if we're not ended, or reading, and the length < hwm,\n// then go ahead and try to read some more preemptively.\nfunction maybeReadMore(stream, state) {\n  if (!state.readingMore) {\n    state.readingMore = true;\n    process.nextTick(maybeReadMore_, stream, state);\n  }\n}\nfunction maybeReadMore_(stream, state) {\n  // Attempt to read more data if we should.\n  //\n  // The conditions for reading more data are (one of):\n  // - Not enough data buffered (state.length < state.highWaterMark). The loop\n  //   is responsible for filling the buffer with enough data if such data\n  //   is available. If highWaterMark is 0 and we are not in the flowing mode\n  //   we should _not_ attempt to buffer any extra data. We'll get more data\n  //   when the stream consumer calls read() instead.\n  // - No data in the buffer, and the stream is in flowing mode. In this mode\n  //   the loop below is responsible for ensuring read() is called. Failing to\n  //   call read here would abort the flow and there's no other mechanism for\n  //   continuing the flow if the stream consumer has just subscribed to the\n  //   'data' event.\n  //\n  // In addition to the above conditions to keep reading data, the following\n  // conditions prevent the data from being read:\n  // - The stream has ended (state.ended).\n  // - There is already a pending 'read' operation (state.reading). This is a\n  //   case where the the stream has called the implementation defined _read()\n  //   method, but they are processing the call asynchronously and have _not_\n  //   called push() with new data. In this case we skip performing more\n  //   read()s. The execution ends in this method again after the _read() ends\n  //   up calling push() with more data.\n  while (!state.reading && !state.ended && (state.length < state.highWaterMark || state.flowing && state.length === 0)) {\n    var len = state.length;\n    debug('maybeReadMore read 0');\n    stream.read(0);\n    if (len === state.length)\n      // didn't get any data, stop spinning.\n      break;\n  }\n  state.readingMore = false;\n}\n\n// abstract method.  to be overridden in specific implementation classes.\n// call cb(er, data) where data is <= n in length.\n// for virtual (non-string, non-buffer) streams, \"length\" is somewhat\n// arbitrary, and perhaps not very meaningful.\nReadable.prototype._read = function (n) {\n  errorOrDestroy(this, new ERR_METHOD_NOT_IMPLEMENTED('_read()'));\n};\nReadable.prototype.pipe = function (dest, pipeOpts) {\n  var src = this;\n  var state = this._readableState;\n  switch (state.pipesCount) {\n    case 0:\n      state.pipes = dest;\n      break;\n    case 1:\n      state.pipes = [state.pipes, dest];\n      break;\n    default:\n      state.pipes.push(dest);\n      break;\n  }\n  state.pipesCount += 1;\n  debug('pipe count=%d opts=%j', state.pipesCount, pipeOpts);\n  var doEnd = (!pipeOpts || pipeOpts.end !== false) && dest !== process.stdout && dest !== process.stderr;\n  var endFn = doEnd ? onend : unpipe;\n  if (state.endEmitted) process.nextTick(endFn);else src.once('end', endFn);\n  dest.on('unpipe', onunpipe);\n  function onunpipe(readable, unpipeInfo) {\n    debug('onunpipe');\n    if (readable === src) {\n      if (unpipeInfo && unpipeInfo.hasUnpiped === false) {\n        unpipeInfo.hasUnpiped = true;\n        cleanup();\n      }\n    }\n  }\n  function onend() {\n    debug('onend');\n    dest.end();\n  }\n\n  // when the dest drains, it reduces the awaitDrain counter\n  // on the source.  This would be more elegant with a .once()\n  // handler in flow(), but adding and removing repeatedly is\n  // too slow.\n  var ondrain = pipeOnDrain(src);\n  dest.on('drain', ondrain);\n  var cleanedUp = false;\n  function cleanup() {\n    debug('cleanup');\n    // cleanup event handlers once the pipe is broken\n    dest.removeListener('close', onclose);\n    dest.removeListener('finish', onfinish);\n    dest.removeListener('drain', ondrain);\n    dest.removeListener('error', onerror);\n    dest.removeListener('unpipe', onunpipe);\n    src.removeListener('end', onend);\n    src.removeListener('end', unpipe);\n    src.removeListener('data', ondata);\n    cleanedUp = true;\n\n    // if the reader is waiting for a drain event from this\n    // specific writer, then it would cause it to never start\n    // flowing again.\n    // So, if this is awaiting a drain, then we just call it now.\n    // If we don't know, then assume that we are waiting for one.\n    if (state.awaitDrain && (!dest._writableState || dest._writableState.needDrain)) ondrain();\n  }\n  src.on('data', ondata);\n  function ondata(chunk) {\n    debug('ondata');\n    var ret = dest.write(chunk);\n    debug('dest.write', ret);\n    if (ret === false) {\n      // If the user unpiped during `dest.write()`, it is possible\n      // to get stuck in a permanently paused state if that write\n      // also returned false.\n      // => Check whether `dest` is still a piping destination.\n      if ((state.pipesCount === 1 && state.pipes === dest || state.pipesCount > 1 && indexOf(state.pipes, dest) !== -1) && !cleanedUp) {\n        debug('false write response, pause', state.awaitDrain);\n        state.awaitDrain++;\n      }\n      src.pause();\n    }\n  }\n\n  // if the dest has an error, then stop piping into it.\n  // however, don't suppress the throwing behavior for this.\n  function onerror(er) {\n    debug('onerror', er);\n    unpipe();\n    dest.removeListener('error', onerror);\n    if (EElistenerCount(dest, 'error') === 0) errorOrDestroy(dest, er);\n  }\n\n  // Make sure our error handler is attached before userland ones.\n  prependListener(dest, 'error', onerror);\n\n  // Both close and finish should trigger unpipe, but only once.\n  function onclose() {\n    dest.removeListener('finish', onfinish);\n    unpipe();\n  }\n  dest.once('close', onclose);\n  function onfinish() {\n    debug('onfinish');\n    dest.removeListener('close', onclose);\n    unpipe();\n  }\n  dest.once('finish', onfinish);\n  function unpipe() {\n    debug('unpipe');\n    src.unpipe(dest);\n  }\n\n  // tell the dest that it's being piped to\n  dest.emit('pipe', src);\n\n  // start the flow if it hasn't been started already.\n  if (!state.flowing) {\n    debug('pipe resume');\n    src.resume();\n  }\n  return dest;\n};\nfunction pipeOnDrain(src) {\n  return function pipeOnDrainFunctionResult() {\n    var state = src._readableState;\n    debug('pipeOnDrain', state.awaitDrain);\n    if (state.awaitDrain) state.awaitDrain--;\n    if (state.awaitDrain === 0 && EElistenerCount(src, 'data')) {\n      state.flowing = true;\n      flow(src);\n    }\n  };\n}\nReadable.prototype.unpipe = function (dest) {\n  var state = this._readableState;\n  var unpipeInfo = {\n    hasUnpiped: false\n  };\n\n  // if we're not piping anywhere, then do nothing.\n  if (state.pipesCount === 0) return this;\n\n  // just one destination.  most common case.\n  if (state.pipesCount === 1) {\n    // passed in one, but it's not the right one.\n    if (dest && dest !== state.pipes) return this;\n    if (!dest) dest = state.pipes;\n\n    // got a match.\n    state.pipes = null;\n    state.pipesCount = 0;\n    state.flowing = false;\n    if (dest) dest.emit('unpipe', this, unpipeInfo);\n    return this;\n  }\n\n  // slow case. multiple pipe destinations.\n\n  if (!dest) {\n    // remove all.\n    var dests = state.pipes;\n    var len = state.pipesCount;\n    state.pipes = null;\n    state.pipesCount = 0;\n    state.flowing = false;\n    for (var i = 0; i < len; i++) dests[i].emit('unpipe', this, {\n      hasUnpiped: false\n    });\n    return this;\n  }\n\n  // try to find the right one.\n  var index = indexOf(state.pipes, dest);\n  if (index === -1) return this;\n  state.pipes.splice(index, 1);\n  state.pipesCount -= 1;\n  if (state.pipesCount === 1) state.pipes = state.pipes[0];\n  dest.emit('unpipe', this, unpipeInfo);\n  return this;\n};\n\n// set up data events if they are asked for\n// Ensure readable listeners eventually get something\nReadable.prototype.on = function (ev, fn) {\n  var res = Stream.prototype.on.call(this, ev, fn);\n  var state = this._readableState;\n  if (ev === 'data') {\n    // update readableListening so that resume() may be a no-op\n    // a few lines down. This is needed to support once('readable').\n    state.readableListening = this.listenerCount('readable') > 0;\n\n    // Try start flowing on next tick if stream isn't explicitly paused\n    if (state.flowing !== false) this.resume();\n  } else if (ev === 'readable') {\n    if (!state.endEmitted && !state.readableListening) {\n      state.readableListening = state.needReadable = true;\n      state.flowing = false;\n      state.emittedReadable = false;\n      debug('on readable', state.length, state.reading);\n      if (state.length) {\n        emitReadable(this);\n      } else if (!state.reading) {\n        process.nextTick(nReadingNextTick, this);\n      }\n    }\n  }\n  return res;\n};\nReadable.prototype.addListener = Readable.prototype.on;\nReadable.prototype.removeListener = function (ev, fn) {\n  var res = Stream.prototype.removeListener.call(this, ev, fn);\n  if (ev === 'readable') {\n    // We need to check if there is someone still listening to\n    // readable and reset the state. However this needs to happen\n    // after readable has been emitted but before I/O (nextTick) to\n    // support once('readable', fn) cycles. This means that calling\n    // resume within the same tick will have no\n    // effect.\n    process.nextTick(updateReadableListening, this);\n  }\n  return res;\n};\nReadable.prototype.removeAllListeners = function (ev) {\n  var res = Stream.prototype.removeAllListeners.apply(this, arguments);\n  if (ev === 'readable' || ev === undefined) {\n    // We need to check if there is someone still listening to\n    // readable and reset the state. However this needs to happen\n    // after readable has been emitted but before I/O (nextTick) to\n    // support once('readable', fn) cycles. This means that calling\n    // resume within the same tick will have no\n    // effect.\n    process.nextTick(updateReadableListening, this);\n  }\n  return res;\n};\nfunction updateReadableListening(self) {\n  var state = self._readableState;\n  state.readableListening = self.listenerCount('readable') > 0;\n  if (state.resumeScheduled && !state.paused) {\n    // flowing needs to be set to true now, otherwise\n    // the upcoming resume will not flow.\n    state.flowing = true;\n\n    // crude way to check if we should resume\n  } else if (self.listenerCount('data') > 0) {\n    self.resume();\n  }\n}\nfunction nReadingNextTick(self) {\n  debug('readable nexttick read 0');\n  self.read(0);\n}\n\n// pause() and resume() are remnants of the legacy readable stream API\n// If the user uses them, then switch into old mode.\nReadable.prototype.resume = function () {\n  var state = this._readableState;\n  if (!state.flowing) {\n    debug('resume');\n    // we flow only if there is no one listening\n    // for readable, but we still have to call\n    // resume()\n    state.flowing = !state.readableListening;\n    resume(this, state);\n  }\n  state.paused = false;\n  return this;\n};\nfunction resume(stream, state) {\n  if (!state.resumeScheduled) {\n    state.resumeScheduled = true;\n    process.nextTick(resume_, stream, state);\n  }\n}\nfunction resume_(stream, state) {\n  debug('resume', state.reading);\n  if (!state.reading) {\n    stream.read(0);\n  }\n  state.resumeScheduled = false;\n  stream.emit('resume');\n  flow(stream);\n  if (state.flowing && !state.reading) stream.read(0);\n}\nReadable.prototype.pause = function () {\n  debug('call pause flowing=%j', this._readableState.flowing);\n  if (this._readableState.flowing !== false) {\n    debug('pause');\n    this._readableState.flowing = false;\n    this.emit('pause');\n  }\n  this._readableState.paused = true;\n  return this;\n};\nfunction flow(stream) {\n  var state = stream._readableState;\n  debug('flow', state.flowing);\n  while (state.flowing && stream.read() !== null);\n}\n\n// wrap an old-style stream as the async data source.\n// This is *not* part of the readable stream interface.\n// It is an ugly unfortunate mess of history.\nReadable.prototype.wrap = function (stream) {\n  var _this = this;\n  var state = this._readableState;\n  var paused = false;\n  stream.on('end', function () {\n    debug('wrapped end');\n    if (state.decoder && !state.ended) {\n      var chunk = state.decoder.end();\n      if (chunk && chunk.length) _this.push(chunk);\n    }\n    _this.push(null);\n  });\n  stream.on('data', function (chunk) {\n    debug('wrapped data');\n    if (state.decoder) chunk = state.decoder.write(chunk);\n\n    // don't skip over falsy values in objectMode\n    if (state.objectMode && (chunk === null || chunk === undefined)) return;else if (!state.objectMode && (!chunk || !chunk.length)) return;\n    var ret = _this.push(chunk);\n    if (!ret) {\n      paused = true;\n      stream.pause();\n    }\n  });\n\n  // proxy all the other methods.\n  // important when wrapping filters and duplexes.\n  for (var i in stream) {\n    if (this[i] === undefined && typeof stream[i] === 'function') {\n      this[i] = function methodWrap(method) {\n        return function methodWrapReturnFunction() {\n          return stream[method].apply(stream, arguments);\n        };\n      }(i);\n    }\n  }\n\n  // proxy certain important events.\n  for (var n = 0; n < kProxyEvents.length; n++) {\n    stream.on(kProxyEvents[n], this.emit.bind(this, kProxyEvents[n]));\n  }\n\n  // when we try to consume some more bytes, simply unpause the\n  // underlying stream.\n  this._read = function (n) {\n    debug('wrapped _read', n);\n    if (paused) {\n      paused = false;\n      stream.resume();\n    }\n  };\n  return this;\n};\nif (typeof Symbol === 'function') {\n  Readable.prototype[Symbol.asyncIterator] = function () {\n    if (createReadableStreamAsyncIterator === undefined) {\n      createReadableStreamAsyncIterator = require('./internal/streams/async_iterator');\n    }\n    return createReadableStreamAsyncIterator(this);\n  };\n}\nObject.defineProperty(Readable.prototype, 'readableHighWaterMark', {\n  // making it explicit this property is not enumerable\n  // because otherwise some prototype manipulation in\n  // userland will fail\n  enumerable: false,\n  get: function get() {\n    return this._readableState.highWaterMark;\n  }\n});\nObject.defineProperty(Readable.prototype, 'readableBuffer', {\n  // making it explicit this property is not enumerable\n  // because otherwise some prototype manipulation in\n  // userland will fail\n  enumerable: false,\n  get: function get() {\n    return this._readableState && this._readableState.buffer;\n  }\n});\nObject.defineProperty(Readable.prototype, 'readableFlowing', {\n  // making it explicit this property is not enumerable\n  // because otherwise some prototype manipulation in\n  // userland will fail\n  enumerable: false,\n  get: function get() {\n    return this._readableState.flowing;\n  },\n  set: function set(state) {\n    if (this._readableState) {\n      this._readableState.flowing = state;\n    }\n  }\n});\n\n// exposed for testing purposes only.\nReadable._fromList = fromList;\nObject.defineProperty(Readable.prototype, 'readableLength', {\n  // making it explicit this property is not enumerable\n  // because otherwise some prototype manipulation in\n  // userland will fail\n  enumerable: false,\n  get: function get() {\n    return this._readableState.length;\n  }\n});\n\n// Pluck off n bytes from an array of buffers.\n// Length is the combined lengths of all the buffers in the list.\n// This function is designed to be inlinable, so please take care when making\n// changes to the function body.\nfunction fromList(n, state) {\n  // nothing buffered\n  if (state.length === 0) return null;\n  var ret;\n  if (state.objectMode) ret = state.buffer.shift();else if (!n || n >= state.length) {\n    // read it all, truncate the list\n    if (state.decoder) ret = state.buffer.join('');else if (state.buffer.length === 1) ret = state.buffer.first();else ret = state.buffer.concat(state.length);\n    state.buffer.clear();\n  } else {\n    // read part of list\n    ret = state.buffer.consume(n, state.decoder);\n  }\n  return ret;\n}\nfunction endReadable(stream) {\n  var state = stream._readableState;\n  debug('endReadable', state.endEmitted);\n  if (!state.endEmitted) {\n    state.ended = true;\n    process.nextTick(endReadableNT, state, stream);\n  }\n}\nfunction endReadableNT(state, stream) {\n  debug('endReadableNT', state.endEmitted, state.length);\n\n  // Check that we didn't get one last unshift.\n  if (!state.endEmitted && state.length === 0) {\n    state.endEmitted = true;\n    stream.readable = false;\n    stream.emit('end');\n    if (state.autoDestroy) {\n      // In case of duplex streams we need a way to detect\n      // if the writable side is ready for autoDestroy as well\n      var wState = stream._writableState;\n      if (!wState || wState.autoDestroy && wState.finished) {\n        stream.destroy();\n      }\n    }\n  }\n}\nif (typeof Symbol === 'function') {\n  Readable.from = function (iterable, opts) {\n    if (from === undefined) {\n      from = require('./internal/streams/from');\n    }\n    return from(Readable, iterable, opts);\n  };\n}\nfunction indexOf(xs, x) {\n  for (var i = 0, l = xs.length; i < l; i++) {\n    if (xs[i] === x) return i;\n  }\n  return -1;\n}", "// Copyright Joyent, Inc. and other Node contributors.\n//\n// Permission is hereby granted, free of charge, to any person obtaining a\n// copy of this software and associated documentation files (the\n// \"Software\"), to deal in the Software without restriction, including\n// without limitation the rights to use, copy, modify, merge, publish,\n// distribute, sublicense, and/or sell copies of the Software, and to permit\n// persons to whom the Software is furnished to do so, subject to the\n// following conditions:\n//\n// The above copyright notice and this permission notice shall be included\n// in all copies or substantial portions of the Software.\n//\n// THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS\n// OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\n// MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN\n// NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM,\n// DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR\n// OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE\n// USE OR OTHER DEALINGS IN THE SOFTWARE.\n\n// a transform stream is a readable/writable stream where you do\n// something with the data.  Sometimes it's called a \"filter\",\n// but that's not a great name for it, since that implies a thing where\n// some bits pass through, and others are simply ignored.  (That would\n// be a valid example of a transform, of course.)\n//\n// While the output is causally related to the input, it's not a\n// necessarily symmetric or synchronous transformation.  For example,\n// a zlib stream might take multiple plain-text writes(), and then\n// emit a single compressed chunk some time in the future.\n//\n// Here's how this works:\n//\n// The Transform stream has all the aspects of the readable and writable\n// stream classes.  When you write(chunk), that calls _write(chunk,cb)\n// internally, and returns false if there's a lot of pending writes\n// buffered up.  When you call read(), that calls _read(n) until\n// there's enough pending readable data buffered up.\n//\n// In a transform stream, the written data is placed in a buffer.  When\n// _read(n) is called, it transforms the queued up data, calling the\n// buffered _write cb's as it consumes chunks.  If consuming a single\n// written chunk would result in multiple output chunks, then the first\n// outputted bit calls the readcb, and subsequent chunks just go into\n// the read buffer, and will cause it to emit 'readable' if necessary.\n//\n// This way, back-pressure is actually determined by the reading side,\n// since _read has to be called to start processing a new chunk.  However,\n// a pathological inflate type of transform can cause excessive buffering\n// here.  For example, imagine a stream where every byte of input is\n// interpreted as an integer from 0-255, and then results in that many\n// bytes of output.  Writing the 4 bytes {ff,ff,ff,ff} would result in\n// 1kb of data being output.  In this case, you could write a very small\n// amount of input, and end up with a very large amount of output.  In\n// such a pathological inflating mechanism, there'd be no way to tell\n// the system to stop doing the transform.  A single 4MB write could\n// cause the system to run out of memory.\n//\n// However, even in such a pathological case, only a single written chunk\n// would be consumed, and then the rest would wait (un-transformed) until\n// the results of the previous transformed chunk were consumed.\n\n'use strict';\n\nmodule.exports = Transform;\nvar _require$codes = require('../errors').codes,\n  ERR_METHOD_NOT_IMPLEMENTED = _require$codes.ERR_METHOD_NOT_IMPLEMENTED,\n  ERR_MULTIPLE_CALLBACK = _require$codes.ERR_MULTIPLE_CALLBACK,\n  ERR_TRANSFORM_ALREADY_TRANSFORMING = _require$codes.ERR_TRANSFORM_ALREADY_TRANSFORMING,\n  ERR_TRANSFORM_WITH_LENGTH_0 = _require$codes.ERR_TRANSFORM_WITH_LENGTH_0;\nvar Duplex = require('./_stream_duplex');\nrequire('inherits')(Transform, Duplex);\nfunction afterTransform(er, data) {\n  var ts = this._transformState;\n  ts.transforming = false;\n  var cb = ts.writecb;\n  if (cb === null) {\n    return this.emit('error', new ERR_MULTIPLE_CALLBACK());\n  }\n  ts.writechunk = null;\n  ts.writecb = null;\n  if (data != null)\n    // single equals check for both `null` and `undefined`\n    this.push(data);\n  cb(er);\n  var rs = this._readableState;\n  rs.reading = false;\n  if (rs.needReadable || rs.length < rs.highWaterMark) {\n    this._read(rs.highWaterMark);\n  }\n}\nfunction Transform(options) {\n  if (!(this instanceof Transform)) return new Transform(options);\n  Duplex.call(this, options);\n  this._transformState = {\n    afterTransform: afterTransform.bind(this),\n    needTransform: false,\n    transforming: false,\n    writecb: null,\n    writechunk: null,\n    writeencoding: null\n  };\n\n  // start out asking for a readable event once data is transformed.\n  this._readableState.needReadable = true;\n\n  // we have implemented the _read method, and done the other things\n  // that Readable wants before the first _read call, so unset the\n  // sync guard flag.\n  this._readableState.sync = false;\n  if (options) {\n    if (typeof options.transform === 'function') this._transform = options.transform;\n    if (typeof options.flush === 'function') this._flush = options.flush;\n  }\n\n  // When the writable side finishes, then flush out anything remaining.\n  this.on('prefinish', prefinish);\n}\nfunction prefinish() {\n  var _this = this;\n  if (typeof this._flush === 'function' && !this._readableState.destroyed) {\n    this._flush(function (er, data) {\n      done(_this, er, data);\n    });\n  } else {\n    done(this, null, null);\n  }\n}\nTransform.prototype.push = function (chunk, encoding) {\n  this._transformState.needTransform = false;\n  return Duplex.prototype.push.call(this, chunk, encoding);\n};\n\n// This is the part where you do stuff!\n// override this function in implementation classes.\n// 'chunk' is an input chunk.\n//\n// Call `push(newChunk)` to pass along transformed output\n// to the readable side.  You may call 'push' zero or more times.\n//\n// Call `cb(err)` when you are done with this chunk.  If you pass\n// an error, then that'll put the hurt on the whole operation.  If you\n// never call cb(), then you'll never get another chunk.\nTransform.prototype._transform = function (chunk, encoding, cb) {\n  cb(new ERR_METHOD_NOT_IMPLEMENTED('_transform()'));\n};\nTransform.prototype._write = function (chunk, encoding, cb) {\n  var ts = this._transformState;\n  ts.writecb = cb;\n  ts.writechunk = chunk;\n  ts.writeencoding = encoding;\n  if (!ts.transforming) {\n    var rs = this._readableState;\n    if (ts.needTransform || rs.needReadable || rs.length < rs.highWaterMark) this._read(rs.highWaterMark);\n  }\n};\n\n// Doesn't matter what the args are here.\n// _transform does all the work.\n// That we got here means that the readable side wants more data.\nTransform.prototype._read = function (n) {\n  var ts = this._transformState;\n  if (ts.writechunk !== null && !ts.transforming) {\n    ts.transforming = true;\n    this._transform(ts.writechunk, ts.writeencoding, ts.afterTransform);\n  } else {\n    // mark that we need a transform, so that any data that comes in\n    // will get processed, now that we've asked for it.\n    ts.needTransform = true;\n  }\n};\nTransform.prototype._destroy = function (err, cb) {\n  Duplex.prototype._destroy.call(this, err, function (err2) {\n    cb(err2);\n  });\n};\nfunction done(stream, er, data) {\n  if (er) return stream.emit('error', er);\n  if (data != null)\n    // single equals check for both `null` and `undefined`\n    stream.push(data);\n\n  // TODO(BridgeAR): Write a test for these two error cases\n  // if there's nothing in the write buffer, then that means\n  // that nothing more will ever be provided\n  if (stream._writableState.length) throw new ERR_TRANSFORM_WITH_LENGTH_0();\n  if (stream._transformState.transforming) throw new ERR_TRANSFORM_ALREADY_TRANSFORMING();\n  return stream.push(null);\n}", "// Copyright Joyent, Inc. and other Node contributors.\n//\n// Permission is hereby granted, free of charge, to any person obtaining a\n// copy of this software and associated documentation files (the\n// \"Software\"), to deal in the Software without restriction, including\n// without limitation the rights to use, copy, modify, merge, publish,\n// distribute, sublicense, and/or sell copies of the Software, and to permit\n// persons to whom the Software is furnished to do so, subject to the\n// following conditions:\n//\n// The above copyright notice and this permission notice shall be included\n// in all copies or substantial portions of the Software.\n//\n// THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS\n// OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\n// MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN\n// NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM,\n// DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR\n// OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE\n// USE OR OTHER DEALINGS IN THE SOFTWARE.\n\n// a passthrough stream.\n// basically just the most minimal sort of Transform stream.\n// Every written chunk gets output as-is.\n\n'use strict';\n\nmodule.exports = PassThrough;\nvar Transform = require('./_stream_transform');\nrequire('inherits')(PassThrough, Transform);\nfunction PassThrough(options) {\n  if (!(this instanceof PassThrough)) return new PassThrough(options);\n  Transform.call(this, options);\n}\nPassThrough.prototype._transform = function (chunk, encoding, cb) {\n  cb(null, chunk);\n};", "// Ported from https://github.com/mafin<PERSON>h/pump with\n// permission from the author, <PERSON> (@mafintosh).\n\n'use strict';\n\nvar eos;\nfunction once(callback) {\n  var called = false;\n  return function () {\n    if (called) return;\n    called = true;\n    callback.apply(void 0, arguments);\n  };\n}\nvar _require$codes = require('../../../errors').codes,\n  ERR_MISSING_ARGS = _require$codes.ERR_MISSING_ARGS,\n  ERR_STREAM_DESTROYED = _require$codes.ERR_STREAM_DESTROYED;\nfunction noop(err) {\n  // Rethrow the error if it exists to avoid swallowing it\n  if (err) throw err;\n}\nfunction isRequest(stream) {\n  return stream.setHeader && typeof stream.abort === 'function';\n}\nfunction destroyer(stream, reading, writing, callback) {\n  callback = once(callback);\n  var closed = false;\n  stream.on('close', function () {\n    closed = true;\n  });\n  if (eos === undefined) eos = require('./end-of-stream');\n  eos(stream, {\n    readable: reading,\n    writable: writing\n  }, function (err) {\n    if (err) return callback(err);\n    closed = true;\n    callback();\n  });\n  var destroyed = false;\n  return function (err) {\n    if (closed) return;\n    if (destroyed) return;\n    destroyed = true;\n\n    // request.destroy just do .end - .abort is what we want\n    if (isRequest(stream)) return stream.abort();\n    if (typeof stream.destroy === 'function') return stream.destroy();\n    callback(err || new ERR_STREAM_DESTROYED('pipe'));\n  };\n}\nfunction call(fn) {\n  fn();\n}\nfunction pipe(from, to) {\n  return from.pipe(to);\n}\nfunction popCallback(streams) {\n  if (!streams.length) return noop;\n  if (typeof streams[streams.length - 1] !== 'function') return noop;\n  return streams.pop();\n}\nfunction pipeline() {\n  for (var _len = arguments.length, streams = new Array(_len), _key = 0; _key < _len; _key++) {\n    streams[_key] = arguments[_key];\n  }\n  var callback = popCallback(streams);\n  if (Array.isArray(streams[0])) streams = streams[0];\n  if (streams.length < 2) {\n    throw new ERR_MISSING_ARGS('streams');\n  }\n  var error;\n  var destroys = streams.map(function (stream, i) {\n    var reading = i < streams.length - 1;\n    var writing = i > 0;\n    return destroyer(stream, reading, writing, function (err) {\n      if (!error) error = err;\n      if (err) destroys.forEach(call);\n      if (reading) return;\n      destroys.forEach(call);\n      callback(error);\n    });\n  });\n  return streams.reduce(pipe);\n}\nmodule.exports = pipeline;", "// Copyright Joyent, Inc. and other Node contributors.\n//\n// Permission is hereby granted, free of charge, to any person obtaining a\n// copy of this software and associated documentation files (the\n// \"Software\"), to deal in the Software without restriction, including\n// without limitation the rights to use, copy, modify, merge, publish,\n// distribute, sublicense, and/or sell copies of the Software, and to permit\n// persons to whom the Software is furnished to do so, subject to the\n// following conditions:\n//\n// The above copyright notice and this permission notice shall be included\n// in all copies or substantial portions of the Software.\n//\n// THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS\n// OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\n// MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN\n// NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM,\n// DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR\n// OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE\n// USE OR OTHER DEALINGS IN THE SOFTWARE.\n\nmodule.exports = Stream;\n\nvar EE = require('events').EventEmitter;\nvar inherits = require('inherits');\n\ninherits(Stream, EE);\nStream.Readable = require('readable-stream/lib/_stream_readable.js');\nStream.Writable = require('readable-stream/lib/_stream_writable.js');\nStream.Duplex = require('readable-stream/lib/_stream_duplex.js');\nStream.Transform = require('readable-stream/lib/_stream_transform.js');\nStream.PassThrough = require('readable-stream/lib/_stream_passthrough.js');\nStream.finished = require('readable-stream/lib/internal/streams/end-of-stream.js')\nStream.pipeline = require('readable-stream/lib/internal/streams/pipeline.js')\n\n// Backwards-compat with node 0.4.x\nStream.Stream = Stream;\n\n\n\n// old-style streams.  Note that the pipe method (the only relevant\n// part of this class) is overridden in the Readable class.\n\nfunction Stream() {\n  EE.call(this);\n}\n\nStream.prototype.pipe = function(dest, options) {\n  var source = this;\n\n  function ondata(chunk) {\n    if (dest.writable) {\n      if (false === dest.write(chunk) && source.pause) {\n        source.pause();\n      }\n    }\n  }\n\n  source.on('data', ondata);\n\n  function ondrain() {\n    if (source.readable && source.resume) {\n      source.resume();\n    }\n  }\n\n  dest.on('drain', ondrain);\n\n  // If the 'end' option is not supplied, dest.end() will be called when\n  // source gets the 'end' or 'close' events.  Only dest.end() once.\n  if (!dest._isStdio && (!options || options.end !== false)) {\n    source.on('end', onend);\n    source.on('close', onclose);\n  }\n\n  var didOnEnd = false;\n  function onend() {\n    if (didOnEnd) return;\n    didOnEnd = true;\n\n    dest.end();\n  }\n\n\n  function onclose() {\n    if (didOnEnd) return;\n    didOnEnd = true;\n\n    if (typeof dest.destroy === 'function') dest.destroy();\n  }\n\n  // don't leave dangling pipes when there are errors.\n  function onerror(er) {\n    cleanup();\n    if (EE.listenerCount(this, 'error') === 0) {\n      throw er; // Unhandled stream error in pipe.\n    }\n  }\n\n  source.on('error', onerror);\n  dest.on('error', onerror);\n\n  // remove all the event listeners that were added.\n  function cleanup() {\n    source.removeListener('data', ondata);\n    dest.removeListener('drain', ondrain);\n\n    source.removeListener('end', onend);\n    source.removeListener('close', onclose);\n\n    source.removeListener('error', onerror);\n    dest.removeListener('error', onerror);\n\n    source.removeListener('end', cleanup);\n    source.removeListener('close', cleanup);\n\n    dest.removeListener('close', cleanup);\n  }\n\n  source.on('end', cleanup);\n  source.on('close', cleanup);\n\n  dest.on('close', cleanup);\n\n  dest.emit('pipe', source);\n\n  // Allow for unix-like usage: A.pipe(B).pipe(C)\n  return dest;\n};\n", "export class AbortSignal {\n    constructor() {\n        this.onabort = null;\n        this._aborted = false;\n        Object.defineProperty(this, \"_aborted\", {\n            value: false,\n            writable: true,\n        });\n    }\n    get aborted() {\n        return this._aborted;\n    }\n    abort() {\n        this._aborted = true;\n        if (this.onabort) {\n            this.onabort(this);\n            this.onabort = null;\n        }\n    }\n}\n", "import { AbortSignal } from \"./AbortSignal\";\nexport class AbortController {\n    constructor() {\n        this.signal = new AbortSignal();\n    }\n    abort() {\n        this.signal.abort();\n    }\n}\n", "import { AbortMultipartUploadCommand, ChecksumAlgorithm, CompleteMultipartUploadCommand, CreateMultipartUploadCommand, PutObjectCommand, PutObjectTaggingCommand, UploadPartCommand, } from \"@aws-sdk/client-s3\";\nimport { AbortController } from \"@smithy/abort-controller\";\nimport { getEndpointFromInstructions, toEndpointV1, } from \"@smithy/middleware-endpoint\";\nimport { extendedEncodeURIComponent } from \"@smithy/smithy-client\";\nimport { EventEmitter } from \"events\";\nimport { byteLength } from \"./bytelength\";\nimport { getChunk } from \"./chunker\";\nexport class Upload extends EventEmitter {\n    static MIN_PART_SIZE = 1024 * 1024 * 5;\n    MAX_PARTS = 10000;\n    queueSize = 4;\n    partSize = Upload.MIN_PART_SIZE;\n    leavePartsOnError = false;\n    tags = [];\n    client;\n    params;\n    totalBytes;\n    bytesUploadedSoFar;\n    abortController;\n    concurrentUploaders = [];\n    createMultiPartPromise;\n    abortMultipartUploadCommand = null;\n    uploadedParts = [];\n    uploadEnqueuedPartsCount = 0;\n    uploadId;\n    uploadEvent;\n    isMultiPart = true;\n    singleUploadResult;\n    sent = false;\n    constructor(options) {\n        super();\n        this.queueSize = options.queueSize || this.queueSize;\n        this.partSize = options.partSize || this.partSize;\n        this.leavePartsOnError = options.leavePartsOnError || this.leavePartsOnError;\n        this.tags = options.tags || this.tags;\n        this.client = options.client;\n        this.params = options.params;\n        this.__validateInput();\n        this.totalBytes = byteLength(this.params.Body);\n        this.bytesUploadedSoFar = 0;\n        this.abortController = options.abortController ?? new AbortController();\n    }\n    async abort() {\n        this.abortController.abort();\n    }\n    async done() {\n        if (this.sent) {\n            throw new Error(\"@aws-sdk/lib-storage: this instance of Upload has already executed .done(). Create a new instance.\");\n        }\n        this.sent = true;\n        return await Promise.race([this.__doMultipartUpload(), this.__abortTimeout(this.abortController.signal)]);\n    }\n    on(event, listener) {\n        this.uploadEvent = event;\n        return super.on(event, listener);\n    }\n    async __uploadUsingPut(dataPart) {\n        this.isMultiPart = false;\n        const params = { ...this.params, Body: dataPart.data };\n        const clientConfig = this.client.config;\n        const requestHandler = clientConfig.requestHandler;\n        const eventEmitter = requestHandler instanceof EventEmitter ? requestHandler : null;\n        const uploadEventListener = (event) => {\n            this.bytesUploadedSoFar = event.loaded;\n            this.totalBytes = event.total;\n            this.__notifyProgress({\n                loaded: this.bytesUploadedSoFar,\n                total: this.totalBytes,\n                part: dataPart.partNumber,\n                Key: this.params.Key,\n                Bucket: this.params.Bucket,\n            });\n        };\n        if (eventEmitter !== null) {\n            eventEmitter.on(\"xhr.upload.progress\", uploadEventListener);\n        }\n        const resolved = await Promise.all([this.client.send(new PutObjectCommand(params)), clientConfig?.endpoint?.()]);\n        const putResult = resolved[0];\n        let endpoint = resolved[1];\n        if (!endpoint) {\n            endpoint = toEndpointV1(await getEndpointFromInstructions(params, PutObjectCommand, {\n                ...clientConfig,\n            }));\n        }\n        if (!endpoint) {\n            throw new Error('Could not resolve endpoint from S3 \"client.config.endpoint()\" nor EndpointsV2.');\n        }\n        if (eventEmitter !== null) {\n            eventEmitter.off(\"xhr.upload.progress\", uploadEventListener);\n        }\n        const locationKey = this.params\n            .Key.split(\"/\")\n            .map((segment) => extendedEncodeURIComponent(segment))\n            .join(\"/\");\n        const locationBucket = extendedEncodeURIComponent(this.params.Bucket);\n        const Location = (() => {\n            const endpointHostnameIncludesBucket = endpoint.hostname.startsWith(`${locationBucket}.`);\n            const forcePathStyle = this.client.config.forcePathStyle;\n            const optionalPort = endpoint.port ? `:${endpoint.port}` : ``;\n            if (forcePathStyle) {\n                return `${endpoint.protocol}//${endpoint.hostname}${optionalPort}/${locationBucket}/${locationKey}`;\n            }\n            if (endpointHostnameIncludesBucket) {\n                return `${endpoint.protocol}//${endpoint.hostname}${optionalPort}/${locationKey}`;\n            }\n            return `${endpoint.protocol}//${locationBucket}.${endpoint.hostname}${optionalPort}/${locationKey}`;\n        })();\n        this.singleUploadResult = {\n            ...putResult,\n            Bucket: this.params.Bucket,\n            Key: this.params.Key,\n            Location,\n        };\n        const totalSize = byteLength(dataPart.data);\n        this.__notifyProgress({\n            loaded: totalSize,\n            total: totalSize,\n            part: 1,\n            Key: this.params.Key,\n            Bucket: this.params.Bucket,\n        });\n    }\n    async __createMultipartUpload() {\n        const requestChecksumCalculation = await this.client.config.requestChecksumCalculation();\n        if (!this.createMultiPartPromise) {\n            const createCommandParams = { ...this.params, Body: undefined };\n            if (requestChecksumCalculation === \"WHEN_SUPPORTED\") {\n                createCommandParams.ChecksumAlgorithm = this.params.ChecksumAlgorithm || ChecksumAlgorithm.CRC32;\n            }\n            this.createMultiPartPromise = this.client\n                .send(new CreateMultipartUploadCommand(createCommandParams))\n                .then((createMpuResponse) => {\n                this.abortMultipartUploadCommand = new AbortMultipartUploadCommand({\n                    Bucket: this.params.Bucket,\n                    Key: this.params.Key,\n                    UploadId: createMpuResponse.UploadId,\n                });\n                return createMpuResponse;\n            });\n        }\n        return this.createMultiPartPromise;\n    }\n    async __doConcurrentUpload(dataFeeder) {\n        for await (const dataPart of dataFeeder) {\n            if (this.uploadEnqueuedPartsCount > this.MAX_PARTS) {\n                throw new Error(`Exceeded ${this.MAX_PARTS} parts in multipart upload to Bucket: ${this.params.Bucket} Key: ${this.params.Key}.`);\n            }\n            if (this.abortController.signal.aborted) {\n                return;\n            }\n            if (dataPart.partNumber === 1 && dataPart.lastPart) {\n                return await this.__uploadUsingPut(dataPart);\n            }\n            if (!this.uploadId) {\n                const { UploadId } = await this.__createMultipartUpload();\n                this.uploadId = UploadId;\n                if (this.abortController.signal.aborted) {\n                    return;\n                }\n            }\n            const partSize = byteLength(dataPart.data) || 0;\n            const requestHandler = this.client.config.requestHandler;\n            const eventEmitter = requestHandler instanceof EventEmitter ? requestHandler : null;\n            let lastSeenBytes = 0;\n            const uploadEventListener = (event, request) => {\n                const requestPartSize = Number(request.query[\"partNumber\"]) || -1;\n                if (requestPartSize !== dataPart.partNumber) {\n                    return;\n                }\n                if (event.total && partSize) {\n                    this.bytesUploadedSoFar += event.loaded - lastSeenBytes;\n                    lastSeenBytes = event.loaded;\n                }\n                this.__notifyProgress({\n                    loaded: this.bytesUploadedSoFar,\n                    total: this.totalBytes,\n                    part: dataPart.partNumber,\n                    Key: this.params.Key,\n                    Bucket: this.params.Bucket,\n                });\n            };\n            if (eventEmitter !== null) {\n                eventEmitter.on(\"xhr.upload.progress\", uploadEventListener);\n            }\n            this.uploadEnqueuedPartsCount += 1;\n            const partResult = await this.client.send(new UploadPartCommand({\n                ...this.params,\n                ContentLength: undefined,\n                UploadId: this.uploadId,\n                Body: dataPart.data,\n                PartNumber: dataPart.partNumber,\n            }));\n            if (eventEmitter !== null) {\n                eventEmitter.off(\"xhr.upload.progress\", uploadEventListener);\n            }\n            if (this.abortController.signal.aborted) {\n                return;\n            }\n            if (!partResult.ETag) {\n                throw new Error(`Part ${dataPart.partNumber} is missing ETag in UploadPart response. Missing Bucket CORS configuration for ETag header?`);\n            }\n            this.uploadedParts.push({\n                PartNumber: dataPart.partNumber,\n                ETag: partResult.ETag,\n                ...(partResult.ChecksumCRC32 && { ChecksumCRC32: partResult.ChecksumCRC32 }),\n                ...(partResult.ChecksumCRC32C && { ChecksumCRC32C: partResult.ChecksumCRC32C }),\n                ...(partResult.ChecksumSHA1 && { ChecksumSHA1: partResult.ChecksumSHA1 }),\n                ...(partResult.ChecksumSHA256 && { ChecksumSHA256: partResult.ChecksumSHA256 }),\n            });\n            if (eventEmitter === null) {\n                this.bytesUploadedSoFar += partSize;\n            }\n            this.__notifyProgress({\n                loaded: this.bytesUploadedSoFar,\n                total: this.totalBytes,\n                part: dataPart.partNumber,\n                Key: this.params.Key,\n                Bucket: this.params.Bucket,\n            });\n        }\n    }\n    async __doMultipartUpload() {\n        const dataFeeder = getChunk(this.params.Body, this.partSize);\n        const concurrentUploaderFailures = [];\n        for (let index = 0; index < this.queueSize; index++) {\n            const currentUpload = this.__doConcurrentUpload(dataFeeder).catch((err) => {\n                concurrentUploaderFailures.push(err);\n            });\n            this.concurrentUploaders.push(currentUpload);\n        }\n        await Promise.all(this.concurrentUploaders);\n        if (concurrentUploaderFailures.length >= 1) {\n            await this.markUploadAsAborted();\n            throw concurrentUploaderFailures[0];\n        }\n        if (this.abortController.signal.aborted) {\n            await this.markUploadAsAborted();\n            throw Object.assign(new Error(\"Upload aborted.\"), { name: \"AbortError\" });\n        }\n        let result;\n        if (this.isMultiPart) {\n            this.uploadedParts.sort((a, b) => a.PartNumber - b.PartNumber);\n            const uploadCompleteParams = {\n                ...this.params,\n                Body: undefined,\n                UploadId: this.uploadId,\n                MultipartUpload: {\n                    Parts: this.uploadedParts,\n                },\n            };\n            result = await this.client.send(new CompleteMultipartUploadCommand(uploadCompleteParams));\n            if (typeof result?.Location === \"string\" && result.Location.includes(\"%2F\")) {\n                result.Location = result.Location.replace(/%2F/g, \"/\");\n            }\n        }\n        else {\n            result = this.singleUploadResult;\n        }\n        this.abortMultipartUploadCommand = null;\n        if (this.tags.length) {\n            await this.client.send(new PutObjectTaggingCommand({\n                ...this.params,\n                Tagging: {\n                    TagSet: this.tags,\n                },\n            }));\n        }\n        return result;\n    }\n    async markUploadAsAborted() {\n        if (this.uploadId && !this.leavePartsOnError && null !== this.abortMultipartUploadCommand) {\n            await this.client.send(this.abortMultipartUploadCommand);\n            this.abortMultipartUploadCommand = null;\n        }\n    }\n    __notifyProgress(progress) {\n        if (this.uploadEvent) {\n            this.emit(this.uploadEvent, progress);\n        }\n    }\n    async __abortTimeout(abortSignal) {\n        return new Promise((resolve, reject) => {\n            abortSignal.onabort = () => {\n                const abortError = new Error(\"Upload aborted.\");\n                abortError.name = \"AbortError\";\n                reject(abortError);\n            };\n        });\n    }\n    __validateInput() {\n        if (!this.params) {\n            throw new Error(`InputError: Upload requires params to be passed to upload.`);\n        }\n        if (!this.client) {\n            throw new Error(`InputError: Upload requires a AWS client to do uploads with.`);\n        }\n        if (this.partSize < Upload.MIN_PART_SIZE) {\n            throw new Error(`EntityTooSmall: Your proposed upload partsize [${this.partSize}] is smaller than the minimum allowed size [${Upload.MIN_PART_SIZE}] (5MB)`);\n        }\n        if (this.queueSize < 1) {\n            throw new Error(`Queue size: Must have at least one uploading queue.`);\n        }\n    }\n}\n", "import { <PERSON>uffer } from \"buffer\";\nimport { ClientDefaultValues } from \"./runtimeConfig\";\nexport const byteLength = (input) => {\n    if (input === null || input === undefined)\n        return 0;\n    if (typeof input === \"string\") {\n        return Buffer.byteLength(input);\n    }\n    if (typeof input.byteLength === \"number\") {\n        return input.byteLength;\n    }\n    else if (typeof input.length === \"number\") {\n        return input.length;\n    }\n    else if (typeof input.size === \"number\") {\n        return input.size;\n    }\n    else if (typeof input.path === \"string\") {\n        try {\n            return ClientDefaultValues.lstatSync(input.path).size;\n        }\n        catch (error) {\n            return undefined;\n        }\n    }\n    return undefined;\n};\n", "export const ClientSharedValues = {\n    lstatSync: () => { },\n};\n", "import { ClientSharedValues } from \"./runtimeConfig.shared\";\nexport const ClientDefaultValues = {\n    ...ClientSharedValues,\n    runtime: \"browser\",\n};\n", "import { <PERSON><PERSON><PERSON> } from \"buffer\";\nimport { Readable } from \"stream\";\nimport { getChunkStream } from \"./chunks/getChunkStream\";\nimport { getChunkUint8Array } from \"./chunks/getChunkUint8Array\";\nimport { getDataReadable } from \"./chunks/getDataReadable\";\nimport { getDataReadableStream } from \"./chunks/getDataReadableStream\";\nexport const getChunk = (data, partSize) => {\n    if (data instanceof Uint8Array) {\n        return getChunkUint8Array(data, partSize);\n    }\n    if (data instanceof Readable) {\n        return getChunkStream(data, partSize, getDataReadable);\n    }\n    if (data instanceof String || typeof data === \"string\") {\n        return getChunkUint8Array(Buffer.from(data), partSize);\n    }\n    if (typeof data.stream === \"function\") {\n        return getChunkStream(data.stream(), partSize, getDataReadableStream);\n    }\n    if (data instanceof ReadableStream) {\n        return getChunkStream(data, partSize, getDataReadableStream);\n    }\n    throw new Error(\"Body Data is unsupported format, expected data to be one of: string | Uint8Array | Buffer | Readable | ReadableStream | Blob;.\");\n};\n", "import { Buffer } from \"buffer\";\nexport async function* getChunkStream(data, partSize, getNextData) {\n    let partNumber = 1;\n    const currentBuffer = { chunks: [], length: 0 };\n    for await (const datum of getNextData(data)) {\n        currentBuffer.chunks.push(datum);\n        currentBuffer.length += datum.byteLength;\n        while (currentBuffer.length > partSize) {\n            const dataChunk = currentBuffer.chunks.length > 1 ? Buffer.concat(currentBuffer.chunks) : currentBuffer.chunks[0];\n            yield {\n                partNumber,\n                data: dataChunk.subarray(0, partSize),\n            };\n            currentBuffer.chunks = [dataChunk.subarray(partSize)];\n            currentBuffer.length = currentBuffer.chunks[0].byteLength;\n            partNumber += 1;\n        }\n    }\n    yield {\n        partNumber,\n        data: currentBuffer.chunks.length !== 1 ? Buffer.concat(currentBuffer.chunks) : currentBuffer.chunks[0],\n        lastPart: true,\n    };\n}\n", "export async function* getChunkUint8Array(data, partSize) {\n    let partNumber = 1;\n    let startByte = 0;\n    let endByte = partSize;\n    while (endByte < data.byteLength) {\n        yield {\n            partNumber,\n            data: data.subarray(startByte, endByte),\n        };\n        partNumber += 1;\n        startByte = endByte;\n        endByte = startByte + partSize;\n    }\n    yield {\n        partNumber,\n        data: data.subarray(startByte),\n        lastPart: true,\n    };\n}\n", "import { Buffer } from \"buffer\";\nexport async function* getDataReadable(data) {\n    for await (const chunk of data) {\n        if (Buffer.isBuffer(chunk) || chunk instanceof Uint8Array) {\n            yield chunk;\n        }\n        else {\n            yield Buffer.from(chunk);\n        }\n    }\n}\n", "import { Buffer } from \"buffer\";\nexport async function* getDataReadableStream(data) {\n    const reader = data.getReader();\n    try {\n        while (true) {\n            const { done, value } = await reader.read();\n            if (done) {\n                return;\n            }\n            if (Buffer.isBuffer(value) || value instanceof Uint8Array) {\n                yield value;\n            }\n            else {\n                yield Buffer.from(value);\n            }\n        }\n    }\n    catch (e) {\n        throw e;\n    }\n    finally {\n        reader.releaseLock();\n    }\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAuBA,QAAI,IAAI,OAAO,YAAY,WAAW,UAAU;AAChD,QAAI,eAAe,KAAK,OAAO,EAAE,UAAU,aACvC,EAAE,QACF,SAASA,cAAa,QAAQ,UAAU,MAAM;AAC9C,aAAO,SAAS,UAAU,MAAM,KAAK,QAAQ,UAAU,IAAI;AAAA,IAC7D;AAEF,QAAI;AACJ,QAAI,KAAK,OAAO,EAAE,YAAY,YAAY;AACxC,uBAAiB,EAAE;AAAA,IACrB,WAAW,OAAO,uBAAuB;AACvC,uBAAiB,SAASC,gBAAe,QAAQ;AAC/C,eAAO,OAAO,oBAAoB,MAAM,EACrC,OAAO,OAAO,sBAAsB,MAAM,CAAC;AAAA,MAChD;AAAA,IACF,OAAO;AACL,uBAAiB,SAASA,gBAAe,QAAQ;AAC/C,eAAO,OAAO,oBAAoB,MAAM;AAAA,MAC1C;AAAA,IACF;AAEA,aAAS,mBAAmB,SAAS;AACnC,UAAI,WAAW,QAAQ,KAAM,SAAQ,KAAK,OAAO;AAAA,IACnD;AAEA,QAAI,cAAc,OAAO,SAAS,SAASC,aAAY,OAAO;AAC5D,aAAO,UAAU;AAAA,IACnB;AAEA,aAASC,gBAAe;AACtB,MAAAA,cAAa,KAAK,KAAK,IAAI;AAAA,IAC7B;AACA,WAAO,UAAUA;AACjB,WAAO,QAAQ,OAAO;AAGtB,IAAAA,cAAa,eAAeA;AAE5B,IAAAA,cAAa,UAAU,UAAU;AACjC,IAAAA,cAAa,UAAU,eAAe;AACtC,IAAAA,cAAa,UAAU,gBAAgB;AAIvC,QAAI,sBAAsB;AAE1B,aAAS,cAAc,UAAU;AAC/B,UAAI,OAAO,aAAa,YAAY;AAClC,cAAM,IAAI,UAAU,qEAAqE,OAAO,QAAQ;AAAA,MAC1G;AAAA,IACF;AAEA,WAAO,eAAeA,eAAc,uBAAuB;AAAA,MACzD,YAAY;AAAA,MACZ,KAAK,WAAW;AACd,eAAO;AAAA,MACT;AAAA,MACA,KAAK,SAAS,KAAK;AACjB,YAAI,OAAO,QAAQ,YAAY,MAAM,KAAK,YAAY,GAAG,GAAG;AAC1D,gBAAM,IAAI,WAAW,oGAAoG,MAAM,GAAG;AAAA,QACpI;AACA,8BAAsB;AAAA,MACxB;AAAA,IACF,CAAC;AAED,IAAAA,cAAa,OAAO,WAAW;AAE7B,UAAI,KAAK,YAAY,UACjB,KAAK,YAAY,OAAO,eAAe,IAAI,EAAE,SAAS;AACxD,aAAK,UAAU,uBAAO,OAAO,IAAI;AACjC,aAAK,eAAe;AAAA,MACtB;AAEA,WAAK,gBAAgB,KAAK,iBAAiB;AAAA,IAC7C;AAIA,IAAAA,cAAa,UAAU,kBAAkB,SAAS,gBAAgB,GAAG;AACnE,UAAI,OAAO,MAAM,YAAY,IAAI,KAAK,YAAY,CAAC,GAAG;AACpD,cAAM,IAAI,WAAW,kFAAkF,IAAI,GAAG;AAAA,MAChH;AACA,WAAK,gBAAgB;AACrB,aAAO;AAAA,IACT;AAEA,aAAS,iBAAiB,MAAM;AAC9B,UAAI,KAAK,kBAAkB;AACzB,eAAOA,cAAa;AACtB,aAAO,KAAK;AAAA,IACd;AAEA,IAAAA,cAAa,UAAU,kBAAkB,SAAS,kBAAkB;AAClE,aAAO,iBAAiB,IAAI;AAAA,IAC9B;AAEA,IAAAA,cAAa,UAAU,OAAO,SAAS,KAAK,MAAM;AAChD,UAAI,OAAO,CAAC;AACZ,eAAS,IAAI,GAAG,IAAI,UAAU,QAAQ,IAAK,MAAK,KAAK,UAAU,CAAC,CAAC;AACjE,UAAI,UAAW,SAAS;AAExB,UAAI,SAAS,KAAK;AAClB,UAAI,WAAW;AACb,kBAAW,WAAW,OAAO,UAAU;AAAA,eAChC,CAAC;AACR,eAAO;AAGT,UAAI,SAAS;AACX,YAAI;AACJ,YAAI,KAAK,SAAS;AAChB,eAAK,KAAK,CAAC;AACb,YAAI,cAAc,OAAO;AAGvB,gBAAM;AAAA,QACR;AAEA,YAAI,MAAM,IAAI,MAAM,sBAAsB,KAAK,OAAO,GAAG,UAAU,MAAM,GAAG;AAC5E,YAAI,UAAU;AACd,cAAM;AAAA,MACR;AAEA,UAAI,UAAU,OAAO,IAAI;AAEzB,UAAI,YAAY;AACd,eAAO;AAET,UAAI,OAAO,YAAY,YAAY;AACjC,qBAAa,SAAS,MAAM,IAAI;AAAA,MAClC,OAAO;AACL,YAAI,MAAM,QAAQ;AAClB,YAAI,YAAY,WAAW,SAAS,GAAG;AACvC,iBAAS,IAAI,GAAG,IAAI,KAAK,EAAE;AACzB,uBAAa,UAAU,CAAC,GAAG,MAAM,IAAI;AAAA,MACzC;AAEA,aAAO;AAAA,IACT;AAEA,aAAS,aAAa,QAAQ,MAAM,UAAU,SAAS;AACrD,UAAI;AACJ,UAAI;AACJ,UAAI;AAEJ,oBAAc,QAAQ;AAEtB,eAAS,OAAO;AAChB,UAAI,WAAW,QAAW;AACxB,iBAAS,OAAO,UAAU,uBAAO,OAAO,IAAI;AAC5C,eAAO,eAAe;AAAA,MACxB,OAAO;AAGL,YAAI,OAAO,gBAAgB,QAAW;AACpC,iBAAO;AAAA,YAAK;AAAA,YAAe;AAAA,YACf,SAAS,WAAW,SAAS,WAAW;AAAA,UAAQ;AAI5D,mBAAS,OAAO;AAAA,QAClB;AACA,mBAAW,OAAO,IAAI;AAAA,MACxB;AAEA,UAAI,aAAa,QAAW;AAE1B,mBAAW,OAAO,IAAI,IAAI;AAC1B,UAAE,OAAO;AAAA,MACX,OAAO;AACL,YAAI,OAAO,aAAa,YAAY;AAElC,qBAAW,OAAO,IAAI,IACpB,UAAU,CAAC,UAAU,QAAQ,IAAI,CAAC,UAAU,QAAQ;AAAA,QAExD,WAAW,SAAS;AAClB,mBAAS,QAAQ,QAAQ;AAAA,QAC3B,OAAO;AACL,mBAAS,KAAK,QAAQ;AAAA,QACxB;AAGA,YAAI,iBAAiB,MAAM;AAC3B,YAAI,IAAI,KAAK,SAAS,SAAS,KAAK,CAAC,SAAS,QAAQ;AACpD,mBAAS,SAAS;AAGlB,cAAI,IAAI,IAAI,MAAM,iDACE,SAAS,SAAS,MAAM,OAAO,IAAI,IAAI,mEAEvB;AACpC,YAAE,OAAO;AACT,YAAE,UAAU;AACZ,YAAE,OAAO;AACT,YAAE,QAAQ,SAAS;AACnB,6BAAmB,CAAC;AAAA,QACtB;AAAA,MACF;AAEA,aAAO;AAAA,IACT;AAEA,IAAAA,cAAa,UAAU,cAAc,SAAS,YAAY,MAAM,UAAU;AACxE,aAAO,aAAa,MAAM,MAAM,UAAU,KAAK;AAAA,IACjD;AAEA,IAAAA,cAAa,UAAU,KAAKA,cAAa,UAAU;AAEnD,IAAAA,cAAa,UAAU,kBACnB,SAAS,gBAAgB,MAAM,UAAU;AACvC,aAAO,aAAa,MAAM,MAAM,UAAU,IAAI;AAAA,IAChD;AAEJ,aAAS,cAAc;AACrB,UAAI,CAAC,KAAK,OAAO;AACf,aAAK,OAAO,eAAe,KAAK,MAAM,KAAK,MAAM;AACjD,aAAK,QAAQ;AACb,YAAI,UAAU,WAAW;AACvB,iBAAO,KAAK,SAAS,KAAK,KAAK,MAAM;AACvC,eAAO,KAAK,SAAS,MAAM,KAAK,QAAQ,SAAS;AAAA,MACnD;AAAA,IACF;AAEA,aAAS,UAAU,QAAQ,MAAM,UAAU;AACzC,UAAI,QAAQ,EAAE,OAAO,OAAO,QAAQ,QAAW,QAAgB,MAAY,SAAmB;AAC9F,UAAI,UAAU,YAAY,KAAK,KAAK;AACpC,cAAQ,WAAW;AACnB,YAAM,SAAS;AACf,aAAO;AAAA,IACT;AAEA,IAAAA,cAAa,UAAU,OAAO,SAASC,MAAK,MAAM,UAAU;AAC1D,oBAAc,QAAQ;AACtB,WAAK,GAAG,MAAM,UAAU,MAAM,MAAM,QAAQ,CAAC;AAC7C,aAAO;AAAA,IACT;AAEA,IAAAD,cAAa,UAAU,sBACnB,SAAS,oBAAoB,MAAM,UAAU;AAC3C,oBAAc,QAAQ;AACtB,WAAK,gBAAgB,MAAM,UAAU,MAAM,MAAM,QAAQ,CAAC;AAC1D,aAAO;AAAA,IACT;AAGJ,IAAAA,cAAa,UAAU,iBACnB,SAAS,eAAe,MAAM,UAAU;AACtC,UAAI,MAAM,QAAQ,UAAU,GAAG;AAE/B,oBAAc,QAAQ;AAEtB,eAAS,KAAK;AACd,UAAI,WAAW;AACb,eAAO;AAET,aAAO,OAAO,IAAI;AAClB,UAAI,SAAS;AACX,eAAO;AAET,UAAI,SAAS,YAAY,KAAK,aAAa,UAAU;AACnD,YAAI,EAAE,KAAK,iBAAiB;AAC1B,eAAK,UAAU,uBAAO,OAAO,IAAI;AAAA,aAC9B;AACH,iBAAO,OAAO,IAAI;AAClB,cAAI,OAAO;AACT,iBAAK,KAAK,kBAAkB,MAAM,KAAK,YAAY,QAAQ;AAAA,QAC/D;AAAA,MACF,WAAW,OAAO,SAAS,YAAY;AACrC,mBAAW;AAEX,aAAK,IAAI,KAAK,SAAS,GAAG,KAAK,GAAG,KAAK;AACrC,cAAI,KAAK,CAAC,MAAM,YAAY,KAAK,CAAC,EAAE,aAAa,UAAU;AACzD,+BAAmB,KAAK,CAAC,EAAE;AAC3B,uBAAW;AACX;AAAA,UACF;AAAA,QACF;AAEA,YAAI,WAAW;AACb,iBAAO;AAET,YAAI,aAAa;AACf,eAAK,MAAM;AAAA,aACR;AACH,oBAAU,MAAM,QAAQ;AAAA,QAC1B;AAEA,YAAI,KAAK,WAAW;AAClB,iBAAO,IAAI,IAAI,KAAK,CAAC;AAEvB,YAAI,OAAO,mBAAmB;AAC5B,eAAK,KAAK,kBAAkB,MAAM,oBAAoB,QAAQ;AAAA,MAClE;AAEA,aAAO;AAAA,IACT;AAEJ,IAAAA,cAAa,UAAU,MAAMA,cAAa,UAAU;AAEpD,IAAAA,cAAa,UAAU,qBACnB,SAAS,mBAAmB,MAAM;AAChC,UAAI,WAAW,QAAQ;AAEvB,eAAS,KAAK;AACd,UAAI,WAAW;AACb,eAAO;AAGT,UAAI,OAAO,mBAAmB,QAAW;AACvC,YAAI,UAAU,WAAW,GAAG;AAC1B,eAAK,UAAU,uBAAO,OAAO,IAAI;AACjC,eAAK,eAAe;AAAA,QACtB,WAAW,OAAO,IAAI,MAAM,QAAW;AACrC,cAAI,EAAE,KAAK,iBAAiB;AAC1B,iBAAK,UAAU,uBAAO,OAAO,IAAI;AAAA;AAEjC,mBAAO,OAAO,IAAI;AAAA,QACtB;AACA,eAAO;AAAA,MACT;AAGA,UAAI,UAAU,WAAW,GAAG;AAC1B,YAAI,OAAO,OAAO,KAAK,MAAM;AAC7B,YAAI;AACJ,aAAK,IAAI,GAAG,IAAI,KAAK,QAAQ,EAAE,GAAG;AAChC,gBAAM,KAAK,CAAC;AACZ,cAAI,QAAQ,iBAAkB;AAC9B,eAAK,mBAAmB,GAAG;AAAA,QAC7B;AACA,aAAK,mBAAmB,gBAAgB;AACxC,aAAK,UAAU,uBAAO,OAAO,IAAI;AACjC,aAAK,eAAe;AACpB,eAAO;AAAA,MACT;AAEA,kBAAY,OAAO,IAAI;AAEvB,UAAI,OAAO,cAAc,YAAY;AACnC,aAAK,eAAe,MAAM,SAAS;AAAA,MACrC,WAAW,cAAc,QAAW;AAElC,aAAK,IAAI,UAAU,SAAS,GAAG,KAAK,GAAG,KAAK;AAC1C,eAAK,eAAe,MAAM,UAAU,CAAC,CAAC;AAAA,QACxC;AAAA,MACF;AAEA,aAAO;AAAA,IACT;AAEJ,aAAS,WAAW,QAAQ,MAAM,QAAQ;AACxC,UAAI,SAAS,OAAO;AAEpB,UAAI,WAAW;AACb,eAAO,CAAC;AAEV,UAAI,aAAa,OAAO,IAAI;AAC5B,UAAI,eAAe;AACjB,eAAO,CAAC;AAEV,UAAI,OAAO,eAAe;AACxB,eAAO,SAAS,CAAC,WAAW,YAAY,UAAU,IAAI,CAAC,UAAU;AAEnE,aAAO,SACL,gBAAgB,UAAU,IAAI,WAAW,YAAY,WAAW,MAAM;AAAA,IAC1E;AAEA,IAAAA,cAAa,UAAU,YAAY,SAAS,UAAU,MAAM;AAC1D,aAAO,WAAW,MAAM,MAAM,IAAI;AAAA,IACpC;AAEA,IAAAA,cAAa,UAAU,eAAe,SAAS,aAAa,MAAM;AAChE,aAAO,WAAW,MAAM,MAAM,KAAK;AAAA,IACrC;AAEA,IAAAA,cAAa,gBAAgB,SAAS,SAAS,MAAM;AACnD,UAAI,OAAO,QAAQ,kBAAkB,YAAY;AAC/C,eAAO,QAAQ,cAAc,IAAI;AAAA,MACnC,OAAO;AACL,eAAO,cAAc,KAAK,SAAS,IAAI;AAAA,MACzC;AAAA,IACF;AAEA,IAAAA,cAAa,UAAU,gBAAgB;AACvC,aAAS,cAAc,MAAM;AAC3B,UAAI,SAAS,KAAK;AAElB,UAAI,WAAW,QAAW;AACxB,YAAI,aAAa,OAAO,IAAI;AAE5B,YAAI,OAAO,eAAe,YAAY;AACpC,iBAAO;AAAA,QACT,WAAW,eAAe,QAAW;AACnC,iBAAO,WAAW;AAAA,QACpB;AAAA,MACF;AAEA,aAAO;AAAA,IACT;AAEA,IAAAA,cAAa,UAAU,aAAa,SAAS,aAAa;AACxD,aAAO,KAAK,eAAe,IAAI,eAAe,KAAK,OAAO,IAAI,CAAC;AAAA,IACjE;AAEA,aAAS,WAAW,KAAK,GAAG;AAC1B,UAAI,OAAO,IAAI,MAAM,CAAC;AACtB,eAAS,IAAI,GAAG,IAAI,GAAG,EAAE;AACvB,aAAK,CAAC,IAAI,IAAI,CAAC;AACjB,aAAO;AAAA,IACT;AAEA,aAAS,UAAU,MAAM,OAAO;AAC9B,aAAO,QAAQ,IAAI,KAAK,QAAQ;AAC9B,aAAK,KAAK,IAAI,KAAK,QAAQ,CAAC;AAC9B,WAAK,IAAI;AAAA,IACX;AAEA,aAAS,gBAAgB,KAAK;AAC5B,UAAI,MAAM,IAAI,MAAM,IAAI,MAAM;AAC9B,eAAS,IAAI,GAAG,IAAI,IAAI,QAAQ,EAAE,GAAG;AACnC,YAAI,CAAC,IAAI,IAAI,CAAC,EAAE,YAAY,IAAI,CAAC;AAAA,MACnC;AACA,aAAO;AAAA,IACT;AAEA,aAAS,KAAK,SAAS,MAAM;AAC3B,aAAO,IAAI,QAAQ,SAAU,SAAS,QAAQ;AAC5C,iBAAS,cAAc,KAAK;AAC1B,kBAAQ,eAAe,MAAM,QAAQ;AACrC,iBAAO,GAAG;AAAA,QACZ;AAEA,iBAAS,WAAW;AAClB,cAAI,OAAO,QAAQ,mBAAmB,YAAY;AAChD,oBAAQ,eAAe,SAAS,aAAa;AAAA,UAC/C;AACA,kBAAQ,CAAC,EAAE,MAAM,KAAK,SAAS,CAAC;AAAA,QAClC;AAAC;AAED,uCAA+B,SAAS,MAAM,UAAU,EAAE,MAAM,KAAK,CAAC;AACtE,YAAI,SAAS,SAAS;AACpB,wCAA8B,SAAS,eAAe,EAAE,MAAM,KAAK,CAAC;AAAA,QACtE;AAAA,MACF,CAAC;AAAA,IACH;AAEA,aAAS,8BAA8B,SAAS,SAAS,OAAO;AAC9D,UAAI,OAAO,QAAQ,OAAO,YAAY;AACpC,uCAA+B,SAAS,SAAS,SAAS,KAAK;AAAA,MACjE;AAAA,IACF;AAEA,aAAS,+BAA+B,SAAS,MAAM,UAAU,OAAO;AACtE,UAAI,OAAO,QAAQ,OAAO,YAAY;AACpC,YAAI,MAAM,MAAM;AACd,kBAAQ,KAAK,MAAM,QAAQ;AAAA,QAC7B,OAAO;AACL,kBAAQ,GAAG,MAAM,QAAQ;AAAA,QAC3B;AAAA,MACF,WAAW,OAAO,QAAQ,qBAAqB,YAAY;AAGzD,gBAAQ,iBAAiB,MAAM,SAAS,aAAa,KAAK;AAGxD,cAAI,MAAM,MAAM;AACd,oBAAQ,oBAAoB,MAAM,YAAY;AAAA,UAChD;AACA,mBAAS,GAAG;AAAA,QACd,CAAC;AAAA,MACH,OAAO;AACL,cAAM,IAAI,UAAU,wEAAwE,OAAO,OAAO;AAAA,MAC5G;AAAA,IACF;AAAA;AAAA;;;AChfA;AAAA;AAAA,WAAO,UAAU,iBAAkB;AAAA;AAAA;;;ACAnC;AAAA;AAAA,WAAO,UAAU,OAAO,OAAO,IAAI,MAAM,CAAC,GAAG;AAAA,MAC3C,IAAI,GAAG,KAAK;AACV,YACE,QAAQ,gBACR,QAAQ,eACR,QAAQ,iBACR,QAAQ,UACR;AACA,kBAAQ,KAAK,sFAAsF,GAAG,mIAAmI;AAAA,QAC3O;AAAA,MACF;AAAA,IACF,CAAC,CAAC;AAAA;AAAA;;;ACXF;AAAA;AAAA;AAEA,aAAS,QAAQ,QAAQ,gBAAgB;AAAE,UAAI,OAAO,OAAO,KAAK,MAAM;AAAG,UAAI,OAAO,uBAAuB;AAAE,YAAI,UAAU,OAAO,sBAAsB,MAAM;AAAG,2BAAmB,UAAU,QAAQ,OAAO,SAAU,KAAK;AAAE,iBAAO,OAAO,yBAAyB,QAAQ,GAAG,EAAE;AAAA,QAAY,CAAC,IAAI,KAAK,KAAK,MAAM,MAAM,OAAO;AAAA,MAAG;AAAE,aAAO;AAAA,IAAM;AACpV,aAAS,cAAc,QAAQ;AAAE,eAAS,IAAI,GAAG,IAAI,UAAU,QAAQ,KAAK;AAAE,YAAI,SAAS,QAAQ,UAAU,CAAC,IAAI,UAAU,CAAC,IAAI,CAAC;AAAG,YAAI,IAAI,QAAQ,OAAO,MAAM,GAAG,IAAE,EAAE,QAAQ,SAAU,KAAK;AAAE,0BAAgB,QAAQ,KAAK,OAAO,GAAG,CAAC;AAAA,QAAG,CAAC,IAAI,OAAO,4BAA4B,OAAO,iBAAiB,QAAQ,OAAO,0BAA0B,MAAM,CAAC,IAAI,QAAQ,OAAO,MAAM,CAAC,EAAE,QAAQ,SAAU,KAAK;AAAE,iBAAO,eAAe,QAAQ,KAAK,OAAO,yBAAyB,QAAQ,GAAG,CAAC;AAAA,QAAG,CAAC;AAAA,MAAG;AAAE,aAAO;AAAA,IAAQ;AACzf,aAAS,gBAAgB,KAAK,KAAK,OAAO;AAAE,YAAM,eAAe,GAAG;AAAG,UAAI,OAAO,KAAK;AAAE,eAAO,eAAe,KAAK,KAAK,EAAE,OAAc,YAAY,MAAM,cAAc,MAAM,UAAU,KAAK,CAAC;AAAA,MAAG,OAAO;AAAE,YAAI,GAAG,IAAI;AAAA,MAAO;AAAE,aAAO;AAAA,IAAK;AAC3O,aAAS,gBAAgB,UAAU,aAAa;AAAE,UAAI,EAAE,oBAAoB,cAAc;AAAE,cAAM,IAAI,UAAU,mCAAmC;AAAA,MAAG;AAAA,IAAE;AACxJ,aAAS,kBAAkB,QAAQ,OAAO;AAAE,eAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK;AAAE,YAAI,aAAa,MAAM,CAAC;AAAG,mBAAW,aAAa,WAAW,cAAc;AAAO,mBAAW,eAAe;AAAM,YAAI,WAAW,WAAY,YAAW,WAAW;AAAM,eAAO,eAAe,QAAQ,eAAe,WAAW,GAAG,GAAG,UAAU;AAAA,MAAG;AAAA,IAAE;AAC5U,aAAS,aAAa,aAAa,YAAY,aAAa;AAAE,UAAI,WAAY,mBAAkB,YAAY,WAAW,UAAU;AAAG,UAAI,YAAa,mBAAkB,aAAa,WAAW;AAAG,aAAO,eAAe,aAAa,aAAa,EAAE,UAAU,MAAM,CAAC;AAAG,aAAO;AAAA,IAAa;AAC5R,aAAS,eAAe,KAAK;AAAE,UAAI,MAAM,aAAa,KAAK,QAAQ;AAAG,aAAO,OAAO,QAAQ,WAAW,MAAM,OAAO,GAAG;AAAA,IAAG;AAC1H,aAAS,aAAa,OAAO,MAAM;AAAE,UAAI,OAAO,UAAU,YAAY,UAAU,KAAM,QAAO;AAAO,UAAI,OAAO,MAAM,OAAO,WAAW;AAAG,UAAI,SAAS,QAAW;AAAE,YAAI,MAAM,KAAK,KAAK,OAAO,QAAQ,SAAS;AAAG,YAAI,OAAO,QAAQ,SAAU,QAAO;AAAK,cAAM,IAAI,UAAU,8CAA8C;AAAA,MAAG;AAAE,cAAQ,SAAS,WAAW,SAAS,QAAQ,KAAK;AAAA,IAAG;AACxX,QAAI,WAAW;AAAf,QACEE,UAAS,SAAS;AACpB,QAAI,YAAY;AAAhB,QACE,UAAU,UAAU;AACtB,QAAI,SAAS,WAAW,QAAQ,UAAU;AAC1C,aAAS,WAAW,KAAK,QAAQ,QAAQ;AACvC,MAAAA,QAAO,UAAU,KAAK,KAAK,KAAK,QAAQ,MAAM;AAAA,IAChD;AACA,WAAO,UAAuB,WAAY;AACxC,eAAS,aAAa;AACpB,wBAAgB,MAAM,UAAU;AAChC,aAAK,OAAO;AACZ,aAAK,OAAO;AACZ,aAAK,SAAS;AAAA,MAChB;AACA,mBAAa,YAAY,CAAC;AAAA,QACxB,KAAK;AAAA,QACL,OAAO,SAAS,KAAK,GAAG;AACtB,cAAI,QAAQ;AAAA,YACV,MAAM;AAAA,YACN,MAAM;AAAA,UACR;AACA,cAAI,KAAK,SAAS,EAAG,MAAK,KAAK,OAAO;AAAA,cAAW,MAAK,OAAO;AAC7D,eAAK,OAAO;AACZ,YAAE,KAAK;AAAA,QACT;AAAA,MACF,GAAG;AAAA,QACD,KAAK;AAAA,QACL,OAAO,SAAS,QAAQ,GAAG;AACzB,cAAI,QAAQ;AAAA,YACV,MAAM;AAAA,YACN,MAAM,KAAK;AAAA,UACb;AACA,cAAI,KAAK,WAAW,EAAG,MAAK,OAAO;AACnC,eAAK,OAAO;AACZ,YAAE,KAAK;AAAA,QACT;AAAA,MACF,GAAG;AAAA,QACD,KAAK;AAAA,QACL,OAAO,SAAS,QAAQ;AACtB,cAAI,KAAK,WAAW,EAAG;AACvB,cAAI,MAAM,KAAK,KAAK;AACpB,cAAI,KAAK,WAAW,EAAG,MAAK,OAAO,KAAK,OAAO;AAAA,cAAU,MAAK,OAAO,KAAK,KAAK;AAC/E,YAAE,KAAK;AACP,iBAAO;AAAA,QACT;AAAA,MACF,GAAG;AAAA,QACD,KAAK;AAAA,QACL,OAAO,SAAS,QAAQ;AACtB,eAAK,OAAO,KAAK,OAAO;AACxB,eAAK,SAAS;AAAA,QAChB;AAAA,MACF,GAAG;AAAA,QACD,KAAK;AAAA,QACL,OAAO,SAAS,KAAK,GAAG;AACtB,cAAI,KAAK,WAAW,EAAG,QAAO;AAC9B,cAAI,IAAI,KAAK;AACb,cAAI,MAAM,KAAK,EAAE;AACjB,iBAAO,IAAI,EAAE,KAAM,QAAO,IAAI,EAAE;AAChC,iBAAO;AAAA,QACT;AAAA,MACF,GAAG;AAAA,QACD,KAAK;AAAA,QACL,OAAO,SAAS,OAAO,GAAG;AACxB,cAAI,KAAK,WAAW,EAAG,QAAOA,QAAO,MAAM,CAAC;AAC5C,cAAI,MAAMA,QAAO,YAAY,MAAM,CAAC;AACpC,cAAI,IAAI,KAAK;AACb,cAAI,IAAI;AACR,iBAAO,GAAG;AACR,uBAAW,EAAE,MAAM,KAAK,CAAC;AACzB,iBAAK,EAAE,KAAK;AACZ,gBAAI,EAAE;AAAA,UACR;AACA,iBAAO;AAAA,QACT;AAAA;AAAA,MAGF,GAAG;AAAA,QACD,KAAK;AAAA,QACL,OAAO,SAAS,QAAQ,GAAG,YAAY;AACrC,cAAI;AACJ,cAAI,IAAI,KAAK,KAAK,KAAK,QAAQ;AAE7B,kBAAM,KAAK,KAAK,KAAK,MAAM,GAAG,CAAC;AAC/B,iBAAK,KAAK,OAAO,KAAK,KAAK,KAAK,MAAM,CAAC;AAAA,UACzC,WAAW,MAAM,KAAK,KAAK,KAAK,QAAQ;AAEtC,kBAAM,KAAK,MAAM;AAAA,UACnB,OAAO;AAEL,kBAAM,aAAa,KAAK,WAAW,CAAC,IAAI,KAAK,WAAW,CAAC;AAAA,UAC3D;AACA,iBAAO;AAAA,QACT;AAAA,MACF,GAAG;AAAA,QACD,KAAK;AAAA,QACL,OAAO,SAAS,QAAQ;AACtB,iBAAO,KAAK,KAAK;AAAA,QACnB;AAAA;AAAA,MAGF,GAAG;AAAA,QACD,KAAK;AAAA,QACL,OAAO,SAAS,WAAW,GAAG;AAC5B,cAAI,IAAI,KAAK;AACb,cAAI,IAAI;AACR,cAAI,MAAM,EAAE;AACZ,eAAK,IAAI;AACT,iBAAO,IAAI,EAAE,MAAM;AACjB,gBAAI,MAAM,EAAE;AACZ,gBAAI,KAAK,IAAI,IAAI,SAAS,IAAI,SAAS;AACvC,gBAAI,OAAO,IAAI,OAAQ,QAAO;AAAA,gBAAS,QAAO,IAAI,MAAM,GAAG,CAAC;AAC5D,iBAAK;AACL,gBAAI,MAAM,GAAG;AACX,kBAAI,OAAO,IAAI,QAAQ;AACrB,kBAAE;AACF,oBAAI,EAAE,KAAM,MAAK,OAAO,EAAE;AAAA,oBAAU,MAAK,OAAO,KAAK,OAAO;AAAA,cAC9D,OAAO;AACL,qBAAK,OAAO;AACZ,kBAAE,OAAO,IAAI,MAAM,EAAE;AAAA,cACvB;AACA;AAAA,YACF;AACA,cAAE;AAAA,UACJ;AACA,eAAK,UAAU;AACf,iBAAO;AAAA,QACT;AAAA;AAAA,MAGF,GAAG;AAAA,QACD,KAAK;AAAA,QACL,OAAO,SAAS,WAAW,GAAG;AAC5B,cAAI,MAAMA,QAAO,YAAY,CAAC;AAC9B,cAAI,IAAI,KAAK;AACb,cAAI,IAAI;AACR,YAAE,KAAK,KAAK,GAAG;AACf,eAAK,EAAE,KAAK;AACZ,iBAAO,IAAI,EAAE,MAAM;AACjB,gBAAI,MAAM,EAAE;AACZ,gBAAI,KAAK,IAAI,IAAI,SAAS,IAAI,SAAS;AACvC,gBAAI,KAAK,KAAK,IAAI,SAAS,GAAG,GAAG,EAAE;AACnC,iBAAK;AACL,gBAAI,MAAM,GAAG;AACX,kBAAI,OAAO,IAAI,QAAQ;AACrB,kBAAE;AACF,oBAAI,EAAE,KAAM,MAAK,OAAO,EAAE;AAAA,oBAAU,MAAK,OAAO,KAAK,OAAO;AAAA,cAC9D,OAAO;AACL,qBAAK,OAAO;AACZ,kBAAE,OAAO,IAAI,MAAM,EAAE;AAAA,cACvB;AACA;AAAA,YACF;AACA,cAAE;AAAA,UACJ;AACA,eAAK,UAAU;AACf,iBAAO;AAAA,QACT;AAAA;AAAA,MAGF,GAAG;AAAA,QACD,KAAK;AAAA,QACL,OAAO,SAAS,MAAM,GAAG,SAAS;AAChC,iBAAO,QAAQ,MAAM,cAAc,cAAc,CAAC,GAAG,OAAO,GAAG,CAAC,GAAG;AAAA;AAAA,YAEjE,OAAO;AAAA;AAAA,YAEP,eAAe;AAAA,UACjB,CAAC,CAAC;AAAA,QACJ;AAAA,MACF,CAAC,CAAC;AACF,aAAO;AAAA,IACT,EAAE;AAAA;AAAA;;;ACtLF;AAAA;AAAA;AAGA,aAAS,QAAQ,KAAK,IAAI;AACxB,UAAI,QAAQ;AACZ,UAAI,oBAAoB,KAAK,kBAAkB,KAAK,eAAe;AACnE,UAAI,oBAAoB,KAAK,kBAAkB,KAAK,eAAe;AACnE,UAAI,qBAAqB,mBAAmB;AAC1C,YAAI,IAAI;AACN,aAAG,GAAG;AAAA,QACR,WAAW,KAAK;AACd,cAAI,CAAC,KAAK,gBAAgB;AACxB,oBAAQ,SAAS,aAAa,MAAM,GAAG;AAAA,UACzC,WAAW,CAAC,KAAK,eAAe,cAAc;AAC5C,iBAAK,eAAe,eAAe;AACnC,oBAAQ,SAAS,aAAa,MAAM,GAAG;AAAA,UACzC;AAAA,QACF;AACA,eAAO;AAAA,MACT;AAKA,UAAI,KAAK,gBAAgB;AACvB,aAAK,eAAe,YAAY;AAAA,MAClC;AAGA,UAAI,KAAK,gBAAgB;AACvB,aAAK,eAAe,YAAY;AAAA,MAClC;AACA,WAAK,SAAS,OAAO,MAAM,SAAUC,MAAK;AACxC,YAAI,CAAC,MAAMA,MAAK;AACd,cAAI,CAAC,MAAM,gBAAgB;AACzB,oBAAQ,SAAS,qBAAqB,OAAOA,IAAG;AAAA,UAClD,WAAW,CAAC,MAAM,eAAe,cAAc;AAC7C,kBAAM,eAAe,eAAe;AACpC,oBAAQ,SAAS,qBAAqB,OAAOA,IAAG;AAAA,UAClD,OAAO;AACL,oBAAQ,SAAS,aAAa,KAAK;AAAA,UACrC;AAAA,QACF,WAAW,IAAI;AACb,kBAAQ,SAAS,aAAa,KAAK;AACnC,aAAGA,IAAG;AAAA,QACR,OAAO;AACL,kBAAQ,SAAS,aAAa,KAAK;AAAA,QACrC;AAAA,MACF,CAAC;AACD,aAAO;AAAA,IACT;AACA,aAAS,oBAAoBC,OAAM,KAAK;AACtC,kBAAYA,OAAM,GAAG;AACrB,kBAAYA,KAAI;AAAA,IAClB;AACA,aAAS,YAAYA,OAAM;AACzB,UAAIA,MAAK,kBAAkB,CAACA,MAAK,eAAe,UAAW;AAC3D,UAAIA,MAAK,kBAAkB,CAACA,MAAK,eAAe,UAAW;AAC3D,MAAAA,MAAK,KAAK,OAAO;AAAA,IACnB;AACA,aAAS,YAAY;AACnB,UAAI,KAAK,gBAAgB;AACvB,aAAK,eAAe,YAAY;AAChC,aAAK,eAAe,UAAU;AAC9B,aAAK,eAAe,QAAQ;AAC5B,aAAK,eAAe,aAAa;AAAA,MACnC;AACA,UAAI,KAAK,gBAAgB;AACvB,aAAK,eAAe,YAAY;AAChC,aAAK,eAAe,QAAQ;AAC5B,aAAK,eAAe,SAAS;AAC7B,aAAK,eAAe,cAAc;AAClC,aAAK,eAAe,cAAc;AAClC,aAAK,eAAe,WAAW;AAC/B,aAAK,eAAe,eAAe;AAAA,MACrC;AAAA,IACF;AACA,aAAS,YAAYA,OAAM,KAAK;AAC9B,MAAAA,MAAK,KAAK,SAAS,GAAG;AAAA,IACxB;AACA,aAAS,eAAe,QAAQ,KAAK;AAOnC,UAAI,SAAS,OAAO;AACpB,UAAI,SAAS,OAAO;AACpB,UAAI,UAAU,OAAO,eAAe,UAAU,OAAO,YAAa,QAAO,QAAQ,GAAG;AAAA,UAAO,QAAO,KAAK,SAAS,GAAG;AAAA,IACrH;AACA,WAAO,UAAU;AAAA,MACf;AAAA,MACA;AAAA,MACA;AAAA,IACF;AAAA;AAAA;;;AC/FA;AAAA;AAAA;AAEA,aAAS,eAAe,UAAU,YAAY;AAAE,eAAS,YAAY,OAAO,OAAO,WAAW,SAAS;AAAG,eAAS,UAAU,cAAc;AAAU,eAAS,YAAY;AAAA,IAAY;AAEtL,QAAI,QAAQ,CAAC;AAEb,aAAS,gBAAgB,MAAM,SAAS,MAAM;AAC5C,UAAI,CAAC,MAAM;AACT,eAAO;AAAA,MACT;AAEA,eAAS,WAAW,MAAM,MAAM,MAAM;AACpC,YAAI,OAAO,YAAY,UAAU;AAC/B,iBAAO;AAAA,QACT,OAAO;AACL,iBAAO,QAAQ,MAAM,MAAM,IAAI;AAAA,QACjC;AAAA,MACF;AAEA,UAAI,YAEJ,SAAU,OAAO;AACf,uBAAeC,YAAW,KAAK;AAE/B,iBAASA,WAAU,MAAM,MAAM,MAAM;AACnC,iBAAO,MAAM,KAAK,MAAM,WAAW,MAAM,MAAM,IAAI,CAAC,KAAK;AAAA,QAC3D;AAEA,eAAOA;AAAA,MACT,EAAE,IAAI;AAEN,gBAAU,UAAU,OAAO,KAAK;AAChC,gBAAU,UAAU,OAAO;AAC3B,YAAM,IAAI,IAAI;AAAA,IAChB;AAGA,aAAS,MAAM,UAAU,OAAO;AAC9B,UAAI,MAAM,QAAQ,QAAQ,GAAG;AAC3B,YAAI,MAAM,SAAS;AACnB,mBAAW,SAAS,IAAI,SAAU,GAAG;AACnC,iBAAO,OAAO,CAAC;AAAA,QACjB,CAAC;AAED,YAAI,MAAM,GAAG;AACX,iBAAO,UAAU,OAAO,OAAO,GAAG,EAAE,OAAO,SAAS,MAAM,GAAG,MAAM,CAAC,EAAE,KAAK,IAAI,GAAG,OAAO,IAAI,SAAS,MAAM,CAAC;AAAA,QAC/G,WAAW,QAAQ,GAAG;AACpB,iBAAO,UAAU,OAAO,OAAO,GAAG,EAAE,OAAO,SAAS,CAAC,GAAG,MAAM,EAAE,OAAO,SAAS,CAAC,CAAC;AAAA,QACpF,OAAO;AACL,iBAAO,MAAM,OAAO,OAAO,GAAG,EAAE,OAAO,SAAS,CAAC,CAAC;AAAA,QACpD;AAAA,MACF,OAAO;AACL,eAAO,MAAM,OAAO,OAAO,GAAG,EAAE,OAAO,OAAO,QAAQ,CAAC;AAAA,MACzD;AAAA,IACF;AAGA,aAAS,WAAW,KAAK,QAAQ,KAAK;AACpC,aAAO,IAAI,OAAO,CAAC,OAAO,MAAM,IAAI,IAAI,CAAC,KAAK,OAAO,MAAM,MAAM;AAAA,IACnE;AAGA,aAAS,SAAS,KAAK,QAAQ,UAAU;AACvC,UAAI,aAAa,UAAa,WAAW,IAAI,QAAQ;AACnD,mBAAW,IAAI;AAAA,MACjB;AAEA,aAAO,IAAI,UAAU,WAAW,OAAO,QAAQ,QAAQ,MAAM;AAAA,IAC/D;AAGA,aAAS,SAAS,KAAK,QAAQ,OAAO;AACpC,UAAI,OAAO,UAAU,UAAU;AAC7B,gBAAQ;AAAA,MACV;AAEA,UAAI,QAAQ,OAAO,SAAS,IAAI,QAAQ;AACtC,eAAO;AAAA,MACT,OAAO;AACL,eAAO,IAAI,QAAQ,QAAQ,KAAK,MAAM;AAAA,MACxC;AAAA,IACF;AAEA,oBAAgB,yBAAyB,SAAU,MAAM,OAAO;AAC9D,aAAO,gBAAgB,QAAQ,8BAA8B,OAAO;AAAA,IACtE,GAAG,SAAS;AACZ,oBAAgB,wBAAwB,SAAU,MAAM,UAAU,QAAQ;AAExE,UAAI;AAEJ,UAAI,OAAO,aAAa,YAAY,WAAW,UAAU,MAAM,GAAG;AAChE,qBAAa;AACb,mBAAW,SAAS,QAAQ,SAAS,EAAE;AAAA,MACzC,OAAO;AACL,qBAAa;AAAA,MACf;AAEA,UAAI;AAEJ,UAAI,SAAS,MAAM,WAAW,GAAG;AAE/B,cAAM,OAAO,OAAO,MAAM,GAAG,EAAE,OAAO,YAAY,GAAG,EAAE,OAAO,MAAM,UAAU,MAAM,CAAC;AAAA,MACvF,OAAO;AACL,YAAI,OAAO,SAAS,MAAM,GAAG,IAAI,aAAa;AAC9C,cAAM,QAAS,OAAO,MAAM,IAAK,EAAE,OAAO,MAAM,GAAG,EAAE,OAAO,YAAY,GAAG,EAAE,OAAO,MAAM,UAAU,MAAM,CAAC;AAAA,MAC7G;AAEA,aAAO,mBAAmB,OAAO,OAAO,MAAM;AAC9C,aAAO;AAAA,IACT,GAAG,SAAS;AACZ,oBAAgB,6BAA6B,yBAAyB;AACtE,oBAAgB,8BAA8B,SAAU,MAAM;AAC5D,aAAO,SAAS,OAAO;AAAA,IACzB,CAAC;AACD,oBAAgB,8BAA8B,iBAAiB;AAC/D,oBAAgB,wBAAwB,SAAU,MAAM;AACtD,aAAO,iBAAiB,OAAO;AAAA,IACjC,CAAC;AACD,oBAAgB,yBAAyB,gCAAgC;AACzE,oBAAgB,0BAA0B,2BAA2B;AACrE,oBAAgB,8BAA8B,iBAAiB;AAC/D,oBAAgB,0BAA0B,uCAAuC,SAAS;AAC1F,oBAAgB,wBAAwB,SAAU,KAAK;AACrD,aAAO,uBAAuB;AAAA,IAChC,GAAG,SAAS;AACZ,oBAAgB,sCAAsC,kCAAkC;AACxF,WAAO,QAAQ,QAAQ;AAAA;AAAA;;;AC9HvB;AAAA;AAAA;AAEA,QAAI,wBAAwB,yBAA2B,MAAM;AAC7D,aAAS,kBAAkB,SAAS,UAAU,WAAW;AACvD,aAAO,QAAQ,iBAAiB,OAAO,QAAQ,gBAAgB,WAAW,QAAQ,SAAS,IAAI;AAAA,IACjG;AACA,aAAS,iBAAiB,OAAO,SAAS,WAAW,UAAU;AAC7D,UAAI,MAAM,kBAAkB,SAAS,UAAU,SAAS;AACxD,UAAI,OAAO,MAAM;AACf,YAAI,EAAE,SAAS,GAAG,KAAK,KAAK,MAAM,GAAG,MAAM,QAAQ,MAAM,GAAG;AAC1D,cAAI,OAAO,WAAW,YAAY;AAClC,gBAAM,IAAI,sBAAsB,MAAM,GAAG;AAAA,QAC3C;AACA,eAAO,KAAK,MAAM,GAAG;AAAA,MACvB;AAGA,aAAO,MAAM,aAAa,KAAK,KAAK;AAAA,IACtC;AACA,WAAO,UAAU;AAAA,MACf;AAAA,IACF;AAAA;AAAA;;;ACrBA;AAAA;AAKA,WAAO,UAAU;AAoBjB,aAAS,UAAW,IAAI,KAAK;AAC3B,UAAI,OAAO,eAAe,GAAG;AAC3B,eAAO;AAAA,MACT;AAEA,UAAI,SAAS;AACb,eAAS,aAAa;AACpB,YAAI,CAAC,QAAQ;AACX,cAAI,OAAO,kBAAkB,GAAG;AAC9B,kBAAM,IAAI,MAAM,GAAG;AAAA,UACrB,WAAW,OAAO,kBAAkB,GAAG;AACrC,oBAAQ,MAAM,GAAG;AAAA,UACnB,OAAO;AACL,oBAAQ,KAAK,GAAG;AAAA,UAClB;AACA,mBAAS;AAAA,QACX;AACA,eAAO,GAAG,MAAM,MAAM,SAAS;AAAA,MACjC;AAEA,aAAO;AAAA,IACT;AAUA,aAAS,OAAQ,MAAM;AAErB,UAAI;AACF,YAAI,CAAC,OAAO,aAAc,QAAO;AAAA,MACnC,SAAS,GAAG;AACV,eAAO;AAAA,MACT;AACA,UAAI,MAAM,OAAO,aAAa,IAAI;AAClC,UAAI,QAAQ,IAAK,QAAO;AACxB,aAAO,OAAO,GAAG,EAAE,YAAY,MAAM;AAAA,IACvC;AAAA;AAAA;;;AClEA;AAAA;AAAA;AA2BA,WAAO,UAAU;AAYjB,aAAS,cAAc,OAAO;AAC5B,UAAI,QAAQ;AACZ,WAAK,OAAO;AACZ,WAAK,QAAQ;AACb,WAAK,SAAS,WAAY;AACxB,uBAAe,OAAO,KAAK;AAAA,MAC7B;AAAA,IACF;AAIA,QAAI;AAGJ,aAAS,gBAAgB;AAGzB,QAAI,eAAe;AAAA,MACjB,WAAW;AAAA,IACb;AAIA,QAAI,SAAS;AAGb,QAAIC,UAAS,iBAAkB;AAC/B,QAAI,iBAAiB,OAAO,WAAW,cAAc,SAAS,OAAO,WAAW,cAAc,SAAS,OAAO,SAAS,cAAc,OAAO,CAAC,GAAG,cAAc,WAAY;AAAA,IAAC;AAC3K,aAAS,oBAAoB,OAAO;AAClC,aAAOA,QAAO,KAAK,KAAK;AAAA,IAC1B;AACA,aAAS,cAAc,KAAK;AAC1B,aAAOA,QAAO,SAAS,GAAG,KAAK,eAAe;AAAA,IAChD;AACA,QAAI,cAAc;AAClB,QAAI,WAAW;AAAf,QACE,mBAAmB,SAAS;AAC9B,QAAI,iBAAiB,yBAAqB;AAA1C,QACE,uBAAuB,eAAe;AADxC,QAEE,6BAA6B,eAAe;AAF9C,QAGE,wBAAwB,eAAe;AAHzC,QAIE,yBAAyB,eAAe;AAJ1C,QAKE,uBAAuB,eAAe;AALxC,QAME,yBAAyB,eAAe;AAN1C,QAOE,6BAA6B,eAAe;AAP9C,QAQE,uBAAuB,eAAe;AACxC,QAAI,iBAAiB,YAAY;AACjC,+BAAoB,UAAU,MAAM;AACpC,aAAS,MAAM;AAAA,IAAC;AAChB,aAAS,cAAc,SAAS,QAAQ,UAAU;AAChD,eAAS,UAAU;AACnB,gBAAU,WAAW,CAAC;AAOtB,UAAI,OAAO,aAAa,UAAW,YAAW,kBAAkB;AAIhE,WAAK,aAAa,CAAC,CAAC,QAAQ;AAC5B,UAAI,SAAU,MAAK,aAAa,KAAK,cAAc,CAAC,CAAC,QAAQ;AAK7D,WAAK,gBAAgB,iBAAiB,MAAM,SAAS,yBAAyB,QAAQ;AAGtF,WAAK,cAAc;AAGnB,WAAK,YAAY;AAEjB,WAAK,SAAS;AAEd,WAAK,QAAQ;AAEb,WAAK,WAAW;AAGhB,WAAK,YAAY;AAKjB,UAAI,WAAW,QAAQ,kBAAkB;AACzC,WAAK,gBAAgB,CAAC;AAKtB,WAAK,kBAAkB,QAAQ,mBAAmB;AAKlD,WAAK,SAAS;AAGd,WAAK,UAAU;AAGf,WAAK,SAAS;AAMd,WAAK,OAAO;AAKZ,WAAK,mBAAmB;AAGxB,WAAK,UAAU,SAAU,IAAI;AAC3B,gBAAQ,QAAQ,EAAE;AAAA,MACpB;AAGA,WAAK,UAAU;AAGf,WAAK,WAAW;AAChB,WAAK,kBAAkB;AACvB,WAAK,sBAAsB;AAI3B,WAAK,YAAY;AAIjB,WAAK,cAAc;AAGnB,WAAK,eAAe;AAGpB,WAAK,YAAY,QAAQ,cAAc;AAGvC,WAAK,cAAc,CAAC,CAAC,QAAQ;AAG7B,WAAK,uBAAuB;AAI5B,WAAK,qBAAqB,IAAI,cAAc,IAAI;AAAA,IAClD;AACA,kBAAc,UAAU,YAAY,SAAS,YAAY;AACvD,UAAI,UAAU,KAAK;AACnB,UAAI,MAAM,CAAC;AACX,aAAO,SAAS;AACd,YAAI,KAAK,OAAO;AAChB,kBAAU,QAAQ;AAAA,MACpB;AACA,aAAO;AAAA,IACT;AACA,KAAC,WAAY;AACX,UAAI;AACF,eAAO,eAAe,cAAc,WAAW,UAAU;AAAA,UACvD,KAAK,aAAa,UAAU,SAAS,4BAA4B;AAC/D,mBAAO,KAAK,UAAU;AAAA,UACxB,GAAG,8EAAmF,SAAS;AAAA,QACjG,CAAC;AAAA,MACH,SAAS,GAAG;AAAA,MAAC;AAAA,IACf,GAAG;AAIH,QAAI;AACJ,QAAI,OAAO,WAAW,cAAc,OAAO,eAAe,OAAO,SAAS,UAAU,OAAO,WAAW,MAAM,YAAY;AACtH,wBAAkB,SAAS,UAAU,OAAO,WAAW;AACvD,aAAO,eAAe,UAAU,OAAO,aAAa;AAAA,QAClD,OAAO,SAAS,MAAM,QAAQ;AAC5B,cAAI,gBAAgB,KAAK,MAAM,MAAM,EAAG,QAAO;AAC/C,cAAI,SAAS,SAAU,QAAO;AAC9B,iBAAO,UAAU,OAAO,0BAA0B;AAAA,QACpD;AAAA,MACF,CAAC;AAAA,IACH,OAAO;AACL,wBAAkB,SAASC,iBAAgB,QAAQ;AACjD,eAAO,kBAAkB;AAAA,MAC3B;AAAA,IACF;AACA,aAAS,SAAS,SAAS;AACzB,eAAS,UAAU;AAYnB,UAAI,WAAW,gBAAgB;AAC/B,UAAI,CAAC,YAAY,CAAC,gBAAgB,KAAK,UAAU,IAAI,EAAG,QAAO,IAAI,SAAS,OAAO;AACnF,WAAK,iBAAiB,IAAI,cAAc,SAAS,MAAM,QAAQ;AAG/D,WAAK,WAAW;AAChB,UAAI,SAAS;AACX,YAAI,OAAO,QAAQ,UAAU,WAAY,MAAK,SAAS,QAAQ;AAC/D,YAAI,OAAO,QAAQ,WAAW,WAAY,MAAK,UAAU,QAAQ;AACjE,YAAI,OAAO,QAAQ,YAAY,WAAY,MAAK,WAAW,QAAQ;AACnE,YAAI,OAAO,QAAQ,UAAU,WAAY,MAAK,SAAS,QAAQ;AAAA,MACjE;AACA,aAAO,KAAK,IAAI;AAAA,IAClB;AAGA,aAAS,UAAU,OAAO,WAAY;AACpC,qBAAe,MAAM,IAAI,uBAAuB,CAAC;AAAA,IACnD;AACA,aAAS,cAAc,QAAQ,IAAI;AACjC,UAAI,KAAK,IAAI,2BAA2B;AAExC,qBAAe,QAAQ,EAAE;AACzB,cAAQ,SAAS,IAAI,EAAE;AAAA,IACzB;AAKA,aAAS,WAAW,QAAQ,OAAO,OAAO,IAAI;AAC5C,UAAI;AACJ,UAAI,UAAU,MAAM;AAClB,aAAK,IAAI,uBAAuB;AAAA,MAClC,WAAW,OAAO,UAAU,YAAY,CAAC,MAAM,YAAY;AACzD,aAAK,IAAI,qBAAqB,SAAS,CAAC,UAAU,QAAQ,GAAG,KAAK;AAAA,MACpE;AACA,UAAI,IAAI;AACN,uBAAe,QAAQ,EAAE;AACzB,gBAAQ,SAAS,IAAI,EAAE;AACvB,eAAO;AAAA,MACT;AACA,aAAO;AAAA,IACT;AACA,aAAS,UAAU,QAAQ,SAAU,OAAO,UAAU,IAAI;AACxD,UAAI,QAAQ,KAAK;AACjB,UAAI,MAAM;AACV,UAAI,QAAQ,CAAC,MAAM,cAAc,cAAc,KAAK;AACpD,UAAI,SAAS,CAACD,QAAO,SAAS,KAAK,GAAG;AACpC,gBAAQ,oBAAoB,KAAK;AAAA,MACnC;AACA,UAAI,OAAO,aAAa,YAAY;AAClC,aAAK;AACL,mBAAW;AAAA,MACb;AACA,UAAI,MAAO,YAAW;AAAA,eAAkB,CAAC,SAAU,YAAW,MAAM;AACpE,UAAI,OAAO,OAAO,WAAY,MAAK;AACnC,UAAI,MAAM,OAAQ,eAAc,MAAM,EAAE;AAAA,eAAW,SAAS,WAAW,MAAM,OAAO,OAAO,EAAE,GAAG;AAC9F,cAAM;AACN,cAAM,cAAc,MAAM,OAAO,OAAO,OAAO,UAAU,EAAE;AAAA,MAC7D;AACA,aAAO;AAAA,IACT;AACA,aAAS,UAAU,OAAO,WAAY;AACpC,WAAK,eAAe;AAAA,IACtB;AACA,aAAS,UAAU,SAAS,WAAY;AACtC,UAAI,QAAQ,KAAK;AACjB,UAAI,MAAM,QAAQ;AAChB,cAAM;AACN,YAAI,CAAC,MAAM,WAAW,CAAC,MAAM,UAAU,CAAC,MAAM,oBAAoB,MAAM,gBAAiB,aAAY,MAAM,KAAK;AAAA,MAClH;AAAA,IACF;AACA,aAAS,UAAU,qBAAqB,SAAS,mBAAmB,UAAU;AAE5E,UAAI,OAAO,aAAa,SAAU,YAAW,SAAS,YAAY;AAClE,UAAI,EAAE,CAAC,OAAO,QAAQ,SAAS,SAAS,UAAU,UAAU,QAAQ,SAAS,WAAW,YAAY,KAAK,EAAE,SAAS,WAAW,IAAI,YAAY,CAAC,IAAI,IAAK,OAAM,IAAI,qBAAqB,QAAQ;AAChM,WAAK,eAAe,kBAAkB;AACtC,aAAO;AAAA,IACT;AACA,WAAO,eAAe,SAAS,WAAW,kBAAkB;AAAA;AAAA;AAAA;AAAA,MAI1D,YAAY;AAAA,MACZ,KAAK,SAAS,MAAM;AAClB,eAAO,KAAK,kBAAkB,KAAK,eAAe,UAAU;AAAA,MAC9D;AAAA,IACF,CAAC;AACD,aAAS,YAAY,OAAO,OAAO,UAAU;AAC3C,UAAI,CAAC,MAAM,cAAc,MAAM,kBAAkB,SAAS,OAAO,UAAU,UAAU;AACnF,gBAAQA,QAAO,KAAK,OAAO,QAAQ;AAAA,MACrC;AACA,aAAO;AAAA,IACT;AACA,WAAO,eAAe,SAAS,WAAW,yBAAyB;AAAA;AAAA;AAAA;AAAA,MAIjE,YAAY;AAAA,MACZ,KAAK,SAAS,MAAM;AAClB,eAAO,KAAK,eAAe;AAAA,MAC7B;AAAA,IACF,CAAC;AAKD,aAAS,cAAc,QAAQ,OAAO,OAAO,OAAO,UAAU,IAAI;AAChE,UAAI,CAAC,OAAO;AACV,YAAI,WAAW,YAAY,OAAO,OAAO,QAAQ;AACjD,YAAI,UAAU,UAAU;AACtB,kBAAQ;AACR,qBAAW;AACX,kBAAQ;AAAA,QACV;AAAA,MACF;AACA,UAAI,MAAM,MAAM,aAAa,IAAI,MAAM;AACvC,YAAM,UAAU;AAChB,UAAI,MAAM,MAAM,SAAS,MAAM;AAE/B,UAAI,CAAC,IAAK,OAAM,YAAY;AAC5B,UAAI,MAAM,WAAW,MAAM,QAAQ;AACjC,YAAI,OAAO,MAAM;AACjB,cAAM,sBAAsB;AAAA,UAC1B;AAAA,UACA;AAAA,UACA;AAAA,UACA,UAAU;AAAA,UACV,MAAM;AAAA,QACR;AACA,YAAI,MAAM;AACR,eAAK,OAAO,MAAM;AAAA,QACpB,OAAO;AACL,gBAAM,kBAAkB,MAAM;AAAA,QAChC;AACA,cAAM,wBAAwB;AAAA,MAChC,OAAO;AACL,gBAAQ,QAAQ,OAAO,OAAO,KAAK,OAAO,UAAU,EAAE;AAAA,MACxD;AACA,aAAO;AAAA,IACT;AACA,aAAS,QAAQ,QAAQ,OAAO,QAAQ,KAAK,OAAO,UAAU,IAAI;AAChE,YAAM,WAAW;AACjB,YAAM,UAAU;AAChB,YAAM,UAAU;AAChB,YAAM,OAAO;AACb,UAAI,MAAM,UAAW,OAAM,QAAQ,IAAI,qBAAqB,OAAO,CAAC;AAAA,eAAW,OAAQ,QAAO,QAAQ,OAAO,MAAM,OAAO;AAAA,UAAO,QAAO,OAAO,OAAO,UAAU,MAAM,OAAO;AAC7K,YAAM,OAAO;AAAA,IACf;AACA,aAAS,aAAa,QAAQ,OAAO,MAAM,IAAI,IAAI;AACjD,QAAE,MAAM;AACR,UAAI,MAAM;AAGR,gBAAQ,SAAS,IAAI,EAAE;AAGvB,gBAAQ,SAAS,aAAa,QAAQ,KAAK;AAC3C,eAAO,eAAe,eAAe;AACrC,uBAAe,QAAQ,EAAE;AAAA,MAC3B,OAAO;AAGL,WAAG,EAAE;AACL,eAAO,eAAe,eAAe;AACrC,uBAAe,QAAQ,EAAE;AAGzB,oBAAY,QAAQ,KAAK;AAAA,MAC3B;AAAA,IACF;AACA,aAAS,mBAAmB,OAAO;AACjC,YAAM,UAAU;AAChB,YAAM,UAAU;AAChB,YAAM,UAAU,MAAM;AACtB,YAAM,WAAW;AAAA,IACnB;AACA,aAAS,QAAQ,QAAQ,IAAI;AAC3B,UAAI,QAAQ,OAAO;AACnB,UAAI,OAAO,MAAM;AACjB,UAAI,KAAK,MAAM;AACf,UAAI,OAAO,OAAO,WAAY,OAAM,IAAI,sBAAsB;AAC9D,yBAAmB,KAAK;AACxB,UAAI,GAAI,cAAa,QAAQ,OAAO,MAAM,IAAI,EAAE;AAAA,WAAO;AAErD,YAAI,WAAW,WAAW,KAAK,KAAK,OAAO;AAC3C,YAAI,CAAC,YAAY,CAAC,MAAM,UAAU,CAAC,MAAM,oBAAoB,MAAM,iBAAiB;AAClF,sBAAY,QAAQ,KAAK;AAAA,QAC3B;AACA,YAAI,MAAM;AACR,kBAAQ,SAAS,YAAY,QAAQ,OAAO,UAAU,EAAE;AAAA,QAC1D,OAAO;AACL,qBAAW,QAAQ,OAAO,UAAU,EAAE;AAAA,QACxC;AAAA,MACF;AAAA,IACF;AACA,aAAS,WAAW,QAAQ,OAAO,UAAU,IAAI;AAC/C,UAAI,CAAC,SAAU,cAAa,QAAQ,KAAK;AACzC,YAAM;AACN,SAAG;AACH,kBAAY,QAAQ,KAAK;AAAA,IAC3B;AAKA,aAAS,aAAa,QAAQ,OAAO;AACnC,UAAI,MAAM,WAAW,KAAK,MAAM,WAAW;AACzC,cAAM,YAAY;AAClB,eAAO,KAAK,OAAO;AAAA,MACrB;AAAA,IACF;AAGA,aAAS,YAAY,QAAQ,OAAO;AAClC,YAAM,mBAAmB;AACzB,UAAI,QAAQ,MAAM;AAClB,UAAI,OAAO,WAAW,SAAS,MAAM,MAAM;AAEzC,YAAI,IAAI,MAAM;AACd,YAAI,SAAS,IAAI,MAAM,CAAC;AACxB,YAAI,SAAS,MAAM;AACnB,eAAO,QAAQ;AACf,YAAI,QAAQ;AACZ,YAAI,aAAa;AACjB,eAAO,OAAO;AACZ,iBAAO,KAAK,IAAI;AAChB,cAAI,CAAC,MAAM,MAAO,cAAa;AAC/B,kBAAQ,MAAM;AACd,mBAAS;AAAA,QACX;AACA,eAAO,aAAa;AACpB,gBAAQ,QAAQ,OAAO,MAAM,MAAM,QAAQ,QAAQ,IAAI,OAAO,MAAM;AAIpE,cAAM;AACN,cAAM,sBAAsB;AAC5B,YAAI,OAAO,MAAM;AACf,gBAAM,qBAAqB,OAAO;AAClC,iBAAO,OAAO;AAAA,QAChB,OAAO;AACL,gBAAM,qBAAqB,IAAI,cAAc,KAAK;AAAA,QACpD;AACA,cAAM,uBAAuB;AAAA,MAC/B,OAAO;AAEL,eAAO,OAAO;AACZ,cAAI,QAAQ,MAAM;AAClB,cAAI,WAAW,MAAM;AACrB,cAAI,KAAK,MAAM;AACf,cAAI,MAAM,MAAM,aAAa,IAAI,MAAM;AACvC,kBAAQ,QAAQ,OAAO,OAAO,KAAK,OAAO,UAAU,EAAE;AACtD,kBAAQ,MAAM;AACd,gBAAM;AAKN,cAAI,MAAM,SAAS;AACjB;AAAA,UACF;AAAA,QACF;AACA,YAAI,UAAU,KAAM,OAAM,sBAAsB;AAAA,MAClD;AACA,YAAM,kBAAkB;AACxB,YAAM,mBAAmB;AAAA,IAC3B;AACA,aAAS,UAAU,SAAS,SAAU,OAAO,UAAU,IAAI;AACzD,SAAG,IAAI,2BAA2B,UAAU,CAAC;AAAA,IAC/C;AACA,aAAS,UAAU,UAAU;AAC7B,aAAS,UAAU,MAAM,SAAU,OAAO,UAAU,IAAI;AACtD,UAAI,QAAQ,KAAK;AACjB,UAAI,OAAO,UAAU,YAAY;AAC/B,aAAK;AACL,gBAAQ;AACR,mBAAW;AAAA,MACb,WAAW,OAAO,aAAa,YAAY;AACzC,aAAK;AACL,mBAAW;AAAA,MACb;AACA,UAAI,UAAU,QAAQ,UAAU,OAAW,MAAK,MAAM,OAAO,QAAQ;AAGrE,UAAI,MAAM,QAAQ;AAChB,cAAM,SAAS;AACf,aAAK,OAAO;AAAA,MACd;AAGA,UAAI,CAAC,MAAM,OAAQ,aAAY,MAAM,OAAO,EAAE;AAC9C,aAAO;AAAA,IACT;AACA,WAAO,eAAe,SAAS,WAAW,kBAAkB;AAAA;AAAA;AAAA;AAAA,MAI1D,YAAY;AAAA,MACZ,KAAK,SAAS,MAAM;AAClB,eAAO,KAAK,eAAe;AAAA,MAC7B;AAAA,IACF,CAAC;AACD,aAAS,WAAW,OAAO;AACzB,aAAO,MAAM,UAAU,MAAM,WAAW,KAAK,MAAM,oBAAoB,QAAQ,CAAC,MAAM,YAAY,CAAC,MAAM;AAAA,IAC3G;AACA,aAAS,UAAU,QAAQ,OAAO;AAChC,aAAO,OAAO,SAAU,KAAK;AAC3B,cAAM;AACN,YAAI,KAAK;AACP,yBAAe,QAAQ,GAAG;AAAA,QAC5B;AACA,cAAM,cAAc;AACpB,eAAO,KAAK,WAAW;AACvB,oBAAY,QAAQ,KAAK;AAAA,MAC3B,CAAC;AAAA,IACH;AACA,aAAS,UAAU,QAAQ,OAAO;AAChC,UAAI,CAAC,MAAM,eAAe,CAAC,MAAM,aAAa;AAC5C,YAAI,OAAO,OAAO,WAAW,cAAc,CAAC,MAAM,WAAW;AAC3D,gBAAM;AACN,gBAAM,cAAc;AACpB,kBAAQ,SAAS,WAAW,QAAQ,KAAK;AAAA,QAC3C,OAAO;AACL,gBAAM,cAAc;AACpB,iBAAO,KAAK,WAAW;AAAA,QACzB;AAAA,MACF;AAAA,IACF;AACA,aAAS,YAAY,QAAQ,OAAO;AAClC,UAAI,OAAO,WAAW,KAAK;AAC3B,UAAI,MAAM;AACR,kBAAU,QAAQ,KAAK;AACvB,YAAI,MAAM,cAAc,GAAG;AACzB,gBAAM,WAAW;AACjB,iBAAO,KAAK,QAAQ;AACpB,cAAI,MAAM,aAAa;AAGrB,gBAAI,SAAS,OAAO;AACpB,gBAAI,CAAC,UAAU,OAAO,eAAe,OAAO,YAAY;AACtD,qBAAO,QAAQ;AAAA,YACjB;AAAA,UACF;AAAA,QACF;AAAA,MACF;AACA,aAAO;AAAA,IACT;AACA,aAAS,YAAY,QAAQ,OAAO,IAAI;AACtC,YAAM,SAAS;AACf,kBAAY,QAAQ,KAAK;AACzB,UAAI,IAAI;AACN,YAAI,MAAM,SAAU,SAAQ,SAAS,EAAE;AAAA,YAAO,QAAO,KAAK,UAAU,EAAE;AAAA,MACxE;AACA,YAAM,QAAQ;AACd,aAAO,WAAW;AAAA,IACpB;AACA,aAAS,eAAe,SAAS,OAAO,KAAK;AAC3C,UAAI,QAAQ,QAAQ;AACpB,cAAQ,QAAQ;AAChB,aAAO,OAAO;AACZ,YAAI,KAAK,MAAM;AACf,cAAM;AACN,WAAG,GAAG;AACN,gBAAQ,MAAM;AAAA,MAChB;AAGA,YAAM,mBAAmB,OAAO;AAAA,IAClC;AACA,WAAO,eAAe,SAAS,WAAW,aAAa;AAAA;AAAA;AAAA;AAAA,MAIrD,YAAY;AAAA,MACZ,KAAK,SAAS,MAAM;AAClB,YAAI,KAAK,mBAAmB,QAAW;AACrC,iBAAO;AAAA,QACT;AACA,eAAO,KAAK,eAAe;AAAA,MAC7B;AAAA,MACA,KAAK,SAAS,IAAI,OAAO;AAGvB,YAAI,CAAC,KAAK,gBAAgB;AACxB;AAAA,QACF;AAIA,aAAK,eAAe,YAAY;AAAA,MAClC;AAAA,IACF,CAAC;AACD,aAAS,UAAU,UAAU,YAAY;AACzC,aAAS,UAAU,aAAa,YAAY;AAC5C,aAAS,UAAU,WAAW,SAAU,KAAK,IAAI;AAC/C,SAAG,GAAG;AAAA,IACR;AAAA;AAAA;;;AChoBA;AAAA;AAAA;AA6BA,QAAI,aAAa,OAAO,QAAQ,SAAU,KAAK;AAC7C,UAAIE,QAAO,CAAC;AACZ,eAAS,OAAO,IAAK,CAAAA,MAAK,KAAK,GAAG;AAClC,aAAOA;AAAA,IACT;AAGA,WAAO,UAAU;AACjB,QAAIC,YAAW;AACf,QAAI,WAAW;AACf,+BAAoB,QAAQA,SAAQ;AACpC;AAEM,aAAO,WAAW,SAAS,SAAS;AACxC,WAAS,IAAI,GAAG,IAAI,KAAK,QAAQ,KAAK;AAChC,iBAAS,KAAK,CAAC;AACnB,YAAI,CAAC,OAAO,UAAU,MAAM,EAAG,QAAO,UAAU,MAAM,IAAI,SAAS,UAAU,MAAM;AAAA,MACrF;AAAA,IACF;AALM;AAEE;AADG;AAKX,aAAS,OAAO,SAAS;AACvB,UAAI,EAAE,gBAAgB,QAAS,QAAO,IAAI,OAAO,OAAO;AACxD,MAAAA,UAAS,KAAK,MAAM,OAAO;AAC3B,eAAS,KAAK,MAAM,OAAO;AAC3B,WAAK,gBAAgB;AACrB,UAAI,SAAS;AACX,YAAI,QAAQ,aAAa,MAAO,MAAK,WAAW;AAChD,YAAI,QAAQ,aAAa,MAAO,MAAK,WAAW;AAChD,YAAI,QAAQ,kBAAkB,OAAO;AACnC,eAAK,gBAAgB;AACrB,eAAK,KAAK,OAAO,KAAK;AAAA,QACxB;AAAA,MACF;AAAA,IACF;AACA,WAAO,eAAe,OAAO,WAAW,yBAAyB;AAAA;AAAA;AAAA;AAAA,MAI/D,YAAY;AAAA,MACZ,KAAK,SAAS,MAAM;AAClB,eAAO,KAAK,eAAe;AAAA,MAC7B;AAAA,IACF,CAAC;AACD,WAAO,eAAe,OAAO,WAAW,kBAAkB;AAAA;AAAA;AAAA;AAAA,MAIxD,YAAY;AAAA,MACZ,KAAK,SAAS,MAAM;AAClB,eAAO,KAAK,kBAAkB,KAAK,eAAe,UAAU;AAAA,MAC9D;AAAA,IACF,CAAC;AACD,WAAO,eAAe,OAAO,WAAW,kBAAkB;AAAA;AAAA;AAAA;AAAA,MAIxD,YAAY;AAAA,MACZ,KAAK,SAAS,MAAM;AAClB,eAAO,KAAK,eAAe;AAAA,MAC7B;AAAA,IACF,CAAC;AAGD,aAAS,QAAQ;AAEf,UAAI,KAAK,eAAe,MAAO;AAI/B,cAAQ,SAAS,SAAS,IAAI;AAAA,IAChC;AACA,aAAS,QAAQC,OAAM;AACrB,MAAAA,MAAK,IAAI;AAAA,IACX;AACA,WAAO,eAAe,OAAO,WAAW,aAAa;AAAA;AAAA;AAAA;AAAA,MAInD,YAAY;AAAA,MACZ,KAAK,SAAS,MAAM;AAClB,YAAI,KAAK,mBAAmB,UAAa,KAAK,mBAAmB,QAAW;AAC1E,iBAAO;AAAA,QACT;AACA,eAAO,KAAK,eAAe,aAAa,KAAK,eAAe;AAAA,MAC9D;AAAA,MACA,KAAK,SAAS,IAAI,OAAO;AAGvB,YAAI,KAAK,mBAAmB,UAAa,KAAK,mBAAmB,QAAW;AAC1E;AAAA,QACF;AAIA,aAAK,eAAe,YAAY;AAChC,aAAK,eAAe,YAAY;AAAA,MAClC;AAAA,IACF,CAAC;AAAA;AAAA;;;AC7HD;AAAA;AAAA;AAKA,QAAI,6BAA6B,yBAA2B,MAAM;AAClE,aAAS,KAAK,UAAU;AACtB,UAAI,SAAS;AACb,aAAO,WAAY;AACjB,YAAI,OAAQ;AACZ,iBAAS;AACT,iBAAS,OAAO,UAAU,QAAQ,OAAO,IAAI,MAAM,IAAI,GAAG,OAAO,GAAG,OAAO,MAAM,QAAQ;AACvF,eAAK,IAAI,IAAI,UAAU,IAAI;AAAA,QAC7B;AACA,iBAAS,MAAM,MAAM,IAAI;AAAA,MAC3B;AAAA,IACF;AACA,aAAS,OAAO;AAAA,IAAC;AACjB,aAAS,UAAU,QAAQ;AACzB,aAAO,OAAO,aAAa,OAAO,OAAO,UAAU;AAAA,IACrD;AACA,aAAS,IAAI,QAAQ,MAAM,UAAU;AACnC,UAAI,OAAO,SAAS,WAAY,QAAO,IAAI,QAAQ,MAAM,IAAI;AAC7D,UAAI,CAAC,KAAM,QAAO,CAAC;AACnB,iBAAW,KAAK,YAAY,IAAI;AAChC,UAAI,WAAW,KAAK,YAAY,KAAK,aAAa,SAAS,OAAO;AAClE,UAAI,WAAW,KAAK,YAAY,KAAK,aAAa,SAAS,OAAO;AAClE,UAAI,iBAAiB,SAASC,kBAAiB;AAC7C,YAAI,CAAC,OAAO,SAAU,UAAS;AAAA,MACjC;AACA,UAAI,gBAAgB,OAAO,kBAAkB,OAAO,eAAe;AACnE,UAAI,WAAW,SAASC,YAAW;AACjC,mBAAW;AACX,wBAAgB;AAChB,YAAI,CAAC,SAAU,UAAS,KAAK,MAAM;AAAA,MACrC;AACA,UAAI,gBAAgB,OAAO,kBAAkB,OAAO,eAAe;AACnE,UAAI,QAAQ,SAASC,SAAQ;AAC3B,mBAAW;AACX,wBAAgB;AAChB,YAAI,CAAC,SAAU,UAAS,KAAK,MAAM;AAAA,MACrC;AACA,UAAI,UAAU,SAASC,SAAQ,KAAK;AAClC,iBAAS,KAAK,QAAQ,GAAG;AAAA,MAC3B;AACA,UAAI,UAAU,SAASC,WAAU;AAC/B,YAAI;AACJ,YAAI,YAAY,CAAC,eAAe;AAC9B,cAAI,CAAC,OAAO,kBAAkB,CAAC,OAAO,eAAe,MAAO,OAAM,IAAI,2BAA2B;AACjG,iBAAO,SAAS,KAAK,QAAQ,GAAG;AAAA,QAClC;AACA,YAAI,YAAY,CAAC,eAAe;AAC9B,cAAI,CAAC,OAAO,kBAAkB,CAAC,OAAO,eAAe,MAAO,OAAM,IAAI,2BAA2B;AACjG,iBAAO,SAAS,KAAK,QAAQ,GAAG;AAAA,QAClC;AAAA,MACF;AACA,UAAI,YAAY,SAASC,aAAY;AACnC,eAAO,IAAI,GAAG,UAAU,QAAQ;AAAA,MAClC;AACA,UAAI,UAAU,MAAM,GAAG;AACrB,eAAO,GAAG,YAAY,QAAQ;AAC9B,eAAO,GAAG,SAAS,OAAO;AAC1B,YAAI,OAAO,IAAK,WAAU;AAAA,YAAO,QAAO,GAAG,WAAW,SAAS;AAAA,MACjE,WAAW,YAAY,CAAC,OAAO,gBAAgB;AAE7C,eAAO,GAAG,OAAO,cAAc;AAC/B,eAAO,GAAG,SAAS,cAAc;AAAA,MACnC;AACA,aAAO,GAAG,OAAO,KAAK;AACtB,aAAO,GAAG,UAAU,QAAQ;AAC5B,UAAI,KAAK,UAAU,MAAO,QAAO,GAAG,SAAS,OAAO;AACpD,aAAO,GAAG,SAAS,OAAO;AAC1B,aAAO,WAAY;AACjB,eAAO,eAAe,YAAY,QAAQ;AAC1C,eAAO,eAAe,SAAS,OAAO;AACtC,eAAO,eAAe,WAAW,SAAS;AAC1C,YAAI,OAAO,IAAK,QAAO,IAAI,eAAe,UAAU,QAAQ;AAC5D,eAAO,eAAe,OAAO,cAAc;AAC3C,eAAO,eAAe,SAAS,cAAc;AAC7C,eAAO,eAAe,UAAU,QAAQ;AACxC,eAAO,eAAe,OAAO,KAAK;AAClC,eAAO,eAAe,SAAS,OAAO;AACtC,eAAO,eAAe,SAAS,OAAO;AAAA,MACxC;AAAA,IACF;AACA,WAAO,UAAU;AAAA;AAAA;;;ACrFjB;AAAA;AAAA;AAEA,QAAI;AACJ,aAAS,gBAAgB,KAAK,KAAK,OAAO;AAAE,YAAM,eAAe,GAAG;AAAG,UAAI,OAAO,KAAK;AAAE,eAAO,eAAe,KAAK,KAAK,EAAE,OAAc,YAAY,MAAM,cAAc,MAAM,UAAU,KAAK,CAAC;AAAA,MAAG,OAAO;AAAE,YAAI,GAAG,IAAI;AAAA,MAAO;AAAE,aAAO;AAAA,IAAK;AAC3O,aAAS,eAAe,KAAK;AAAE,UAAI,MAAM,aAAa,KAAK,QAAQ;AAAG,aAAO,OAAO,QAAQ,WAAW,MAAM,OAAO,GAAG;AAAA,IAAG;AAC1H,aAAS,aAAa,OAAO,MAAM;AAAE,UAAI,OAAO,UAAU,YAAY,UAAU,KAAM,QAAO;AAAO,UAAI,OAAO,MAAM,OAAO,WAAW;AAAG,UAAI,SAAS,QAAW;AAAE,YAAI,MAAM,KAAK,KAAK,OAAO,QAAQ,SAAS;AAAG,YAAI,OAAO,QAAQ,SAAU,QAAO;AAAK,cAAM,IAAI,UAAU,8CAA8C;AAAA,MAAG;AAAE,cAAQ,SAAS,WAAW,SAAS,QAAQ,KAAK;AAAA,IAAG;AACxX,QAAI,WAAW;AACf,QAAI,eAAe,OAAO,aAAa;AACvC,QAAI,cAAc,OAAO,YAAY;AACrC,QAAI,SAAS,OAAO,OAAO;AAC3B,QAAI,SAAS,OAAO,OAAO;AAC3B,QAAI,eAAe,OAAO,aAAa;AACvC,QAAI,iBAAiB,OAAO,eAAe;AAC3C,QAAI,UAAU,OAAO,QAAQ;AAC7B,aAAS,iBAAiB,OAAO,MAAM;AACrC,aAAO;AAAA,QACL;AAAA,QACA;AAAA,MACF;AAAA,IACF;AACA,aAAS,eAAe,MAAM;AAC5B,UAAI,UAAU,KAAK,YAAY;AAC/B,UAAI,YAAY,MAAM;AACpB,YAAI,OAAO,KAAK,OAAO,EAAE,KAAK;AAI9B,YAAI,SAAS,MAAM;AACjB,eAAK,YAAY,IAAI;AACrB,eAAK,YAAY,IAAI;AACrB,eAAK,WAAW,IAAI;AACpB,kBAAQ,iBAAiB,MAAM,KAAK,CAAC;AAAA,QACvC;AAAA,MACF;AAAA,IACF;AACA,aAAS,WAAW,MAAM;AAGxB,cAAQ,SAAS,gBAAgB,IAAI;AAAA,IACvC;AACA,aAAS,YAAY,aAAa,MAAM;AACtC,aAAO,SAAU,SAAS,QAAQ;AAChC,oBAAY,KAAK,WAAY;AAC3B,cAAI,KAAK,MAAM,GAAG;AAChB,oBAAQ,iBAAiB,QAAW,IAAI,CAAC;AACzC;AAAA,UACF;AACA,eAAK,cAAc,EAAE,SAAS,MAAM;AAAA,QACtC,GAAG,MAAM;AAAA,MACX;AAAA,IACF;AACA,QAAI,yBAAyB,OAAO,eAAe,WAAY;AAAA,IAAC,CAAC;AACjE,QAAI,uCAAuC,OAAO,gBAAgB,wBAAwB;AAAA,MACxF,IAAI,SAAS;AACX,eAAO,KAAK,OAAO;AAAA,MACrB;AAAA,MACA,MAAM,SAAS,OAAO;AACpB,YAAI,QAAQ;AAGZ,YAAI,QAAQ,KAAK,MAAM;AACvB,YAAI,UAAU,MAAM;AAClB,iBAAO,QAAQ,OAAO,KAAK;AAAA,QAC7B;AACA,YAAI,KAAK,MAAM,GAAG;AAChB,iBAAO,QAAQ,QAAQ,iBAAiB,QAAW,IAAI,CAAC;AAAA,QAC1D;AACA,YAAI,KAAK,OAAO,EAAE,WAAW;AAK3B,iBAAO,IAAI,QAAQ,SAAU,SAAS,QAAQ;AAC5C,oBAAQ,SAAS,WAAY;AAC3B,kBAAI,MAAM,MAAM,GAAG;AACjB,uBAAO,MAAM,MAAM,CAAC;AAAA,cACtB,OAAO;AACL,wBAAQ,iBAAiB,QAAW,IAAI,CAAC;AAAA,cAC3C;AAAA,YACF,CAAC;AAAA,UACH,CAAC;AAAA,QACH;AAMA,YAAI,cAAc,KAAK,YAAY;AACnC,YAAI;AACJ,YAAI,aAAa;AACf,oBAAU,IAAI,QAAQ,YAAY,aAAa,IAAI,CAAC;AAAA,QACtD,OAAO;AAGL,cAAI,OAAO,KAAK,OAAO,EAAE,KAAK;AAC9B,cAAI,SAAS,MAAM;AACjB,mBAAO,QAAQ,QAAQ,iBAAiB,MAAM,KAAK,CAAC;AAAA,UACtD;AACA,oBAAU,IAAI,QAAQ,KAAK,cAAc,CAAC;AAAA,QAC5C;AACA,aAAK,YAAY,IAAI;AACrB,eAAO;AAAA,MACT;AAAA,IACF,GAAG,gBAAgB,uBAAuB,OAAO,eAAe,WAAY;AAC1E,aAAO;AAAA,IACT,CAAC,GAAG,gBAAgB,uBAAuB,UAAU,SAAS,UAAU;AACtE,UAAI,SAAS;AAIb,aAAO,IAAI,QAAQ,SAAU,SAAS,QAAQ;AAC5C,eAAO,OAAO,EAAE,QAAQ,MAAM,SAAU,KAAK;AAC3C,cAAI,KAAK;AACP,mBAAO,GAAG;AACV;AAAA,UACF;AACA,kBAAQ,iBAAiB,QAAW,IAAI,CAAC;AAAA,QAC3C,CAAC;AAAA,MACH,CAAC;AAAA,IACH,CAAC,GAAG,wBAAwB,sBAAsB;AAClD,QAAI,oCAAoC,SAASC,mCAAkC,QAAQ;AACzF,UAAI;AACJ,UAAI,WAAW,OAAO,OAAO,uCAAuC,iBAAiB,CAAC,GAAG,gBAAgB,gBAAgB,SAAS;AAAA,QAChI,OAAO;AAAA,QACP,UAAU;AAAA,MACZ,CAAC,GAAG,gBAAgB,gBAAgB,cAAc;AAAA,QAChD,OAAO;AAAA,QACP,UAAU;AAAA,MACZ,CAAC,GAAG,gBAAgB,gBAAgB,aAAa;AAAA,QAC/C,OAAO;AAAA,QACP,UAAU;AAAA,MACZ,CAAC,GAAG,gBAAgB,gBAAgB,QAAQ;AAAA,QAC1C,OAAO;AAAA,QACP,UAAU;AAAA,MACZ,CAAC,GAAG,gBAAgB,gBAAgB,QAAQ;AAAA,QAC1C,OAAO,OAAO,eAAe;AAAA,QAC7B,UAAU;AAAA,MACZ,CAAC,GAAG,gBAAgB,gBAAgB,gBAAgB;AAAA,QAClD,OAAO,SAAS,MAAM,SAAS,QAAQ;AACrC,cAAI,OAAO,SAAS,OAAO,EAAE,KAAK;AAClC,cAAI,MAAM;AACR,qBAAS,YAAY,IAAI;AACzB,qBAAS,YAAY,IAAI;AACzB,qBAAS,WAAW,IAAI;AACxB,oBAAQ,iBAAiB,MAAM,KAAK,CAAC;AAAA,UACvC,OAAO;AACL,qBAAS,YAAY,IAAI;AACzB,qBAAS,WAAW,IAAI;AAAA,UAC1B;AAAA,QACF;AAAA,QACA,UAAU;AAAA,MACZ,CAAC,GAAG,eAAe;AACnB,eAAS,YAAY,IAAI;AACzB,eAAS,QAAQ,SAAU,KAAK;AAC9B,YAAI,OAAO,IAAI,SAAS,8BAA8B;AACpD,cAAI,SAAS,SAAS,WAAW;AAGjC,cAAI,WAAW,MAAM;AACnB,qBAAS,YAAY,IAAI;AACzB,qBAAS,YAAY,IAAI;AACzB,qBAAS,WAAW,IAAI;AACxB,mBAAO,GAAG;AAAA,UACZ;AACA,mBAAS,MAAM,IAAI;AACnB;AAAA,QACF;AACA,YAAI,UAAU,SAAS,YAAY;AACnC,YAAI,YAAY,MAAM;AACpB,mBAAS,YAAY,IAAI;AACzB,mBAAS,YAAY,IAAI;AACzB,mBAAS,WAAW,IAAI;AACxB,kBAAQ,iBAAiB,QAAW,IAAI,CAAC;AAAA,QAC3C;AACA,iBAAS,MAAM,IAAI;AAAA,MACrB,CAAC;AACD,aAAO,GAAG,YAAY,WAAW,KAAK,MAAM,QAAQ,CAAC;AACrD,aAAO;AAAA,IACT;AACA,WAAO,UAAU;AAAA;AAAA;;;ACnLjB;AAAA;AAAA,WAAO,UAAU,WAAY;AAC3B,YAAM,IAAI,MAAM,+CAA+C;AAAA,IACjE;AAAA;AAAA;;;ACFA;AAAA;AAAA;AAuBA,WAAO,UAAUC;AAGjB,QAAI;AAGJ,IAAAA,UAAS,gBAAgB;AAGzB,QAAI,KAAK,iBAAkB;AAC3B,QAAI,kBAAkB,SAASC,iBAAgB,SAAS,MAAM;AAC5D,aAAO,QAAQ,UAAU,IAAI,EAAE;AAAA,IACjC;AAIA,QAAI,SAAS;AAGb,QAAIC,UAAS,iBAAkB;AAC/B,QAAI,iBAAiB,OAAO,WAAW,cAAc,SAAS,OAAO,WAAW,cAAc,SAAS,OAAO,SAAS,cAAc,OAAO,CAAC,GAAG,cAAc,WAAY;AAAA,IAAC;AAC3K,aAAS,oBAAoB,OAAO;AAClC,aAAOA,QAAO,KAAK,KAAK;AAAA,IAC1B;AACA,aAAS,cAAc,KAAK;AAC1B,aAAOA,QAAO,SAAS,GAAG,KAAK,eAAe;AAAA,IAChD;AAGA,QAAI,YAAY;AAChB,QAAI;AACJ,QAAI,aAAa,UAAU,UAAU;AACnC,cAAQ,UAAU,SAAS,QAAQ;AAAA,IACrC,OAAO;AACL,cAAQ,SAASC,SAAQ;AAAA,MAAC;AAAA,IAC5B;AAGA,QAAI,aAAa;AACjB,QAAI,cAAc;AAClB,QAAI,WAAW;AAAf,QACE,mBAAmB,SAAS;AAC9B,QAAI,iBAAiB,yBAAqB;AAA1C,QACE,uBAAuB,eAAe;AADxC,QAEE,4BAA4B,eAAe;AAF7C,QAGE,6BAA6B,eAAe;AAH9C,QAIE,qCAAqC,eAAe;AAGtD,QAAI;AACJ,QAAI;AACJ,QAAI;AACJ,+BAAoBH,WAAU,MAAM;AACpC,QAAI,iBAAiB,YAAY;AACjC,QAAI,eAAe,CAAC,SAAS,SAAS,WAAW,SAAS,QAAQ;AAClE,aAAS,gBAAgB,SAAS,OAAO,IAAI;AAG3C,UAAI,OAAO,QAAQ,oBAAoB,WAAY,QAAO,QAAQ,gBAAgB,OAAO,EAAE;AAM3F,UAAI,CAAC,QAAQ,WAAW,CAAC,QAAQ,QAAQ,KAAK,EAAG,SAAQ,GAAG,OAAO,EAAE;AAAA,eAAW,MAAM,QAAQ,QAAQ,QAAQ,KAAK,CAAC,EAAG,SAAQ,QAAQ,KAAK,EAAE,QAAQ,EAAE;AAAA,UAAO,SAAQ,QAAQ,KAAK,IAAI,CAAC,IAAI,QAAQ,QAAQ,KAAK,CAAC;AAAA,IACrN;AACA,aAAS,cAAc,SAAS,QAAQ,UAAU;AAChD,eAAS,UAAU;AACnB,gBAAU,WAAW,CAAC;AAOtB,UAAI,OAAO,aAAa,UAAW,YAAW,kBAAkB;AAIhE,WAAK,aAAa,CAAC,CAAC,QAAQ;AAC5B,UAAI,SAAU,MAAK,aAAa,KAAK,cAAc,CAAC,CAAC,QAAQ;AAI7D,WAAK,gBAAgB,iBAAiB,MAAM,SAAS,yBAAyB,QAAQ;AAKtF,WAAK,SAAS,IAAI,WAAW;AAC7B,WAAK,SAAS;AACd,WAAK,QAAQ;AACb,WAAK,aAAa;AAClB,WAAK,UAAU;AACf,WAAK,QAAQ;AACb,WAAK,aAAa;AAClB,WAAK,UAAU;AAMf,WAAK,OAAO;AAIZ,WAAK,eAAe;AACpB,WAAK,kBAAkB;AACvB,WAAK,oBAAoB;AACzB,WAAK,kBAAkB;AACvB,WAAK,SAAS;AAGd,WAAK,YAAY,QAAQ,cAAc;AAGvC,WAAK,cAAc,CAAC,CAAC,QAAQ;AAG7B,WAAK,YAAY;AAKjB,WAAK,kBAAkB,QAAQ,mBAAmB;AAGlD,WAAK,aAAa;AAGlB,WAAK,cAAc;AACnB,WAAK,UAAU;AACf,WAAK,WAAW;AAChB,UAAI,QAAQ,UAAU;AACpB,YAAI,CAAC,cAAe,iBAAgB,yBAA2B;AAC/D,aAAK,UAAU,IAAI,cAAc,QAAQ,QAAQ;AACjD,aAAK,WAAW,QAAQ;AAAA,MAC1B;AAAA,IACF;AACA,aAASA,UAAS,SAAS;AACzB,eAAS,UAAU;AACnB,UAAI,EAAE,gBAAgBA,WAAW,QAAO,IAAIA,UAAS,OAAO;AAI5D,UAAI,WAAW,gBAAgB;AAC/B,WAAK,iBAAiB,IAAI,cAAc,SAAS,MAAM,QAAQ;AAG/D,WAAK,WAAW;AAChB,UAAI,SAAS;AACX,YAAI,OAAO,QAAQ,SAAS,WAAY,MAAK,QAAQ,QAAQ;AAC7D,YAAI,OAAO,QAAQ,YAAY,WAAY,MAAK,WAAW,QAAQ;AAAA,MACrE;AACA,aAAO,KAAK,IAAI;AAAA,IAClB;AACA,WAAO,eAAeA,UAAS,WAAW,aAAa;AAAA;AAAA;AAAA;AAAA,MAIrD,YAAY;AAAA,MACZ,KAAK,SAAS,MAAM;AAClB,YAAI,KAAK,mBAAmB,QAAW;AACrC,iBAAO;AAAA,QACT;AACA,eAAO,KAAK,eAAe;AAAA,MAC7B;AAAA,MACA,KAAK,SAAS,IAAI,OAAO;AAGvB,YAAI,CAAC,KAAK,gBAAgB;AACxB;AAAA,QACF;AAIA,aAAK,eAAe,YAAY;AAAA,MAClC;AAAA,IACF,CAAC;AACD,IAAAA,UAAS,UAAU,UAAU,YAAY;AACzC,IAAAA,UAAS,UAAU,aAAa,YAAY;AAC5C,IAAAA,UAAS,UAAU,WAAW,SAAU,KAAK,IAAI;AAC/C,SAAG,GAAG;AAAA,IACR;AAMA,IAAAA,UAAS,UAAU,OAAO,SAAU,OAAO,UAAU;AACnD,UAAI,QAAQ,KAAK;AACjB,UAAI;AACJ,UAAI,CAAC,MAAM,YAAY;AACrB,YAAI,OAAO,UAAU,UAAU;AAC7B,qBAAW,YAAY,MAAM;AAC7B,cAAI,aAAa,MAAM,UAAU;AAC/B,oBAAQE,QAAO,KAAK,OAAO,QAAQ;AACnC,uBAAW;AAAA,UACb;AACA,2BAAiB;AAAA,QACnB;AAAA,MACF,OAAO;AACL,yBAAiB;AAAA,MACnB;AACA,aAAO,iBAAiB,MAAM,OAAO,UAAU,OAAO,cAAc;AAAA,IACtE;AAGA,IAAAF,UAAS,UAAU,UAAU,SAAU,OAAO;AAC5C,aAAO,iBAAiB,MAAM,OAAO,MAAM,MAAM,KAAK;AAAA,IACxD;AACA,aAAS,iBAAiB,QAAQ,OAAO,UAAU,YAAY,gBAAgB;AAC7E,YAAM,oBAAoB,KAAK;AAC/B,UAAI,QAAQ,OAAO;AACnB,UAAI,UAAU,MAAM;AAClB,cAAM,UAAU;AAChB,mBAAW,QAAQ,KAAK;AAAA,MAC1B,OAAO;AACL,YAAI;AACJ,YAAI,CAAC,eAAgB,MAAK,aAAa,OAAO,KAAK;AACnD,YAAI,IAAI;AACN,yBAAe,QAAQ,EAAE;AAAA,QAC3B,WAAW,MAAM,cAAc,SAAS,MAAM,SAAS,GAAG;AACxD,cAAI,OAAO,UAAU,YAAY,CAAC,MAAM,cAAc,OAAO,eAAe,KAAK,MAAME,QAAO,WAAW;AACvG,oBAAQ,oBAAoB,KAAK;AAAA,UACnC;AACA,cAAI,YAAY;AACd,gBAAI,MAAM,WAAY,gBAAe,QAAQ,IAAI,mCAAmC,CAAC;AAAA,gBAAO,UAAS,QAAQ,OAAO,OAAO,IAAI;AAAA,UACjI,WAAW,MAAM,OAAO;AACtB,2BAAe,QAAQ,IAAI,0BAA0B,CAAC;AAAA,UACxD,WAAW,MAAM,WAAW;AAC1B,mBAAO;AAAA,UACT,OAAO;AACL,kBAAM,UAAU;AAChB,gBAAI,MAAM,WAAW,CAAC,UAAU;AAC9B,sBAAQ,MAAM,QAAQ,MAAM,KAAK;AACjC,kBAAI,MAAM,cAAc,MAAM,WAAW,EAAG,UAAS,QAAQ,OAAO,OAAO,KAAK;AAAA,kBAAO,eAAc,QAAQ,KAAK;AAAA,YACpH,OAAO;AACL,uBAAS,QAAQ,OAAO,OAAO,KAAK;AAAA,YACtC;AAAA,UACF;AAAA,QACF,WAAW,CAAC,YAAY;AACtB,gBAAM,UAAU;AAChB,wBAAc,QAAQ,KAAK;AAAA,QAC7B;AAAA,MACF;AAKA,aAAO,CAAC,MAAM,UAAU,MAAM,SAAS,MAAM,iBAAiB,MAAM,WAAW;AAAA,IACjF;AACA,aAAS,SAAS,QAAQ,OAAO,OAAO,YAAY;AAClD,UAAI,MAAM,WAAW,MAAM,WAAW,KAAK,CAAC,MAAM,MAAM;AACtD,cAAM,aAAa;AACnB,eAAO,KAAK,QAAQ,KAAK;AAAA,MAC3B,OAAO;AAEL,cAAM,UAAU,MAAM,aAAa,IAAI,MAAM;AAC7C,YAAI,WAAY,OAAM,OAAO,QAAQ,KAAK;AAAA,YAAO,OAAM,OAAO,KAAK,KAAK;AACxE,YAAI,MAAM,aAAc,cAAa,MAAM;AAAA,MAC7C;AACA,oBAAc,QAAQ,KAAK;AAAA,IAC7B;AACA,aAAS,aAAa,OAAO,OAAO;AAClC,UAAI;AACJ,UAAI,CAAC,cAAc,KAAK,KAAK,OAAO,UAAU,YAAY,UAAU,UAAa,CAAC,MAAM,YAAY;AAClG,aAAK,IAAI,qBAAqB,SAAS,CAAC,UAAU,UAAU,YAAY,GAAG,KAAK;AAAA,MAClF;AACA,aAAO;AAAA,IACT;AACA,IAAAF,UAAS,UAAU,WAAW,WAAY;AACxC,aAAO,KAAK,eAAe,YAAY;AAAA,IACzC;AAGA,IAAAA,UAAS,UAAU,cAAc,SAAU,KAAK;AAC9C,UAAI,CAAC,cAAe,iBAAgB,yBAA2B;AAC/D,UAAI,UAAU,IAAI,cAAc,GAAG;AACnC,WAAK,eAAe,UAAU;AAE9B,WAAK,eAAe,WAAW,KAAK,eAAe,QAAQ;AAG3D,UAAI,IAAI,KAAK,eAAe,OAAO;AACnC,UAAI,UAAU;AACd,aAAO,MAAM,MAAM;AACjB,mBAAW,QAAQ,MAAM,EAAE,IAAI;AAC/B,YAAI,EAAE;AAAA,MACR;AACA,WAAK,eAAe,OAAO,MAAM;AACjC,UAAI,YAAY,GAAI,MAAK,eAAe,OAAO,KAAK,OAAO;AAC3D,WAAK,eAAe,SAAS,QAAQ;AACrC,aAAO;AAAA,IACT;AAGA,QAAI,UAAU;AACd,aAAS,wBAAwB,GAAG;AAClC,UAAI,KAAK,SAAS;AAEhB,YAAI;AAAA,MACN,OAAO;AAGL;AACA,aAAK,MAAM;AACX,aAAK,MAAM;AACX,aAAK,MAAM;AACX,aAAK,MAAM;AACX,aAAK,MAAM;AACX;AAAA,MACF;AACA,aAAO;AAAA,IACT;AAIA,aAAS,cAAc,GAAG,OAAO;AAC/B,UAAI,KAAK,KAAK,MAAM,WAAW,KAAK,MAAM,MAAO,QAAO;AACxD,UAAI,MAAM,WAAY,QAAO;AAC7B,UAAI,MAAM,GAAG;AAEX,YAAI,MAAM,WAAW,MAAM,OAAQ,QAAO,MAAM,OAAO,KAAK,KAAK;AAAA,YAAY,QAAO,MAAM;AAAA,MAC5F;AAEA,UAAI,IAAI,MAAM,cAAe,OAAM,gBAAgB,wBAAwB,CAAC;AAC5E,UAAI,KAAK,MAAM,OAAQ,QAAO;AAE9B,UAAI,CAAC,MAAM,OAAO;AAChB,cAAM,eAAe;AACrB,eAAO;AAAA,MACT;AACA,aAAO,MAAM;AAAA,IACf;AAGA,IAAAA,UAAS,UAAU,OAAO,SAAU,GAAG;AACrC,YAAM,QAAQ,CAAC;AACf,UAAI,SAAS,GAAG,EAAE;AAClB,UAAI,QAAQ,KAAK;AACjB,UAAI,QAAQ;AACZ,UAAI,MAAM,EAAG,OAAM,kBAAkB;AAKrC,UAAI,MAAM,KAAK,MAAM,kBAAkB,MAAM,kBAAkB,IAAI,MAAM,UAAU,MAAM,gBAAgB,MAAM,SAAS,MAAM,MAAM,QAAQ;AAC1I,cAAM,sBAAsB,MAAM,QAAQ,MAAM,KAAK;AACrD,YAAI,MAAM,WAAW,KAAK,MAAM,MAAO,aAAY,IAAI;AAAA,YAAO,cAAa,IAAI;AAC/E,eAAO;AAAA,MACT;AACA,UAAI,cAAc,GAAG,KAAK;AAG1B,UAAI,MAAM,KAAK,MAAM,OAAO;AAC1B,YAAI,MAAM,WAAW,EAAG,aAAY,IAAI;AACxC,eAAO;AAAA,MACT;AAyBA,UAAI,SAAS,MAAM;AACnB,YAAM,iBAAiB,MAAM;AAG7B,UAAI,MAAM,WAAW,KAAK,MAAM,SAAS,IAAI,MAAM,eAAe;AAChE,iBAAS;AACT,cAAM,8BAA8B,MAAM;AAAA,MAC5C;AAIA,UAAI,MAAM,SAAS,MAAM,SAAS;AAChC,iBAAS;AACT,cAAM,oBAAoB,MAAM;AAAA,MAClC,WAAW,QAAQ;AACjB,cAAM,SAAS;AACf,cAAM,UAAU;AAChB,cAAM,OAAO;AAEb,YAAI,MAAM,WAAW,EAAG,OAAM,eAAe;AAE7C,aAAK,MAAM,MAAM,aAAa;AAC9B,cAAM,OAAO;AAGb,YAAI,CAAC,MAAM,QAAS,KAAI,cAAc,OAAO,KAAK;AAAA,MACpD;AACA,UAAI;AACJ,UAAI,IAAI,EAAG,OAAM,SAAS,GAAG,KAAK;AAAA,UAAO,OAAM;AAC/C,UAAI,QAAQ,MAAM;AAChB,cAAM,eAAe,MAAM,UAAU,MAAM;AAC3C,YAAI;AAAA,MACN,OAAO;AACL,cAAM,UAAU;AAChB,cAAM,aAAa;AAAA,MACrB;AACA,UAAI,MAAM,WAAW,GAAG;AAGtB,YAAI,CAAC,MAAM,MAAO,OAAM,eAAe;AAGvC,YAAI,UAAU,KAAK,MAAM,MAAO,aAAY,IAAI;AAAA,MAClD;AACA,UAAI,QAAQ,KAAM,MAAK,KAAK,QAAQ,GAAG;AACvC,aAAO;AAAA,IACT;AACA,aAAS,WAAW,QAAQ,OAAO;AACjC,YAAM,YAAY;AAClB,UAAI,MAAM,MAAO;AACjB,UAAI,MAAM,SAAS;AACjB,YAAI,QAAQ,MAAM,QAAQ,IAAI;AAC9B,YAAI,SAAS,MAAM,QAAQ;AACzB,gBAAM,OAAO,KAAK,KAAK;AACvB,gBAAM,UAAU,MAAM,aAAa,IAAI,MAAM;AAAA,QAC/C;AAAA,MACF;AACA,YAAM,QAAQ;AACd,UAAI,MAAM,MAAM;AAId,qBAAa,MAAM;AAAA,MACrB,OAAO;AAEL,cAAM,eAAe;AACrB,YAAI,CAAC,MAAM,iBAAiB;AAC1B,gBAAM,kBAAkB;AACxB,wBAAc,MAAM;AAAA,QACtB;AAAA,MACF;AAAA,IACF;AAKA,aAAS,aAAa,QAAQ;AAC5B,UAAI,QAAQ,OAAO;AACnB,YAAM,gBAAgB,MAAM,cAAc,MAAM,eAAe;AAC/D,YAAM,eAAe;AACrB,UAAI,CAAC,MAAM,iBAAiB;AAC1B,cAAM,gBAAgB,MAAM,OAAO;AACnC,cAAM,kBAAkB;AACxB,gBAAQ,SAAS,eAAe,MAAM;AAAA,MACxC;AAAA,IACF;AACA,aAAS,cAAc,QAAQ;AAC7B,UAAI,QAAQ,OAAO;AACnB,YAAM,iBAAiB,MAAM,WAAW,MAAM,QAAQ,MAAM,KAAK;AACjE,UAAI,CAAC,MAAM,cAAc,MAAM,UAAU,MAAM,QAAQ;AACrD,eAAO,KAAK,UAAU;AACtB,cAAM,kBAAkB;AAAA,MAC1B;AAQA,YAAM,eAAe,CAAC,MAAM,WAAW,CAAC,MAAM,SAAS,MAAM,UAAU,MAAM;AAC7E,WAAK,MAAM;AAAA,IACb;AAQA,aAAS,cAAc,QAAQ,OAAO;AACpC,UAAI,CAAC,MAAM,aAAa;AACtB,cAAM,cAAc;AACpB,gBAAQ,SAAS,gBAAgB,QAAQ,KAAK;AAAA,MAChD;AAAA,IACF;AACA,aAAS,eAAe,QAAQ,OAAO;AAwBrC,aAAO,CAAC,MAAM,WAAW,CAAC,MAAM,UAAU,MAAM,SAAS,MAAM,iBAAiB,MAAM,WAAW,MAAM,WAAW,IAAI;AACpH,YAAI,MAAM,MAAM;AAChB,cAAM,sBAAsB;AAC5B,eAAO,KAAK,CAAC;AACb,YAAI,QAAQ,MAAM;AAEhB;AAAA,MACJ;AACA,YAAM,cAAc;AAAA,IACtB;AAMA,IAAAA,UAAS,UAAU,QAAQ,SAAU,GAAG;AACtC,qBAAe,MAAM,IAAI,2BAA2B,SAAS,CAAC;AAAA,IAChE;AACA,IAAAA,UAAS,UAAU,OAAO,SAAU,MAAM,UAAU;AAClD,UAAI,MAAM;AACV,UAAI,QAAQ,KAAK;AACjB,cAAQ,MAAM,YAAY;AAAA,QACxB,KAAK;AACH,gBAAM,QAAQ;AACd;AAAA,QACF,KAAK;AACH,gBAAM,QAAQ,CAAC,MAAM,OAAO,IAAI;AAChC;AAAA,QACF;AACE,gBAAM,MAAM,KAAK,IAAI;AACrB;AAAA,MACJ;AACA,YAAM,cAAc;AACpB,YAAM,yBAAyB,MAAM,YAAY,QAAQ;AACzD,UAAI,SAAS,CAAC,YAAY,SAAS,QAAQ,UAAU,SAAS,QAAQ,UAAU,SAAS,QAAQ;AACjG,UAAI,QAAQ,QAAQ,QAAQ;AAC5B,UAAI,MAAM,WAAY,SAAQ,SAAS,KAAK;AAAA,UAAO,KAAI,KAAK,OAAO,KAAK;AACxE,WAAK,GAAG,UAAU,QAAQ;AAC1B,eAAS,SAAS,UAAU,YAAY;AACtC,cAAM,UAAU;AAChB,YAAI,aAAa,KAAK;AACpB,cAAI,cAAc,WAAW,eAAe,OAAO;AACjD,uBAAW,aAAa;AACxB,oBAAQ;AAAA,UACV;AAAA,QACF;AAAA,MACF;AACA,eAAS,QAAQ;AACf,cAAM,OAAO;AACb,aAAK,IAAI;AAAA,MACX;AAMA,UAAI,UAAU,YAAY,GAAG;AAC7B,WAAK,GAAG,SAAS,OAAO;AACxB,UAAI,YAAY;AAChB,eAAS,UAAU;AACjB,cAAM,SAAS;AAEf,aAAK,eAAe,SAAS,OAAO;AACpC,aAAK,eAAe,UAAU,QAAQ;AACtC,aAAK,eAAe,SAAS,OAAO;AACpC,aAAK,eAAe,SAAS,OAAO;AACpC,aAAK,eAAe,UAAU,QAAQ;AACtC,YAAI,eAAe,OAAO,KAAK;AAC/B,YAAI,eAAe,OAAO,MAAM;AAChC,YAAI,eAAe,QAAQ,MAAM;AACjC,oBAAY;AAOZ,YAAI,MAAM,eAAe,CAAC,KAAK,kBAAkB,KAAK,eAAe,WAAY,SAAQ;AAAA,MAC3F;AACA,UAAI,GAAG,QAAQ,MAAM;AACrB,eAAS,OAAO,OAAO;AACrB,cAAM,QAAQ;AACd,YAAI,MAAM,KAAK,MAAM,KAAK;AAC1B,cAAM,cAAc,GAAG;AACvB,YAAI,QAAQ,OAAO;AAKjB,eAAK,MAAM,eAAe,KAAK,MAAM,UAAU,QAAQ,MAAM,aAAa,KAAK,QAAQ,MAAM,OAAO,IAAI,MAAM,OAAO,CAAC,WAAW;AAC/H,kBAAM,+BAA+B,MAAM,UAAU;AACrD,kBAAM;AAAA,UACR;AACA,cAAI,MAAM;AAAA,QACZ;AAAA,MACF;AAIA,eAAS,QAAQ,IAAI;AACnB,cAAM,WAAW,EAAE;AACnB,eAAO;AACP,aAAK,eAAe,SAAS,OAAO;AACpC,YAAI,gBAAgB,MAAM,OAAO,MAAM,EAAG,gBAAe,MAAM,EAAE;AAAA,MACnE;AAGA,sBAAgB,MAAM,SAAS,OAAO;AAGtC,eAAS,UAAU;AACjB,aAAK,eAAe,UAAU,QAAQ;AACtC,eAAO;AAAA,MACT;AACA,WAAK,KAAK,SAAS,OAAO;AAC1B,eAAS,WAAW;AAClB,cAAM,UAAU;AAChB,aAAK,eAAe,SAAS,OAAO;AACpC,eAAO;AAAA,MACT;AACA,WAAK,KAAK,UAAU,QAAQ;AAC5B,eAAS,SAAS;AAChB,cAAM,QAAQ;AACd,YAAI,OAAO,IAAI;AAAA,MACjB;AAGA,WAAK,KAAK,QAAQ,GAAG;AAGrB,UAAI,CAAC,MAAM,SAAS;AAClB,cAAM,aAAa;AACnB,YAAI,OAAO;AAAA,MACb;AACA,aAAO;AAAA,IACT;AACA,aAAS,YAAY,KAAK;AACxB,aAAO,SAAS,4BAA4B;AAC1C,YAAI,QAAQ,IAAI;AAChB,cAAM,eAAe,MAAM,UAAU;AACrC,YAAI,MAAM,WAAY,OAAM;AAC5B,YAAI,MAAM,eAAe,KAAK,gBAAgB,KAAK,MAAM,GAAG;AAC1D,gBAAM,UAAU;AAChB,eAAK,GAAG;AAAA,QACV;AAAA,MACF;AAAA,IACF;AACA,IAAAA,UAAS,UAAU,SAAS,SAAU,MAAM;AAC1C,UAAI,QAAQ,KAAK;AACjB,UAAI,aAAa;AAAA,QACf,YAAY;AAAA,MACd;AAGA,UAAI,MAAM,eAAe,EAAG,QAAO;AAGnC,UAAI,MAAM,eAAe,GAAG;AAE1B,YAAI,QAAQ,SAAS,MAAM,MAAO,QAAO;AACzC,YAAI,CAAC,KAAM,QAAO,MAAM;AAGxB,cAAM,QAAQ;AACd,cAAM,aAAa;AACnB,cAAM,UAAU;AAChB,YAAI,KAAM,MAAK,KAAK,UAAU,MAAM,UAAU;AAC9C,eAAO;AAAA,MACT;AAIA,UAAI,CAAC,MAAM;AAET,YAAI,QAAQ,MAAM;AAClB,YAAI,MAAM,MAAM;AAChB,cAAM,QAAQ;AACd,cAAM,aAAa;AACnB,cAAM,UAAU;AAChB,iBAAS,IAAI,GAAG,IAAI,KAAK,IAAK,OAAM,CAAC,EAAE,KAAK,UAAU,MAAM;AAAA,UAC1D,YAAY;AAAA,QACd,CAAC;AACD,eAAO;AAAA,MACT;AAGA,UAAI,QAAQ,QAAQ,MAAM,OAAO,IAAI;AACrC,UAAI,UAAU,GAAI,QAAO;AACzB,YAAM,MAAM,OAAO,OAAO,CAAC;AAC3B,YAAM,cAAc;AACpB,UAAI,MAAM,eAAe,EAAG,OAAM,QAAQ,MAAM,MAAM,CAAC;AACvD,WAAK,KAAK,UAAU,MAAM,UAAU;AACpC,aAAO;AAAA,IACT;AAIA,IAAAA,UAAS,UAAU,KAAK,SAAU,IAAI,IAAI;AACxC,UAAI,MAAM,OAAO,UAAU,GAAG,KAAK,MAAM,IAAI,EAAE;AAC/C,UAAI,QAAQ,KAAK;AACjB,UAAI,OAAO,QAAQ;AAGjB,cAAM,oBAAoB,KAAK,cAAc,UAAU,IAAI;AAG3D,YAAI,MAAM,YAAY,MAAO,MAAK,OAAO;AAAA,MAC3C,WAAW,OAAO,YAAY;AAC5B,YAAI,CAAC,MAAM,cAAc,CAAC,MAAM,mBAAmB;AACjD,gBAAM,oBAAoB,MAAM,eAAe;AAC/C,gBAAM,UAAU;AAChB,gBAAM,kBAAkB;AACxB,gBAAM,eAAe,MAAM,QAAQ,MAAM,OAAO;AAChD,cAAI,MAAM,QAAQ;AAChB,yBAAa,IAAI;AAAA,UACnB,WAAW,CAAC,MAAM,SAAS;AACzB,oBAAQ,SAAS,kBAAkB,IAAI;AAAA,UACzC;AAAA,QACF;AAAA,MACF;AACA,aAAO;AAAA,IACT;AACA,IAAAA,UAAS,UAAU,cAAcA,UAAS,UAAU;AACpD,IAAAA,UAAS,UAAU,iBAAiB,SAAU,IAAI,IAAI;AACpD,UAAI,MAAM,OAAO,UAAU,eAAe,KAAK,MAAM,IAAI,EAAE;AAC3D,UAAI,OAAO,YAAY;AAOrB,gBAAQ,SAAS,yBAAyB,IAAI;AAAA,MAChD;AACA,aAAO;AAAA,IACT;AACA,IAAAA,UAAS,UAAU,qBAAqB,SAAU,IAAI;AACpD,UAAI,MAAM,OAAO,UAAU,mBAAmB,MAAM,MAAM,SAAS;AACnE,UAAI,OAAO,cAAc,OAAO,QAAW;AAOzC,gBAAQ,SAAS,yBAAyB,IAAI;AAAA,MAChD;AACA,aAAO;AAAA,IACT;AACA,aAAS,wBAAwBI,OAAM;AACrC,UAAI,QAAQA,MAAK;AACjB,YAAM,oBAAoBA,MAAK,cAAc,UAAU,IAAI;AAC3D,UAAI,MAAM,mBAAmB,CAAC,MAAM,QAAQ;AAG1C,cAAM,UAAU;AAAA,MAGlB,WAAWA,MAAK,cAAc,MAAM,IAAI,GAAG;AACzC,QAAAA,MAAK,OAAO;AAAA,MACd;AAAA,IACF;AACA,aAAS,iBAAiBA,OAAM;AAC9B,YAAM,0BAA0B;AAChC,MAAAA,MAAK,KAAK,CAAC;AAAA,IACb;AAIA,IAAAJ,UAAS,UAAU,SAAS,WAAY;AACtC,UAAI,QAAQ,KAAK;AACjB,UAAI,CAAC,MAAM,SAAS;AAClB,cAAM,QAAQ;AAId,cAAM,UAAU,CAAC,MAAM;AACvB,eAAO,MAAM,KAAK;AAAA,MACpB;AACA,YAAM,SAAS;AACf,aAAO;AAAA,IACT;AACA,aAAS,OAAO,QAAQ,OAAO;AAC7B,UAAI,CAAC,MAAM,iBAAiB;AAC1B,cAAM,kBAAkB;AACxB,gBAAQ,SAAS,SAAS,QAAQ,KAAK;AAAA,MACzC;AAAA,IACF;AACA,aAAS,QAAQ,QAAQ,OAAO;AAC9B,YAAM,UAAU,MAAM,OAAO;AAC7B,UAAI,CAAC,MAAM,SAAS;AAClB,eAAO,KAAK,CAAC;AAAA,MACf;AACA,YAAM,kBAAkB;AACxB,aAAO,KAAK,QAAQ;AACpB,WAAK,MAAM;AACX,UAAI,MAAM,WAAW,CAAC,MAAM,QAAS,QAAO,KAAK,CAAC;AAAA,IACpD;AACA,IAAAA,UAAS,UAAU,QAAQ,WAAY;AACrC,YAAM,yBAAyB,KAAK,eAAe,OAAO;AAC1D,UAAI,KAAK,eAAe,YAAY,OAAO;AACzC,cAAM,OAAO;AACb,aAAK,eAAe,UAAU;AAC9B,aAAK,KAAK,OAAO;AAAA,MACnB;AACA,WAAK,eAAe,SAAS;AAC7B,aAAO;AAAA,IACT;AACA,aAAS,KAAK,QAAQ;AACpB,UAAI,QAAQ,OAAO;AACnB,YAAM,QAAQ,MAAM,OAAO;AAC3B,aAAO,MAAM,WAAW,OAAO,KAAK,MAAM,KAAK;AAAA,IACjD;AAKA,IAAAA,UAAS,UAAU,OAAO,SAAU,QAAQ;AAC1C,UAAI,QAAQ;AACZ,UAAI,QAAQ,KAAK;AACjB,UAAI,SAAS;AACb,aAAO,GAAG,OAAO,WAAY;AAC3B,cAAM,aAAa;AACnB,YAAI,MAAM,WAAW,CAAC,MAAM,OAAO;AACjC,cAAI,QAAQ,MAAM,QAAQ,IAAI;AAC9B,cAAI,SAAS,MAAM,OAAQ,OAAM,KAAK,KAAK;AAAA,QAC7C;AACA,cAAM,KAAK,IAAI;AAAA,MACjB,CAAC;AACD,aAAO,GAAG,QAAQ,SAAU,OAAO;AACjC,cAAM,cAAc;AACpB,YAAI,MAAM,QAAS,SAAQ,MAAM,QAAQ,MAAM,KAAK;AAGpD,YAAI,MAAM,eAAe,UAAU,QAAQ,UAAU,QAAY;AAAA,iBAAgB,CAAC,MAAM,eAAe,CAAC,SAAS,CAAC,MAAM,QAAS;AACjI,YAAI,MAAM,MAAM,KAAK,KAAK;AAC1B,YAAI,CAAC,KAAK;AACR,mBAAS;AACT,iBAAO,MAAM;AAAA,QACf;AAAA,MACF,CAAC;AAID,eAAS,KAAK,QAAQ;AACpB,YAAI,KAAK,CAAC,MAAM,UAAa,OAAO,OAAO,CAAC,MAAM,YAAY;AAC5D,eAAK,CAAC,IAAI,yBAAS,WAAW,QAAQ;AACpC,mBAAO,SAAS,2BAA2B;AACzC,qBAAO,OAAO,MAAM,EAAE,MAAM,QAAQ,SAAS;AAAA,YAC/C;AAAA,UACF,EAAE,CAAC;AAAA,QACL;AAAA,MACF;AAGA,eAAS,IAAI,GAAG,IAAI,aAAa,QAAQ,KAAK;AAC5C,eAAO,GAAG,aAAa,CAAC,GAAG,KAAK,KAAK,KAAK,MAAM,aAAa,CAAC,CAAC,CAAC;AAAA,MAClE;AAIA,WAAK,QAAQ,SAAUK,IAAG;AACxB,cAAM,iBAAiBA,EAAC;AACxB,YAAI,QAAQ;AACV,mBAAS;AACT,iBAAO,OAAO;AAAA,QAChB;AAAA,MACF;AACA,aAAO;AAAA,IACT;AACA,QAAI,OAAO,WAAW,YAAY;AAChC,MAAAL,UAAS,UAAU,OAAO,aAAa,IAAI,WAAY;AACrD,YAAI,sCAAsC,QAAW;AACnD,8CAAoC;AAAA,QACtC;AACA,eAAO,kCAAkC,IAAI;AAAA,MAC/C;AAAA,IACF;AACA,WAAO,eAAeA,UAAS,WAAW,yBAAyB;AAAA;AAAA;AAAA;AAAA,MAIjE,YAAY;AAAA,MACZ,KAAK,SAAS,MAAM;AAClB,eAAO,KAAK,eAAe;AAAA,MAC7B;AAAA,IACF,CAAC;AACD,WAAO,eAAeA,UAAS,WAAW,kBAAkB;AAAA;AAAA;AAAA;AAAA,MAI1D,YAAY;AAAA,MACZ,KAAK,SAAS,MAAM;AAClB,eAAO,KAAK,kBAAkB,KAAK,eAAe;AAAA,MACpD;AAAA,IACF,CAAC;AACD,WAAO,eAAeA,UAAS,WAAW,mBAAmB;AAAA;AAAA;AAAA;AAAA,MAI3D,YAAY;AAAA,MACZ,KAAK,SAAS,MAAM;AAClB,eAAO,KAAK,eAAe;AAAA,MAC7B;AAAA,MACA,KAAK,SAAS,IAAI,OAAO;AACvB,YAAI,KAAK,gBAAgB;AACvB,eAAK,eAAe,UAAU;AAAA,QAChC;AAAA,MACF;AAAA,IACF,CAAC;AAGD,IAAAA,UAAS,YAAY;AACrB,WAAO,eAAeA,UAAS,WAAW,kBAAkB;AAAA;AAAA;AAAA;AAAA,MAI1D,YAAY;AAAA,MACZ,KAAK,SAAS,MAAM;AAClB,eAAO,KAAK,eAAe;AAAA,MAC7B;AAAA,IACF,CAAC;AAMD,aAAS,SAAS,GAAG,OAAO;AAE1B,UAAI,MAAM,WAAW,EAAG,QAAO;AAC/B,UAAI;AACJ,UAAI,MAAM,WAAY,OAAM,MAAM,OAAO,MAAM;AAAA,eAAW,CAAC,KAAK,KAAK,MAAM,QAAQ;AAEjF,YAAI,MAAM,QAAS,OAAM,MAAM,OAAO,KAAK,EAAE;AAAA,iBAAW,MAAM,OAAO,WAAW,EAAG,OAAM,MAAM,OAAO,MAAM;AAAA,YAAO,OAAM,MAAM,OAAO,OAAO,MAAM,MAAM;AACzJ,cAAM,OAAO,MAAM;AAAA,MACrB,OAAO;AAEL,cAAM,MAAM,OAAO,QAAQ,GAAG,MAAM,OAAO;AAAA,MAC7C;AACA,aAAO;AAAA,IACT;AACA,aAAS,YAAY,QAAQ;AAC3B,UAAI,QAAQ,OAAO;AACnB,YAAM,eAAe,MAAM,UAAU;AACrC,UAAI,CAAC,MAAM,YAAY;AACrB,cAAM,QAAQ;AACd,gBAAQ,SAAS,eAAe,OAAO,MAAM;AAAA,MAC/C;AAAA,IACF;AACA,aAAS,cAAc,OAAO,QAAQ;AACpC,YAAM,iBAAiB,MAAM,YAAY,MAAM,MAAM;AAGrD,UAAI,CAAC,MAAM,cAAc,MAAM,WAAW,GAAG;AAC3C,cAAM,aAAa;AACnB,eAAO,WAAW;AAClB,eAAO,KAAK,KAAK;AACjB,YAAI,MAAM,aAAa;AAGrB,cAAI,SAAS,OAAO;AACpB,cAAI,CAAC,UAAU,OAAO,eAAe,OAAO,UAAU;AACpD,mBAAO,QAAQ;AAAA,UACjB;AAAA,QACF;AAAA,MACF;AAAA,IACF;AACA,QAAI,OAAO,WAAW,YAAY;AAChC,MAAAA,UAAS,OAAO,SAAU,UAAU,MAAM;AACxC,YAAI,SAAS,QAAW;AACtB,iBAAO;AAAA,QACT;AACA,eAAO,KAAKA,WAAU,UAAU,IAAI;AAAA,MACtC;AAAA,IACF;AACA,aAAS,QAAQ,IAAI,GAAG;AACtB,eAAS,IAAI,GAAG,IAAI,GAAG,QAAQ,IAAI,GAAG,KAAK;AACzC,YAAI,GAAG,CAAC,MAAM,EAAG,QAAO;AAAA,MAC1B;AACA,aAAO;AAAA,IACT;AAAA;AAAA;;;AClgCA;AAAA;AAAA;AAiEA,WAAO,UAAU;AACjB,QAAI,iBAAiB,yBAAqB;AAA1C,QACE,6BAA6B,eAAe;AAD9C,QAEE,wBAAwB,eAAe;AAFzC,QAGE,qCAAqC,eAAe;AAHtD,QAIE,8BAA8B,eAAe;AAC/C,QAAI,SAAS;AACb,+BAAoB,WAAW,MAAM;AACrC,aAAS,eAAe,IAAI,MAAM;AAChC,UAAI,KAAK,KAAK;AACd,SAAG,eAAe;AAClB,UAAI,KAAK,GAAG;AACZ,UAAI,OAAO,MAAM;AACf,eAAO,KAAK,KAAK,SAAS,IAAI,sBAAsB,CAAC;AAAA,MACvD;AACA,SAAG,aAAa;AAChB,SAAG,UAAU;AACb,UAAI,QAAQ;AAEV,aAAK,KAAK,IAAI;AAChB,SAAG,EAAE;AACL,UAAI,KAAK,KAAK;AACd,SAAG,UAAU;AACb,UAAI,GAAG,gBAAgB,GAAG,SAAS,GAAG,eAAe;AACnD,aAAK,MAAM,GAAG,aAAa;AAAA,MAC7B;AAAA,IACF;AACA,aAAS,UAAU,SAAS;AAC1B,UAAI,EAAE,gBAAgB,WAAY,QAAO,IAAI,UAAU,OAAO;AAC9D,aAAO,KAAK,MAAM,OAAO;AACzB,WAAK,kBAAkB;AAAA,QACrB,gBAAgB,eAAe,KAAK,IAAI;AAAA,QACxC,eAAe;AAAA,QACf,cAAc;AAAA,QACd,SAAS;AAAA,QACT,YAAY;AAAA,QACZ,eAAe;AAAA,MACjB;AAGA,WAAK,eAAe,eAAe;AAKnC,WAAK,eAAe,OAAO;AAC3B,UAAI,SAAS;AACX,YAAI,OAAO,QAAQ,cAAc,WAAY,MAAK,aAAa,QAAQ;AACvE,YAAI,OAAO,QAAQ,UAAU,WAAY,MAAK,SAAS,QAAQ;AAAA,MACjE;AAGA,WAAK,GAAG,aAAa,SAAS;AAAA,IAChC;AACA,aAAS,YAAY;AACnB,UAAI,QAAQ;AACZ,UAAI,OAAO,KAAK,WAAW,cAAc,CAAC,KAAK,eAAe,WAAW;AACvE,aAAK,OAAO,SAAU,IAAI,MAAM;AAC9B,eAAK,OAAO,IAAI,IAAI;AAAA,QACtB,CAAC;AAAA,MACH,OAAO;AACL,aAAK,MAAM,MAAM,IAAI;AAAA,MACvB;AAAA,IACF;AACA,cAAU,UAAU,OAAO,SAAU,OAAO,UAAU;AACpD,WAAK,gBAAgB,gBAAgB;AACrC,aAAO,OAAO,UAAU,KAAK,KAAK,MAAM,OAAO,QAAQ;AAAA,IACzD;AAYA,cAAU,UAAU,aAAa,SAAU,OAAO,UAAU,IAAI;AAC9D,SAAG,IAAI,2BAA2B,cAAc,CAAC;AAAA,IACnD;AACA,cAAU,UAAU,SAAS,SAAU,OAAO,UAAU,IAAI;AAC1D,UAAI,KAAK,KAAK;AACd,SAAG,UAAU;AACb,SAAG,aAAa;AAChB,SAAG,gBAAgB;AACnB,UAAI,CAAC,GAAG,cAAc;AACpB,YAAI,KAAK,KAAK;AACd,YAAI,GAAG,iBAAiB,GAAG,gBAAgB,GAAG,SAAS,GAAG,cAAe,MAAK,MAAM,GAAG,aAAa;AAAA,MACtG;AAAA,IACF;AAKA,cAAU,UAAU,QAAQ,SAAU,GAAG;AACvC,UAAI,KAAK,KAAK;AACd,UAAI,GAAG,eAAe,QAAQ,CAAC,GAAG,cAAc;AAC9C,WAAG,eAAe;AAClB,aAAK,WAAW,GAAG,YAAY,GAAG,eAAe,GAAG,cAAc;AAAA,MACpE,OAAO;AAGL,WAAG,gBAAgB;AAAA,MACrB;AAAA,IACF;AACA,cAAU,UAAU,WAAW,SAAU,KAAK,IAAI;AAChD,aAAO,UAAU,SAAS,KAAK,MAAM,KAAK,SAAU,MAAM;AACxD,WAAG,IAAI;AAAA,MACT,CAAC;AAAA,IACH;AACA,aAAS,KAAK,QAAQ,IAAI,MAAM;AAC9B,UAAI,GAAI,QAAO,OAAO,KAAK,SAAS,EAAE;AACtC,UAAI,QAAQ;AAEV,eAAO,KAAK,IAAI;AAKlB,UAAI,OAAO,eAAe,OAAQ,OAAM,IAAI,4BAA4B;AACxE,UAAI,OAAO,gBAAgB,aAAc,OAAM,IAAI,mCAAmC;AACtF,aAAO,OAAO,KAAK,IAAI;AAAA,IACzB;AAAA;AAAA;;;AC7LA;AAAA;AAAA;AA2BA,WAAO,UAAU;AACjB,QAAI,YAAY;AAChB,+BAAoB,aAAa,SAAS;AAC1C,aAAS,YAAY,SAAS;AAC5B,UAAI,EAAE,gBAAgB,aAAc,QAAO,IAAI,YAAY,OAAO;AAClE,gBAAU,KAAK,MAAM,OAAO;AAAA,IAC9B;AACA,gBAAY,UAAU,aAAa,SAAU,OAAO,UAAU,IAAI;AAChE,SAAG,MAAM,KAAK;AAAA,IAChB;AAAA;AAAA;;;ACpCA;AAAA;AAAA;AAKA,QAAI;AACJ,aAAS,KAAK,UAAU;AACtB,UAAI,SAAS;AACb,aAAO,WAAY;AACjB,YAAI,OAAQ;AACZ,iBAAS;AACT,iBAAS,MAAM,QAAQ,SAAS;AAAA,MAClC;AAAA,IACF;AACA,QAAI,iBAAiB,yBAA2B;AAAhD,QACE,mBAAmB,eAAe;AADpC,QAEE,uBAAuB,eAAe;AACxC,aAAS,KAAK,KAAK;AAEjB,UAAI,IAAK,OAAM;AAAA,IACjB;AACA,aAAS,UAAU,QAAQ;AACzB,aAAO,OAAO,aAAa,OAAO,OAAO,UAAU;AAAA,IACrD;AACA,aAAS,UAAU,QAAQ,SAAS,SAAS,UAAU;AACrD,iBAAW,KAAK,QAAQ;AACxB,UAAI,SAAS;AACb,aAAO,GAAG,SAAS,WAAY;AAC7B,iBAAS;AAAA,MACX,CAAC;AACD,UAAI,QAAQ,OAAW,OAAM;AAC7B,UAAI,QAAQ;AAAA,QACV,UAAU;AAAA,QACV,UAAU;AAAA,MACZ,GAAG,SAAU,KAAK;AAChB,YAAI,IAAK,QAAO,SAAS,GAAG;AAC5B,iBAAS;AACT,iBAAS;AAAA,MACX,CAAC;AACD,UAAI,YAAY;AAChB,aAAO,SAAU,KAAK;AACpB,YAAI,OAAQ;AACZ,YAAI,UAAW;AACf,oBAAY;AAGZ,YAAI,UAAU,MAAM,EAAG,QAAO,OAAO,MAAM;AAC3C,YAAI,OAAO,OAAO,YAAY,WAAY,QAAO,OAAO,QAAQ;AAChE,iBAAS,OAAO,IAAI,qBAAqB,MAAM,CAAC;AAAA,MAClD;AAAA,IACF;AACA,aAAS,KAAK,IAAI;AAChB,SAAG;AAAA,IACL;AACA,aAAS,KAAK,MAAM,IAAI;AACtB,aAAO,KAAK,KAAK,EAAE;AAAA,IACrB;AACA,aAAS,YAAY,SAAS;AAC5B,UAAI,CAAC,QAAQ,OAAQ,QAAO;AAC5B,UAAI,OAAO,QAAQ,QAAQ,SAAS,CAAC,MAAM,WAAY,QAAO;AAC9D,aAAO,QAAQ,IAAI;AAAA,IACrB;AACA,aAAS,WAAW;AAClB,eAAS,OAAO,UAAU,QAAQ,UAAU,IAAI,MAAM,IAAI,GAAG,OAAO,GAAG,OAAO,MAAM,QAAQ;AAC1F,gBAAQ,IAAI,IAAI,UAAU,IAAI;AAAA,MAChC;AACA,UAAI,WAAW,YAAY,OAAO;AAClC,UAAI,MAAM,QAAQ,QAAQ,CAAC,CAAC,EAAG,WAAU,QAAQ,CAAC;AAClD,UAAI,QAAQ,SAAS,GAAG;AACtB,cAAM,IAAI,iBAAiB,SAAS;AAAA,MACtC;AACA,UAAI;AACJ,UAAI,WAAW,QAAQ,IAAI,SAAU,QAAQ,GAAG;AAC9C,YAAI,UAAU,IAAI,QAAQ,SAAS;AACnC,YAAI,UAAU,IAAI;AAClB,eAAO,UAAU,QAAQ,SAAS,SAAS,SAAU,KAAK;AACxD,cAAI,CAAC,MAAO,SAAQ;AACpB,cAAI,IAAK,UAAS,QAAQ,IAAI;AAC9B,cAAI,QAAS;AACb,mBAAS,QAAQ,IAAI;AACrB,mBAAS,KAAK;AAAA,QAChB,CAAC;AAAA,MACH,CAAC;AACD,aAAO,QAAQ,OAAO,IAAI;AAAA,IAC5B;AACA,WAAO,UAAU;AAAA;AAAA;;;ACrFjB;AAAA;AAqBA,WAAO,UAAU;AAEjB,QAAI,KAAK,iBAAkB;AAC3B,QAAI,WAAW;AAEf,aAAS,QAAQ,EAAE;AACnB,WAAO,WAAW;AAClB,WAAO,WAAW;AAClB,WAAO,SAAS;AAChB,WAAO,YAAY;AACnB,WAAO,cAAc;AACrB,WAAO,WAAW;AAClB,WAAO,WAAW;AAGlB,WAAO,SAAS;AAOhB,aAAS,SAAS;AAChB,SAAG,KAAK,IAAI;AAAA,IACd;AAEA,WAAO,UAAU,OAAO,SAAS,MAAM,SAAS;AAC9C,UAAI,SAAS;AAEb,eAAS,OAAO,OAAO;AACrB,YAAI,KAAK,UAAU;AACjB,cAAI,UAAU,KAAK,MAAM,KAAK,KAAK,OAAO,OAAO;AAC/C,mBAAO,MAAM;AAAA,UACf;AAAA,QACF;AAAA,MACF;AAEA,aAAO,GAAG,QAAQ,MAAM;AAExB,eAAS,UAAU;AACjB,YAAI,OAAO,YAAY,OAAO,QAAQ;AACpC,iBAAO,OAAO;AAAA,QAChB;AAAA,MACF;AAEA,WAAK,GAAG,SAAS,OAAO;AAIxB,UAAI,CAAC,KAAK,aAAa,CAAC,WAAW,QAAQ,QAAQ,QAAQ;AACzD,eAAO,GAAG,OAAO,KAAK;AACtB,eAAO,GAAG,SAAS,OAAO;AAAA,MAC5B;AAEA,UAAI,WAAW;AACf,eAAS,QAAQ;AACf,YAAI,SAAU;AACd,mBAAW;AAEX,aAAK,IAAI;AAAA,MACX;AAGA,eAAS,UAAU;AACjB,YAAI,SAAU;AACd,mBAAW;AAEX,YAAI,OAAO,KAAK,YAAY,WAAY,MAAK,QAAQ;AAAA,MACvD;AAGA,eAAS,QAAQ,IAAI;AACnB,gBAAQ;AACR,YAAI,GAAG,cAAc,MAAM,OAAO,MAAM,GAAG;AACzC,gBAAM;AAAA,QACR;AAAA,MACF;AAEA,aAAO,GAAG,SAAS,OAAO;AAC1B,WAAK,GAAG,SAAS,OAAO;AAGxB,eAAS,UAAU;AACjB,eAAO,eAAe,QAAQ,MAAM;AACpC,aAAK,eAAe,SAAS,OAAO;AAEpC,eAAO,eAAe,OAAO,KAAK;AAClC,eAAO,eAAe,SAAS,OAAO;AAEtC,eAAO,eAAe,SAAS,OAAO;AACtC,aAAK,eAAe,SAAS,OAAO;AAEpC,eAAO,eAAe,OAAO,OAAO;AACpC,eAAO,eAAe,SAAS,OAAO;AAEtC,aAAK,eAAe,SAAS,OAAO;AAAA,MACtC;AAEA,aAAO,GAAG,OAAO,OAAO;AACxB,aAAO,GAAG,SAAS,OAAO;AAE1B,WAAK,GAAG,SAAS,OAAO;AAExB,WAAK,KAAK,QAAQ,MAAM;AAGxB,aAAO;AAAA,IACT;AAAA;AAAA;;;AChIO,IAAM,cAAN,MAAkB;AAAA,EACrB,cAAc;AACV,SAAK,UAAU;AACf,SAAK,WAAW;AAChB,WAAO,eAAe,MAAM,YAAY;AAAA,MACpC,OAAO;AAAA,MACP,UAAU;AAAA,IACd,CAAC;AAAA,EACL;AAAA,EACA,IAAI,UAAU;AACV,WAAO,KAAK;AAAA,EAChB;AAAA,EACA,QAAQ;AACJ,SAAK,WAAW;AAChB,QAAI,KAAK,SAAS;AACd,WAAK,QAAQ,IAAI;AACjB,WAAK,UAAU;AAAA,IACnB;AAAA,EACJ;AACJ;;;AClBO,IAAM,kBAAN,MAAsB;AAAA,EACzB,cAAc;AACV,SAAK,SAAS,IAAI,YAAY;AAAA,EAClC;AAAA,EACA,QAAQ;AACJ,SAAK,OAAO,MAAM;AAAA,EACtB;AACJ;;;ACJA,oBAA6B;;;ACJ7B,oBAAuB;;;ACAhB,IAAM,qBAAqB;AAAA,EAC9B,WAAW,MAAM;AAAA,EAAE;AACvB;;;ACDO,IAAM,sBAAsB;AAAA,EAC/B,GAAG;AAAA,EACH,SAAS;AACb;;;AFFO,IAAM,aAAa,CAAC,UAAU;AACjC,MAAI,UAAU,QAAQ,UAAU;AAC5B,WAAO;AACX,MAAI,OAAO,UAAU,UAAU;AAC3B,WAAO,qBAAO,WAAW,KAAK;AAAA,EAClC;AACA,MAAI,OAAO,MAAM,eAAe,UAAU;AACtC,WAAO,MAAM;AAAA,EACjB,WACS,OAAO,MAAM,WAAW,UAAU;AACvC,WAAO,MAAM;AAAA,EACjB,WACS,OAAO,MAAM,SAAS,UAAU;AACrC,WAAO,MAAM;AAAA,EACjB,WACS,OAAO,MAAM,SAAS,UAAU;AACrC,QAAI;AACA,aAAO,oBAAoB,UAAU,MAAM,IAAI,EAAE;AAAA,IACrD,SACO,OAAO;AACV,aAAO;AAAA,IACX;AAAA,EACJ;AACA,SAAO;AACX;;;AG1BA,IAAAM,iBAAuB;AACvB,oBAAyB;;;ACDzB,IAAAC,iBAAuB;AACvB,gBAAuB,eAAe,MAAM,UAAU,aAAa;AAC/D,MAAI,aAAa;AACjB,QAAM,gBAAgB,EAAE,QAAQ,CAAC,GAAG,QAAQ,EAAE;AAC9C,mBAAiB,SAAS,YAAY,IAAI,GAAG;AACzC,kBAAc,OAAO,KAAK,KAAK;AAC/B,kBAAc,UAAU,MAAM;AAC9B,WAAO,cAAc,SAAS,UAAU;AACpC,YAAM,YAAY,cAAc,OAAO,SAAS,IAAI,sBAAO,OAAO,cAAc,MAAM,IAAI,cAAc,OAAO,CAAC;AAChH,YAAM;AAAA,QACF;AAAA,QACA,MAAM,UAAU,SAAS,GAAG,QAAQ;AAAA,MACxC;AACA,oBAAc,SAAS,CAAC,UAAU,SAAS,QAAQ,CAAC;AACpD,oBAAc,SAAS,cAAc,OAAO,CAAC,EAAE;AAC/C,oBAAc;AAAA,IAClB;AAAA,EACJ;AACA,QAAM;AAAA,IACF;AAAA,IACA,MAAM,cAAc,OAAO,WAAW,IAAI,sBAAO,OAAO,cAAc,MAAM,IAAI,cAAc,OAAO,CAAC;AAAA,IACtG,UAAU;AAAA,EACd;AACJ;;;ACvBA,gBAAuB,mBAAmB,MAAM,UAAU;AACtD,MAAI,aAAa;AACjB,MAAI,YAAY;AAChB,MAAI,UAAU;AACd,SAAO,UAAU,KAAK,YAAY;AAC9B,UAAM;AAAA,MACF;AAAA,MACA,MAAM,KAAK,SAAS,WAAW,OAAO;AAAA,IAC1C;AACA,kBAAc;AACd,gBAAY;AACZ,cAAU,YAAY;AAAA,EAC1B;AACA,QAAM;AAAA,IACF;AAAA,IACA,MAAM,KAAK,SAAS,SAAS;AAAA,IAC7B,UAAU;AAAA,EACd;AACJ;;;AClBA,IAAAC,iBAAuB;AACvB,gBAAuB,gBAAgB,MAAM;AACzC,mBAAiB,SAAS,MAAM;AAC5B,QAAI,sBAAO,SAAS,KAAK,KAAK,iBAAiB,YAAY;AACvD,YAAM;AAAA,IACV,OACK;AACD,YAAM,sBAAO,KAAK,KAAK;AAAA,IAC3B;AAAA,EACJ;AACJ;;;ACVA,IAAAC,iBAAuB;AACvB,gBAAuB,sBAAsB,MAAM;AAC/C,QAAM,SAAS,KAAK,UAAU;AAC9B,MAAI;AACA,WAAO,MAAM;AACT,YAAM,EAAE,MAAM,MAAM,IAAI,MAAM,OAAO,KAAK;AAC1C,UAAI,MAAM;AACN;AAAA,MACJ;AACA,UAAI,sBAAO,SAAS,KAAK,KAAK,iBAAiB,YAAY;AACvD,cAAM;AAAA,MACV,OACK;AACD,cAAM,sBAAO,KAAK,KAAK;AAAA,MAC3B;AAAA,IACJ;AAAA,EACJ,SACO,GAAG;AACN,UAAM;AAAA,EACV,UACA;AACI,WAAO,YAAY;AAAA,EACvB;AACJ;;;AJjBO,IAAM,WAAW,CAAC,MAAM,aAAa;AACxC,MAAI,gBAAgB,YAAY;AAC5B,WAAO,mBAAmB,MAAM,QAAQ;AAAA,EAC5C;AACA,MAAI,gBAAgB,wBAAU;AAC1B,WAAO,eAAe,MAAM,UAAU,eAAe;AAAA,EACzD;AACA,MAAI,gBAAgB,UAAU,OAAO,SAAS,UAAU;AACpD,WAAO,mBAAmB,sBAAO,KAAK,IAAI,GAAG,QAAQ;AAAA,EACzD;AACA,MAAI,OAAO,KAAK,WAAW,YAAY;AACnC,WAAO,eAAe,KAAK,OAAO,GAAG,UAAU,qBAAqB;AAAA,EACxE;AACA,MAAI,gBAAgB,gBAAgB;AAChC,WAAO,eAAe,MAAM,UAAU,qBAAqB;AAAA,EAC/D;AACA,QAAM,IAAI,MAAM,gIAAgI;AACpJ;;;AJhBO,IAAM,UAAN,MAAM,gBAAe,2BAAa;AAAA,EAsBrC,YAAY,SAAS;AACjB,UAAM;AArBV,qCAAY;AACZ,qCAAY;AACZ,oCAAW,QAAO;AAClB,6CAAoB;AACpB,gCAAO,CAAC;AACR;AACA;AACA;AACA;AACA;AACA,+CAAsB,CAAC;AACvB;AACA,uDAA8B;AAC9B,yCAAgB,CAAC;AACjB,oDAA2B;AAC3B;AACA;AACA,uCAAc;AACd;AACA,gCAAO;AAGH,SAAK,YAAY,QAAQ,aAAa,KAAK;AAC3C,SAAK,WAAW,QAAQ,YAAY,KAAK;AACzC,SAAK,oBAAoB,QAAQ,qBAAqB,KAAK;AAC3D,SAAK,OAAO,QAAQ,QAAQ,KAAK;AACjC,SAAK,SAAS,QAAQ;AACtB,SAAK,SAAS,QAAQ;AACtB,SAAK,gBAAgB;AACrB,SAAK,aAAa,WAAW,KAAK,OAAO,IAAI;AAC7C,SAAK,qBAAqB;AAC1B,SAAK,kBAAkB,QAAQ,mBAAmB,IAAI,gBAAgB;AAAA,EAC1E;AAAA,EACA,MAAM,QAAQ;AACV,SAAK,gBAAgB,MAAM;AAAA,EAC/B;AAAA,EACA,MAAM,OAAO;AACT,QAAI,KAAK,MAAM;AACX,YAAM,IAAI,MAAM,oGAAoG;AAAA,IACxH;AACA,SAAK,OAAO;AACZ,WAAO,MAAM,QAAQ,KAAK,CAAC,KAAK,oBAAoB,GAAG,KAAK,eAAe,KAAK,gBAAgB,MAAM,CAAC,CAAC;AAAA,EAC5G;AAAA,EACA,GAAG,OAAO,UAAU;AAChB,SAAK,cAAc;AACnB,WAAO,MAAM,GAAG,OAAO,QAAQ;AAAA,EACnC;AAAA,EACA,MAAM,iBAAiB,UAAU;AAxDrC;AAyDQ,SAAK,cAAc;AACnB,UAAM,SAAS,EAAE,GAAG,KAAK,QAAQ,MAAM,SAAS,KAAK;AACrD,UAAM,eAAe,KAAK,OAAO;AACjC,UAAM,iBAAiB,aAAa;AACpC,UAAM,eAAe,0BAA0B,6BAAe,iBAAiB;AAC/E,UAAM,sBAAsB,CAAC,UAAU;AACnC,WAAK,qBAAqB,MAAM;AAChC,WAAK,aAAa,MAAM;AACxB,WAAK,iBAAiB;AAAA,QAClB,QAAQ,KAAK;AAAA,QACb,OAAO,KAAK;AAAA,QACZ,MAAM,SAAS;AAAA,QACf,KAAK,KAAK,OAAO;AAAA,QACjB,QAAQ,KAAK,OAAO;AAAA,MACxB,CAAC;AAAA,IACL;AACA,QAAI,iBAAiB,MAAM;AACvB,mBAAa,GAAG,uBAAuB,mBAAmB;AAAA,IAC9D;AACA,UAAM,WAAW,MAAM,QAAQ,IAAI,CAAC,KAAK,OAAO,KAAK,IAAI,iBAAiB,MAAM,CAAC,IAAG,kDAAc,aAAd,qCAA0B,CAAC;AAC/G,UAAM,YAAY,SAAS,CAAC;AAC5B,QAAI,WAAW,SAAS,CAAC;AACzB,QAAI,CAAC,UAAU;AACX,iBAAW,aAAa,MAAM,4BAA4B,QAAQ,kBAAkB;AAAA,QAChF,GAAG;AAAA,MACP,CAAC,CAAC;AAAA,IACN;AACA,QAAI,CAAC,UAAU;AACX,YAAM,IAAI,MAAM,gFAAgF;AAAA,IACpG;AACA,QAAI,iBAAiB,MAAM;AACvB,mBAAa,IAAI,uBAAuB,mBAAmB;AAAA,IAC/D;AACA,UAAM,cAAc,KAAK,OACpB,IAAI,MAAM,GAAG,EACb,IAAI,CAAC,YAAY,2BAA2B,OAAO,CAAC,EACpD,KAAK,GAAG;AACb,UAAM,iBAAiB,2BAA2B,KAAK,OAAO,MAAM;AACpE,UAAM,YAAY,MAAM;AACpB,YAAM,iCAAiC,SAAS,SAAS,WAAW,GAAG,cAAc,GAAG;AACxF,YAAM,iBAAiB,KAAK,OAAO,OAAO;AAC1C,YAAM,eAAe,SAAS,OAAO,IAAI,SAAS,IAAI,KAAK;AAC3D,UAAI,gBAAgB;AAChB,eAAO,GAAG,SAAS,QAAQ,KAAK,SAAS,QAAQ,GAAG,YAAY,IAAI,cAAc,IAAI,WAAW;AAAA,MACrG;AACA,UAAI,gCAAgC;AAChC,eAAO,GAAG,SAAS,QAAQ,KAAK,SAAS,QAAQ,GAAG,YAAY,IAAI,WAAW;AAAA,MACnF;AACA,aAAO,GAAG,SAAS,QAAQ,KAAK,cAAc,IAAI,SAAS,QAAQ,GAAG,YAAY,IAAI,WAAW;AAAA,IACrG,GAAG;AACH,SAAK,qBAAqB;AAAA,MACtB,GAAG;AAAA,MACH,QAAQ,KAAK,OAAO;AAAA,MACpB,KAAK,KAAK,OAAO;AAAA,MACjB;AAAA,IACJ;AACA,UAAM,YAAY,WAAW,SAAS,IAAI;AAC1C,SAAK,iBAAiB;AAAA,MAClB,QAAQ;AAAA,MACR,OAAO;AAAA,MACP,MAAM;AAAA,MACN,KAAK,KAAK,OAAO;AAAA,MACjB,QAAQ,KAAK,OAAO;AAAA,IACxB,CAAC;AAAA,EACL;AAAA,EACA,MAAM,0BAA0B;AAC5B,UAAM,6BAA6B,MAAM,KAAK,OAAO,OAAO,2BAA2B;AACvF,QAAI,CAAC,KAAK,wBAAwB;AAC9B,YAAM,sBAAsB,EAAE,GAAG,KAAK,QAAQ,MAAM,OAAU;AAC9D,UAAI,+BAA+B,kBAAkB;AACjD,4BAAoB,oBAAoB,KAAK,OAAO,qBAAqB,kBAAkB;AAAA,MAC/F;AACA,WAAK,yBAAyB,KAAK,OAC9B,KAAK,IAAI,6BAA6B,mBAAmB,CAAC,EAC1D,KAAK,CAAC,sBAAsB;AAC7B,aAAK,8BAA8B,IAAI,4BAA4B;AAAA,UAC/D,QAAQ,KAAK,OAAO;AAAA,UACpB,KAAK,KAAK,OAAO;AAAA,UACjB,UAAU,kBAAkB;AAAA,QAChC,CAAC;AACD,eAAO;AAAA,MACX,CAAC;AAAA,IACL;AACA,WAAO,KAAK;AAAA,EAChB;AAAA,EACA,MAAM,qBAAqB,YAAY;AACnC,qBAAiB,YAAY,YAAY;AACrC,UAAI,KAAK,2BAA2B,KAAK,WAAW;AAChD,cAAM,IAAI,MAAM,YAAY,KAAK,SAAS,yCAAyC,KAAK,OAAO,MAAM,SAAS,KAAK,OAAO,GAAG,GAAG;AAAA,MACpI;AACA,UAAI,KAAK,gBAAgB,OAAO,SAAS;AACrC;AAAA,MACJ;AACA,UAAI,SAAS,eAAe,KAAK,SAAS,UAAU;AAChD,eAAO,MAAM,KAAK,iBAAiB,QAAQ;AAAA,MAC/C;AACA,UAAI,CAAC,KAAK,UAAU;AAChB,cAAM,EAAE,SAAS,IAAI,MAAM,KAAK,wBAAwB;AACxD,aAAK,WAAW;AAChB,YAAI,KAAK,gBAAgB,OAAO,SAAS;AACrC;AAAA,QACJ;AAAA,MACJ;AACA,YAAM,WAAW,WAAW,SAAS,IAAI,KAAK;AAC9C,YAAM,iBAAiB,KAAK,OAAO,OAAO;AAC1C,YAAM,eAAe,0BAA0B,6BAAe,iBAAiB;AAC/E,UAAI,gBAAgB;AACpB,YAAM,sBAAsB,CAAC,OAAO,YAAY;AAC5C,cAAM,kBAAkB,OAAO,QAAQ,MAAM,YAAY,CAAC,KAAK;AAC/D,YAAI,oBAAoB,SAAS,YAAY;AACzC;AAAA,QACJ;AACA,YAAI,MAAM,SAAS,UAAU;AACzB,eAAK,sBAAsB,MAAM,SAAS;AAC1C,0BAAgB,MAAM;AAAA,QAC1B;AACA,aAAK,iBAAiB;AAAA,UAClB,QAAQ,KAAK;AAAA,UACb,OAAO,KAAK;AAAA,UACZ,MAAM,SAAS;AAAA,UACf,KAAK,KAAK,OAAO;AAAA,UACjB,QAAQ,KAAK,OAAO;AAAA,QACxB,CAAC;AAAA,MACL;AACA,UAAI,iBAAiB,MAAM;AACvB,qBAAa,GAAG,uBAAuB,mBAAmB;AAAA,MAC9D;AACA,WAAK,4BAA4B;AACjC,YAAM,aAAa,MAAM,KAAK,OAAO,KAAK,IAAI,kBAAkB;AAAA,QAC5D,GAAG,KAAK;AAAA,QACR,eAAe;AAAA,QACf,UAAU,KAAK;AAAA,QACf,MAAM,SAAS;AAAA,QACf,YAAY,SAAS;AAAA,MACzB,CAAC,CAAC;AACF,UAAI,iBAAiB,MAAM;AACvB,qBAAa,IAAI,uBAAuB,mBAAmB;AAAA,MAC/D;AACA,UAAI,KAAK,gBAAgB,OAAO,SAAS;AACrC;AAAA,MACJ;AACA,UAAI,CAAC,WAAW,MAAM;AAClB,cAAM,IAAI,MAAM,QAAQ,SAAS,UAAU,6FAA6F;AAAA,MAC5I;AACA,WAAK,cAAc,KAAK;AAAA,QACpB,YAAY,SAAS;AAAA,QACrB,MAAM,WAAW;AAAA,QACjB,GAAI,WAAW,iBAAiB,EAAE,eAAe,WAAW,cAAc;AAAA,QAC1E,GAAI,WAAW,kBAAkB,EAAE,gBAAgB,WAAW,eAAe;AAAA,QAC7E,GAAI,WAAW,gBAAgB,EAAE,cAAc,WAAW,aAAa;AAAA,QACvE,GAAI,WAAW,kBAAkB,EAAE,gBAAgB,WAAW,eAAe;AAAA,MACjF,CAAC;AACD,UAAI,iBAAiB,MAAM;AACvB,aAAK,sBAAsB;AAAA,MAC/B;AACA,WAAK,iBAAiB;AAAA,QAClB,QAAQ,KAAK;AAAA,QACb,OAAO,KAAK;AAAA,QACZ,MAAM,SAAS;AAAA,QACf,KAAK,KAAK,OAAO;AAAA,QACjB,QAAQ,KAAK,OAAO;AAAA,MACxB,CAAC;AAAA,IACL;AAAA,EACJ;AAAA,EACA,MAAM,sBAAsB;AACxB,UAAM,aAAa,SAAS,KAAK,OAAO,MAAM,KAAK,QAAQ;AAC3D,UAAM,6BAA6B,CAAC;AACpC,aAAS,QAAQ,GAAG,QAAQ,KAAK,WAAW,SAAS;AACjD,YAAM,gBAAgB,KAAK,qBAAqB,UAAU,EAAE,MAAM,CAAC,QAAQ;AACvE,mCAA2B,KAAK,GAAG;AAAA,MACvC,CAAC;AACD,WAAK,oBAAoB,KAAK,aAAa;AAAA,IAC/C;AACA,UAAM,QAAQ,IAAI,KAAK,mBAAmB;AAC1C,QAAI,2BAA2B,UAAU,GAAG;AACxC,YAAM,KAAK,oBAAoB;AAC/B,YAAM,2BAA2B,CAAC;AAAA,IACtC;AACA,QAAI,KAAK,gBAAgB,OAAO,SAAS;AACrC,YAAM,KAAK,oBAAoB;AAC/B,YAAM,OAAO,OAAO,IAAI,MAAM,iBAAiB,GAAG,EAAE,MAAM,aAAa,CAAC;AAAA,IAC5E;AACA,QAAI;AACJ,QAAI,KAAK,aAAa;AAClB,WAAK,cAAc,KAAK,CAAC,GAAG,MAAM,EAAE,aAAa,EAAE,UAAU;AAC7D,YAAM,uBAAuB;AAAA,QACzB,GAAG,KAAK;AAAA,QACR,MAAM;AAAA,QACN,UAAU,KAAK;AAAA,QACf,iBAAiB;AAAA,UACb,OAAO,KAAK;AAAA,QAChB;AAAA,MACJ;AACA,eAAS,MAAM,KAAK,OAAO,KAAK,IAAI,+BAA+B,oBAAoB,CAAC;AACxF,UAAI,QAAO,iCAAQ,cAAa,YAAY,OAAO,SAAS,SAAS,KAAK,GAAG;AACzE,eAAO,WAAW,OAAO,SAAS,QAAQ,QAAQ,GAAG;AAAA,MACzD;AAAA,IACJ,OACK;AACD,eAAS,KAAK;AAAA,IAClB;AACA,SAAK,8BAA8B;AACnC,QAAI,KAAK,KAAK,QAAQ;AAClB,YAAM,KAAK,OAAO,KAAK,IAAI,wBAAwB;AAAA,QAC/C,GAAG,KAAK;AAAA,QACR,SAAS;AAAA,UACL,QAAQ,KAAK;AAAA,QACjB;AAAA,MACJ,CAAC,CAAC;AAAA,IACN;AACA,WAAO;AAAA,EACX;AAAA,EACA,MAAM,sBAAsB;AACxB,QAAI,KAAK,YAAY,CAAC,KAAK,qBAAqB,SAAS,KAAK,6BAA6B;AACvF,YAAM,KAAK,OAAO,KAAK,KAAK,2BAA2B;AACvD,WAAK,8BAA8B;AAAA,IACvC;AAAA,EACJ;AAAA,EACA,iBAAiB,UAAU;AACvB,QAAI,KAAK,aAAa;AAClB,WAAK,KAAK,KAAK,aAAa,QAAQ;AAAA,IACxC;AAAA,EACJ;AAAA,EACA,MAAM,eAAe,aAAa;AAC9B,WAAO,IAAI,QAAQ,CAAC,SAAS,WAAW;AACpC,kBAAY,UAAU,MAAM;AACxB,cAAM,aAAa,IAAI,MAAM,iBAAiB;AAC9C,mBAAW,OAAO;AAClB,eAAO,UAAU;AAAA,MACrB;AAAA,IACJ,CAAC;AAAA,EACL;AAAA,EACA,kBAAkB;AACd,QAAI,CAAC,KAAK,QAAQ;AACd,YAAM,IAAI,MAAM,4DAA4D;AAAA,IAChF;AACA,QAAI,CAAC,KAAK,QAAQ;AACd,YAAM,IAAI,MAAM,8DAA8D;AAAA,IAClF;AACA,QAAI,KAAK,WAAW,QAAO,eAAe;AACtC,YAAM,IAAI,MAAM,kDAAkD,KAAK,QAAQ,+CAA+C,QAAO,aAAa,SAAS;AAAA,IAC/J;AACA,QAAI,KAAK,YAAY,GAAG;AACpB,YAAM,IAAI,MAAM,qDAAqD;AAAA,IACzE;AAAA,EACJ;AACJ;AAvSI,cADS,SACF,iBAAgB,OAAO,OAAO;AADlC,IAAM,SAAN;", "names": ["ReflectApply", "ReflectOwnKeys", "NumberIsNaN", "EventEmitter", "once", "<PERSON><PERSON><PERSON>", "err", "self", "NodeError", "<PERSON><PERSON><PERSON>", "realHasInstance", "keys", "Readable", "self", "onlegacyfinish", "onfinish", "onend", "onerror", "onclose", "onrequest", "createReadableStreamAsyncIterator", "Readable", "EElistenerCount", "<PERSON><PERSON><PERSON>", "debug", "self", "n", "import_buffer", "import_buffer", "import_buffer", "import_buffer"]}