import { ApiResponse } from "~/types/api/Api";
import { API_BASE_URL, apiRequest } from "~/utils/api";
import {
  InventoryResponse,
  SellerCategoriesResponse,
} from "~/types/api/businessConsoleService/inventoryItems";

export async function getInventoryItems(
  request?: Request,
  queryParams?: string,
  sellerId?: number
): Promise<ApiResponse<InventoryResponse>> {
  const url = new URL(`${API_BASE_URL}/inventory/item_detail`);
  if (queryParams) {
    url.search = queryParams;
  }
  if (sellerId) {
    url.searchParams.append('sellerId', sellerId.toString());
  }

  const response = await apiRequest<InventoryResponse>(
    url.toString(),
    "GET",
    undefined,
    {},
    true,
    request
  );

  if (response) {
    return response;
  } else {
    throw new Error("Failed to fetch inventory items");
  }
}

export async function getSellerCategories(
  request?: Request,
  queryParams?: string,
  sellerId?: number
): Promise<ApiResponse<SellerCategoriesResponse>> {
  const url = new URL(`${API_BASE_URL}/inventory/seller_categories`);
  if (queryParams) {
    url.search = queryParams;
  }
  if (sellerId) {
    url.searchParams.append('sellerId', sellerId.toString());
  }
  
  const response = await apiRequest<SellerCategoriesResponse>(
    url.toString(),
    "GET",
    undefined,
    {},
    true,
    request
  );
  if (response) {
    return response;
  } else {
    throw new Error("Failed to fetch seller categories");
  }
}

export async function updateInventoryItemStatus(
  request: Request,
  body: any
) {
  const response = await apiRequest(
    `${API_BASE_URL}/inventory/inv_item_status`,
    "PUT",
    body,
    {},
    true,
    request
  );

  if (response) {
    return response;
  } else {
    throw new Error("Failed to update item status");
  }
}
