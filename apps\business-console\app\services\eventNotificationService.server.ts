// app/services/eventNotificationService.server.ts
import { API_BASE_URL, apiRequest } from "~/utils/api";
import { ApiResponse } from "~/types/api/Api";

// Event types
export type EventType = "ORDER_DELAYED" | "REFUND_PROCESSED";

// Base payload structure
export interface BaseEventPayload {
  orderGroupId: number;
}

// Order delayed event payload
export interface OrderDelayedPayload extends BaseEventPayload {
  reason: string;
  newEta: string;
}

// Refund processed event payload
export interface RefundProcessedPayload extends BaseEventPayload {
  refundAmount: string;
  refundTo: string;
  tat: string;
}

// Union type for all possible payloads
export type EventPayload = OrderDelayedPayload | RefundProcessedPayload;

// Main request payload
export interface SendEventNotificationRequest {
  eventType: EventType;
  customerMobileNumber: string | null;
  customerName: string | null;
  sellerId: number | null;
  payload: EventPayload;
}

// Response type
export interface SendEventNotificationResponse {
  success: boolean;
  message: string;
  messageId?: string;
}

/**
 * Send event notification to customer (Server-side only)
 */
export async function sendEventNotification(
  request: SendEventNotificationRequest,
  serverRequest: Request
): Promise<ApiResponse<SendEventNotificationResponse>> {
  const url = `${API_BASE_URL}/bc/mnetadmin/send-template-message`;
  
  return apiRequest<SendEventNotificationResponse>(
    url,
    "POST",
    request,
    {},
    true,
    serverRequest
  );
}
